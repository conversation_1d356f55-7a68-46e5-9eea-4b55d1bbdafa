#!/bin/bash
# error handling
set -e

# Ensure NVM is loaded
source ~/.nvm/nvm.sh

# Make sure Node.js version is set (optional if you need a specific version)
nvm use 18  # or the version you need

# Install pnpm if not already installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm not found. Installing pnpm..."
    npm install -g pnpm
fi

echo "Navigating to app directory"
cd /var/www/zenith-funnel

echo "Installing dependencies"
pnpm install  # Install dependencies using pnpm

# Add DB here 
# pnpm run db

# Install pnpm if not already installed
if ! command -v pm2 &> /dev/null
then
    echo "pm2 not found. Installing pm2..."
    npm install pm2 -g
fi
cd /var/www/zenith-funnel

# Restart PM2 process
pm2 stop zenith-funnel-app
pm2 delete zenith-funnel-app
pm2 start pm2.config.json

echo "Deployment complete."
