import * as React from "react";
import { useEffect } from "react";
import { Box, useTheme, useMediaQuery, Typography, Button } from '@mui/material';
import '../styles/harvest/main.css';

const styles= {
    zenithButton: {
        color: '#ffffff',
        backgroundColor: '#007F00',
        padding: '10px',
        fontSize: '35px',
        fontWeight: '300',
    },
    headingOne:{
        lineHeight: '1.3em', 
        fontSize:'80px', 
        fontWeight:'bold',
        color: '#FD6440',
    },
    headingTwo:{
        lineHeight: '3em', 
        fontSize:'30px', 
        fontWeight:'bold',
        color: '#FFFFFF',
    },
    headingThree:{
        lineHeight: '1em', 
        fontSize:'25px', 
        fontWeight:'bold',
        color: '#FD6440',
    }
  };

function Thankyou() {

    useEffect(() => {
        document.title = 'Are you lost?';
    }, []);

    return (
        <div>
            <center>
                <div style={{ width: '60vw', height:'100vh', display:'flex', flexDirection: 'column', flexWrap: 'nowrap', alignContent: 'center', justifyContent: 'space-evenly' }}>
                    <div>
                        <Typography sx={{'textTransform':'uppercase'}} style={styles.headingOne} variant="body1">Oops!</Typography>
                        <Typography sx={{'textTransform':'uppercase'}} style={styles.headingTwo} variant="body1">Are you lost?</Typography>
                        <Typography sx={{'textTransform':'uppercase'}} style={styles.headingThree} variant="body1">Try clicking one of these.</Typography>
                    </div>
                    <div style={{display: 'flex', textAlign: 'center', flexDirection:'column'}}>
                        <a href="https://harvest.delivery">
                            <img src='/harvest/harvest_logo.svg' width={'225px'} style={{ padding:"20px 0" }} />
                        </a>
                        <a href="https://zenith.clinic">
                            <span style={styles.zenithButton}>
                                    <span style={{fontWeight: '600', textAlign: 'center'}}>Zenith</span>Clinics
                            </span>
                        </a>
                    </div>
                </div>
            </center>
        </div>
    );
}

export default Thankyou;