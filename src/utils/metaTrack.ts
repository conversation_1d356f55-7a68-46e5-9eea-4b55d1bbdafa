import CryptoJS from 'crypto-js';
import axios, { AxiosError } from 'axios';

export function metaConversionApi(event_name: any, event_source_url: any, event_id: any, user_email: any, user_phone: any, first_name: any, last_name: any) {
  
  try {
    const payload = {
      data: [
        {
          event_name: event_name,
          event_time: Math.floor(Date.now() / 1000),
          event_source_url: event_source_url,
          action_source: 'website',
          event_id: event_id,
          user_data: {
            em: hashSHA256(user_email),
            ph: hashSHA256(user_phone),
            fn: hashSHA256(first_name),
            ln: hashSHA256(last_name),
            client_user_agent: window.navigator.userAgent,
          }
        },
      ],
    };

    axios.post(
      `https://graph.facebook.com/v18.0/${import.meta.env.VITE_META_PIXEL_ID_ZENITH}/events?access_token=${import.meta.env.VITE_META_ACCESS_TOKEN}`,
      payload
    );

    // Removed Dual Brand Dataset
    // axios.post(
    //   `https://graph.facebook.com/v18.0/${import.meta.env.VITE_META_PIXEL_ID_DUAL_BRAND}/events?access_token=${import.meta.env.VITE_META_ACCESS_TOKEN}`,
    //   payload
    // );

  } catch (err: any) {
    console.error(err.response?.data || err.message);
  }
};

function hashSHA256(value: string) {
  return CryptoJS.SHA256(value.trim().toLowerCase()).toString();
}
