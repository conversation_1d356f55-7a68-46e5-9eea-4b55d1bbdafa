/**
 * Utility functions for formatting dates in Australian format (DD/MM/YYYY) and timezone conversions
 */

/**
 * Detects the client's timezone
 * @returns The client's timezone string (e.g., 'America/New_York') or 'Australia/Sydney' as fallback
 */
export function detectClientTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.warn('Error detecting client timezone, falling back to Australia/Sydney:', error);
    return 'Australia/Sydney';
  }
}

/**
 * Helper function to create a date in a specific timezone
 * @param dateString - Date string (YYYY-MM-DD)
 * @param timeString - Time string (HH:MM)
 * @param timezone - Target timezone
 * @returns Date object
 */
function createDateInTimezone(dateString: string, timeString: string, timezone: string): Date {
  // Create a date string in ISO format
  const isoString = `${dateString}T${timeString}:00`;

  // Create a temporary date
  const tempDate = new Date(isoString);

  // Get what this date would be in the target timezone
  const targetTime = new Intl.DateTimeFormat('en-CA', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).format(tempDate);

  // Parse the target time and calculate offset
  const targetDate = new Date(targetTime.replace(/(\d{4})-(\d{2})-(\d{2}), (\d{2}):(\d{2}):(\d{2})/, '$1-$2-$3T$4:$5:$6'));
  const offset = tempDate.getTime() - targetDate.getTime();

  // Apply the offset to get the correct time
  return new Date(tempDate.getTime() + offset);
}

/**
 * Converts a date and time from Sydney timezone to the client's timezone
 * @param dateString - The date string (e.g., '2025-07-01')
 * @param timeSlot - The time slot string (e.g., '00:00 - 00:30')
 * @param clientTimezone - The client's timezone (optional, will auto-detect if not provided)
 * @returns Object with converted date and time information
 */
export function convertSydneyToClientTimezone(
  dateString: string,
  timeSlot: string,
  clientTimezone?: string
): {
  clientDate: Date;
  clientDateString: string;
  clientTimeSlot: string;
  originalSydneyTime: string;
  timezoneLabel: string;
  isSameTimezone: boolean;
} {
  const targetTimezone = clientTimezone || detectClientTimezone();
  const sydneyTimezone = 'Australia/Sydney';
  const isSameTimezone = targetTimezone === sydneyTimezone;

  console.log('Timezone conversion:', {
    dateString,
    timeSlot,
    targetTimezone,
    sydneyTimezone,
    isSameTimezone
  });

  try {
    // Parse the time slot (handle formats like "00:00 - 00:30")
    const timeMatch = timeSlot.match(/^(\d{1,2}):(\d{2})\s*-\s*(\d{1,2}):(\d{2})$/);
    if (!timeMatch) {
      throw new Error(`Invalid time slot format: ${timeSlot}`);
    }

    const [, startHour, startMinute, endHour, endMinute] = timeMatch;
    console.log('Parsed time:', { startHour, startMinute, endHour, endMinute });

    // Create Date objects representing the Sydney times
    // We'll use a simple approach: assume the input times are in Sydney timezone
    const sydneyStartDate = new Date(`${dateString}T${startHour.padStart(2, '0')}:${startMinute.padStart(2, '0')}:00`);
    const sydneyEndDate = new Date(`${dateString}T${endHour.padStart(2, '0')}:${endMinute.padStart(2, '0')}:00`);

    // If we're in the same timezone, just use the dates as-is
    if (isSameTimezone) {
      const clientDateString = sydneyStartDate.toLocaleDateString('en-AU', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });

      return {
        clientDate: sydneyStartDate,
        clientDateString,
        clientTimeSlot: timeSlot, // Keep original format
        originalSydneyTime: `${timeSlot} AEDT/AEST`,
        timezoneLabel: 'AEDT/AEST',
        isSameTimezone: true
      };
    }

    // For different timezones, we need to convert
    // The tricky part is that the input represents Sydney local time, but Date constructor interprets it as local time
    // We need to adjust for this difference

    // Get the current timezone offset difference between Sydney and client
    const now = new Date();
    const sydneyTime = new Intl.DateTimeFormat('en-CA', {
      timeZone: 'Australia/Sydney',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(now);

    const clientTime = new Intl.DateTimeFormat('en-CA', {
      timeZone: targetTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(now);

    const sydneyDate = new Date(sydneyTime.replace(/(\d{4})-(\d{2})-(\d{2}), (\d{2}):(\d{2}):(\d{2})/, '$1-$2-$3T$4:$5:$6'));
    const clientDate = new Date(clientTime.replace(/(\d{4})-(\d{2})-(\d{2}), (\d{2}):(\d{2}):(\d{2})/, '$1-$2-$3T$4:$5:$6'));
    const offsetMs = sydneyDate.getTime() - clientDate.getTime();

    // Apply the offset to our booking times
    const adjustedStartDate = new Date(sydneyStartDate.getTime() - offsetMs);
    const adjustedEndDate = new Date(sydneyEndDate.getTime() - offsetMs);

    // Format for display
    const clientDateString = adjustedStartDate.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    const clientStartTime = adjustedStartDate.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const clientEndTime = adjustedEndDate.toLocaleTimeString('en-AU', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const clientTimeSlot = `${clientStartTime} - ${clientEndTime}`;

    // Get timezone abbreviation for display
    const timezoneLabel = new Intl.DateTimeFormat('en', {
      timeZone: targetTimezone,
      timeZoneName: 'short'
    }).formatToParts(new Date()).find(part => part.type === 'timeZoneName')?.value || targetTimezone;

    return {
      clientDate: adjustedStartDate,
      clientDateString,
      clientTimeSlot,
      originalSydneyTime: `${timeSlot} AEDT/AEST`,
      timezoneLabel,
      isSameTimezone: false
    };

  } catch (error) {
    console.warn('Error converting timezone, falling back to original values:', error);

    // Fallback to original values with Sydney timezone label
    const fallbackDate = new Date(dateString);
    const fallbackDateString = fallbackDate.toLocaleDateString('en-AU', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    return {
      clientDate: fallbackDate,
      clientDateString: fallbackDateString,
      clientTimeSlot: timeSlot,
      originalSydneyTime: `${timeSlot} AEDT/AEST`,
      timezoneLabel: 'AEDT/AEST',
      isSameTimezone: true
    };
  }
}

/**
 * Formats a date string to Australian format (DD/MM/YYYY)
 * @param dateString - The date string to format (can be various formats)
 * @returns Formatted date string in DD/MM/YYYY format or original string if invalid
 */
export function formatToAustralianDate(dateString: string | null | undefined): string {
  if (!dateString || dateString === "Not scheduled yet" || dateString === "N/A - initial order not placed") {
    return dateString || "";
  }

  try {
    // Try to parse the date string
    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      // If it's already in DD/MM/YYYY format, return as is
      if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateString)) {
        return dateString;
      }
      // If it's in DD-MM-YYYY format, convert to DD/MM/YYYY
      if (/^\d{1,2}-\d{1,2}-\d{4}$/.test(dateString)) {
        return dateString.replace(/-/g, '/');
      }
      // Return original string if we can't parse it
      return dateString;
    }

    // Format to Australian format (DD/MM/YYYY)
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    console.warn('Error formatting date:', dateString, error);
    return dateString;
  }
}

/**
 * Formats a date to Australian format with timezone consideration
 * @param dateString - The date string to format
 * @param timezone - The timezone to use (default: 'Australia/Sydney')
 * @returns Formatted date string in DD/MM/YYYY format
 */
export function formatToAustralianDateWithTimezone(
  dateString: string | null | undefined, 
  timezone: string = 'Australia/Sydney'
): string {
  if (!dateString || dateString === "Not scheduled yet" || dateString === "N/A - initial order not placed") {
    return dateString || "";
  }

  try {
    const date = new Date(dateString);
    
    if (isNaN(date.getTime())) {
      return formatToAustralianDate(dateString);
    }

    // Format using Australian timezone
    const formatter = new Intl.DateTimeFormat('en-AU', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      timeZone: timezone
    });

    return formatter.format(date);
  } catch (error) {
    console.warn('Error formatting date with timezone:', dateString, error);
    return formatToAustralianDate(dateString);
  }
}
