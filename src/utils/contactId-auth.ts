import axiosInstance from '../services/axios';
import { AuthUser } from '../types';
import { authenticateByEmail } from '../services/email-auth';

export interface ContactIdAuthResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
}

/**
 * Authenticates a user based on their contactId by fetching their data from Zoho
 * and setting up proper authentication with tokens via email-only login
 */
export const authenticateByContactId = async (contactId: string): Promise<ContactIdAuthResult> => {
  try {
    // Step 0: Validate contactId format
    if (!contactId || typeof contactId !== 'string') {
      return {
        success: false,
        error: "Invalid contact ID format."
      };
    }

    // Step 1: Get email from contact ID (use contactId as-is, no 'p' prefix needed for API)
    const emailResponse = await axiosInstance.get(`/zoho/v1.0/contacts/${contactId}`);

    if (!emailResponse.data?.success || !emailResponse.data?.email) {
      return {
        success: false,
        error: "Could not retrieve email for this contact. Please check the contact ID or contact support."
      };
    }

    const email = emailResponse.data.email;

    // Step 2: Use email-only authentication to get proper tokens and user data
    const authResult = await authenticateByEmail(email);

    if (!authResult.success) {
      return {
        success: false,
        error: authResult.error || "Authentication failed. Please contact support."
      };
    }

    return {
      success: true,
      user: authResult.user
    };

  } catch (error) {
    console.error("Error authenticating by contactId:", error);

    // Clear any partial authentication state
    localStorage.removeItem("zenith_auth_user");
    localStorage.removeItem("zenith_authenticated");

    return {
      success: false,
      error: "Failed to authenticate. Please try again or contact support."
    };
  }
};

/**
 * Extracts contactId from URL path segments
 */
export const extractContactIdFromPath = (): string | null => {
  const pathSegments = window.location.pathname.split('/');
  const contactIdFromPath = pathSegments[pathSegments.length - 1];

  // Check if the last segment looks like a contactId (not empty and not 'profile')
  if (!contactIdFromPath || contactIdFromPath === 'profile') {
    return null;
  }

  // Basic validation - contactId should be alphanumeric and reasonable length
  if (contactIdFromPath.length < 3 || contactIdFromPath.length > 50) {
    return null;
  }

  // Check if it contains only valid characters (letters, numbers, and common ID characters)
  if (!/^[a-zA-Z0-9_-]+$/.test(contactIdFromPath)) {
    return null;
  }

  return contactIdFromPath;
};

/**
 * Checks if the current URL contains a contactId parameter
 */
export const hasContactIdInUrl = (): boolean => {
  return extractContactIdFromPath() !== null;
};
