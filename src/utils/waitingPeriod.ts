/**
 * Utility functions for calculating and managing waiting periods and business day calculations
 */

export interface WaitingPeriodStatus {
  isInWaitingPeriod: boolean;
  waitingPeriodEndDate: Date | null;
  remainingTime: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

export interface ExtendTPWaitingPeriodStatus {
  isInWaitingPeriod: boolean;
  extensionEligibleDate: Date | null;
  remainingTime: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

/**
 * Calculates the waiting period end date (1 calendar month from treatment plan start date)
 * @param treatmentPlanStartDate - The start date of the treatment plan (YYYY-MM-DD format)
 * @returns Date object representing when the waiting period ends
 */
export function calculateWaitingPeriodEndDate(treatmentPlanStartDate: string): Date | null {
  if (!treatmentPlanStartDate) {
    return null;
  }

  try {
    const startDate = new Date(treatmentPlanStartDate);
    
    // Check if the date is valid
    if (isNaN(startDate.getTime())) {
      console.warn('Invalid treatment plan start date:', treatmentPlanStartDate);
      return null;
    }

    // Add 1 calendar month to the start date
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + 1);
    
    return endDate;
  } catch (error) {
    console.error('Error calculating waiting period end date:', error);
    return null;
  }
}

/**
 * Checks if the user is currently in the waiting period
 * @param treatmentPlanStartDate - The start date of the treatment plan (YYYY-MM-DD format)
 * @returns Boolean indicating if the user is still in the waiting period
 */
export function isInWaitingPeriod(treatmentPlanStartDate: string): boolean {
  const endDate = calculateWaitingPeriodEndDate(treatmentPlanStartDate);
  
  if (!endDate) {
    return false;
  }

  const now = new Date();
  return now < endDate;
}

/**
 * Calculates the remaining time in the waiting period
 * @param treatmentPlanStartDate - The start date of the treatment plan (YYYY-MM-DD format)
 * @returns Object with remaining days, hours, and minutes, or null if not in waiting period
 */
export function calculateRemainingTime(treatmentPlanStartDate: string): {
  days: number;
  hours: number;
  minutes: number;
} | null {
  const endDate = calculateWaitingPeriodEndDate(treatmentPlanStartDate);
  
  if (!endDate) {
    return null;
  }

  const now = new Date();
  
  // If waiting period has ended, return null
  if (now >= endDate) {
    return null;
  }

  // Calculate the difference in milliseconds
  const diffMs = endDate.getTime() - now.getTime();
  
  // Convert to days, hours, and minutes
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  return {
    days,
    hours,
    minutes
  };
}

/**
 * Gets comprehensive waiting period status
 * @param treatmentPlanStartDate - The start date of the treatment plan (YYYY-MM-DD format)
 * @returns Complete waiting period status object
 */
export function getWaitingPeriodStatus(treatmentPlanStartDate: string): WaitingPeriodStatus {
  const waitingPeriodEndDate = calculateWaitingPeriodEndDate(treatmentPlanStartDate);
  const isInPeriod = isInWaitingPeriod(treatmentPlanStartDate);
  const remainingTime = calculateRemainingTime(treatmentPlanStartDate);

  return {
    isInWaitingPeriod: isInPeriod,
    waitingPeriodEndDate,
    remainingTime
  };
}

/**
 * Formats remaining time for display
 * @param remainingTime - Object with days, hours, minutes
 * @returns Formatted string for display
 */
export function formatRemainingTime(remainingTime: { days: number; hours: number; minutes: number } | null): string {
  if (!remainingTime) {
    return '';
  }

  const { days, hours, minutes } = remainingTime;

  // Handle singular/plural forms
  const dayText = days === 1 ? 'Day' : 'Days';
  const hourText = hours === 1 ? 'Hour' : 'Hours';
  const minuteText = minutes === 1 ? 'Minute' : 'Minutes';

  return `${days} ${dayText} ${hours.toString().padStart(2, '0')} ${hourText} ${minutes.toString().padStart(2, '0')} ${minuteText}`;
}

// ===== EXTEND TREATMENT PLAN WAITING PERIOD FUNCTIONS =====

/**
 * Calculates when users become eligible to request treatment plan extension (14 days before plan expires)
 * @param treatmentPlanEndDate - The end date of the treatment plan (YYYY-MM-DD format)
 * @returns Date object representing when extension requests become available
 */
export function calculateExtensionEligibleDate(treatmentPlanEndDate: string): Date | null {
  if (!treatmentPlanEndDate) {
    return null;
  }

  try {
    const endDate = new Date(treatmentPlanEndDate);

    // Check if the date is valid
    if (isNaN(endDate.getTime())) {
      console.warn('Invalid treatment plan end date:', treatmentPlanEndDate);
      return null;
    }

    // Subtract 14 days from the end date to get eligible date
    const eligibleDate = new Date(endDate);
    eligibleDate.setDate(eligibleDate.getDate() - 14);

    return eligibleDate;
  } catch (error) {
    console.error('Error calculating extension eligible date:', error);
    return null;
  }
}

/**
 * Checks if the user is currently in the waiting period for treatment plan extension
 * @param treatmentPlanEndDate - The end date of the treatment plan (YYYY-MM-DD format)
 * @returns Boolean indicating if the user is still in the waiting period (cannot request extension yet)
 */
export function isInExtendTPWaitingPeriod(treatmentPlanEndDate: string): boolean {
  const eligibleDate = calculateExtensionEligibleDate(treatmentPlanEndDate);

  if (!eligibleDate) {
    return false;
  }

  const now = new Date();
  return now < eligibleDate;
}

/**
 * Calculates the remaining time until extension requests become available
 * @param treatmentPlanEndDate - The end date of the treatment plan (YYYY-MM-DD format)
 * @returns Object with remaining days, hours, and minutes, or null if extension is available
 */
export function calculateExtendTPRemainingTime(treatmentPlanEndDate: string): {
  days: number;
  hours: number;
  minutes: number;
} | null {
  const eligibleDate = calculateExtensionEligibleDate(treatmentPlanEndDate);

  if (!eligibleDate) {
    return null;
  }

  const now = new Date();

  // If extension is already available, return null
  if (now >= eligibleDate) {
    return null;
  }

  // Calculate the difference in milliseconds
  const diffMs = eligibleDate.getTime() - now.getTime();

  // Convert to days, hours, and minutes
  const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  return {
    days,
    hours,
    minutes
  };
}

/**
 * Gets comprehensive waiting period status for treatment plan extension
 * @param treatmentPlanEndDate - The end date of the treatment plan (YYYY-MM-DD format)
 * @returns Complete waiting period status object for extension
 */
export function getExtendTPWaitingPeriodStatus(treatmentPlanEndDate: string): ExtendTPWaitingPeriodStatus {
  const extensionEligibleDate = calculateExtensionEligibleDate(treatmentPlanEndDate);
  const isInPeriod = isInExtendTPWaitingPeriod(treatmentPlanEndDate);
  const remainingTime = calculateExtendTPRemainingTime(treatmentPlanEndDate);

  return {
    isInWaitingPeriod: isInPeriod,
    extensionEligibleDate,
    remainingTime
  };
}

// ===== BUSINESS DAY CALCULATION FUNCTIONS =====

/**
 * Checks if a given date is a business day (Monday-Friday)
 * @param date - The date to check
 * @returns Boolean indicating if the date is a business day
 */
export function isBusinessDay(date: Date): boolean {
  const dayOfWeek = date.getDay();
  // Sunday = 0, Monday = 1, ..., Saturday = 6
  return dayOfWeek >= 1 && dayOfWeek <= 5;
}

/**
 * Adds business days to a given date
 * @param startDate - The starting date
 * @param businessDays - Number of business days to add
 * @returns New date with business days added
 */
export function addBusinessDays(startDate: Date, businessDays: number): Date {
  const result = new Date(startDate);
  let daysAdded = 0;

  while (daysAdded < businessDays) {
    result.setDate(result.getDate() + 1);
    if (isBusinessDay(result)) {
      daysAdded++;
    }
  }

  return result;
}

/**
 * Calculates the number of business days between two dates
 * @param startDate - The start date
 * @param endDate - The end date
 * @returns Number of business days between the dates
 */
export function getBusinessDaysBetween(startDate: Date, endDate: Date): number {
  if (startDate >= endDate) {
    return 0;
  }

  let businessDays = 0;
  const current = new Date(startDate);

  while (current < endDate) {
    current.setDate(current.getDate() + 1);
    if (isBusinessDay(current)) {
      businessDays++;
    }
  }

  return businessDays;
}

/**
 * Determines if rejection status should be delayed (shown as pending instead)
 * Non-eligible users should see "Pending Doctor's Review" for one full business day
 * before seeing rejection status
 * @param submissionDate - When the questionnaire was submitted (ISO string or Date)
 * @param currentDate - Current date (defaults to now)
 * @returns Boolean indicating if rejection should be delayed (true = show pending, false = show rejection)
 */
export function shouldDelayRejectionStatus(
  submissionDate: string | Date,
  currentDate: Date = new Date()
): boolean {
  try {
    const submittedAt = typeof submissionDate === 'string' ? new Date(submissionDate) : submissionDate;

    // Check if submission date is valid
    if (isNaN(submittedAt.getTime())) {
      console.warn('Invalid submission date:', submissionDate);
      return false; // If we can't parse the date, don't delay rejection
    }

    // Calculate one business day after submission
    const oneBusinessDayLater = addBusinessDays(submittedAt, 1);

    // If current time is before one business day has passed, delay rejection
    return currentDate < oneBusinessDayLater;
  } catch (error) {
    console.error('Error calculating rejection delay:', error);
    return false; // On error, don't delay rejection
  }
}

/**
 * Gets the date when rejection status can be shown (one business day after submission)
 * @param submissionDate - When the questionnaire was submitted (ISO string or Date)
 * @returns Date when rejection can be shown, or null if invalid submission date
 */
export function getRejectionEligibleDate(submissionDate: string | Date): Date | null {
  try {
    const submittedAt = typeof submissionDate === 'string' ? new Date(submissionDate) : submissionDate;

    if (isNaN(submittedAt.getTime())) {
      return null;
    }

    return addBusinessDays(submittedAt, 1);
  } catch (error) {
    console.error('Error calculating rejection eligible date:', error);
    return null;
  }
}
