// utils/getStoredUTM.ts
export const getStoredUTM = (
  ttl = 3 * 24 * 60 * 60 * 1000 // default 3 days
): Record<string, string> => {
  try {
    const raw = localStorage.getItem('utm_params');
    if (!raw) return {};
    const data = JSON.parse(raw);
    if (Date.now() - data.timestamp > ttl) {
      localStorage.removeItem('utm_params');
      return {};
    }
    const { timestamp, ...utm } = data;
    return utm;
  } catch {
    return {};
  }
};
