import { TreatmentPlan } from '../types';
import { 
  QUANTITY_LEVELS, 
  QuantityLevel, 
  ThcStrength, 
  PatientQuantityStatus,
  QuantityIncreaseFormData,
  QUANTITY_INCREASE_SCORING
} from '../types/quantityIncrease';
import { isTreatmentPlanActive } from './treatmentPlanValidation';

/**
 * Gets the current quantity for a specific THC strength
 */
export function getCurrentQuantity(treatmentPlan: TreatmentPlan | null, strength: ThcStrength): number {
  if (!treatmentPlan?.totalAllowance) return 0;
  
  const quantityStr = strength === '22' ? treatmentPlan.totalAllowance.thc22 : treatmentPlan.totalAllowance.thc29;
  const quantity = parseInt(quantityStr || '0');
  return isNaN(quantity) ? 0 : quantity;
}

/**
 * Gets the next available quantity level for a given current quantity
 */
export function getNextQuantityLevel(currentQuantity: number): number | null {
  const currentIndex = QUANTITY_LEVELS.indexOf(currentQuantity as QuantityLevel);
  if (currentIndex === -1 || currentIndex === QUANTITY_LEVELS.length - 1) {
    return null; // Invalid current quantity or already at max
  }
  return QUANTITY_LEVELS[currentIndex + 1];
}

/**
 * Gets all available quantity levels above the current quantity
 */
export function getAvailableQuantityLevels(currentQuantity: number): number[] {
  const currentIndex = QUANTITY_LEVELS.indexOf(currentQuantity as QuantityLevel);
  if (currentIndex === -1 || currentIndex === QUANTITY_LEVELS.length - 1) {
    return []; // Invalid current quantity or already at max
  }
  return QUANTITY_LEVELS.slice(currentIndex + 1);
}

/**
 * Checks if a patient can increase quantity for a specific strength
 */
export function canIncreaseQuantity(treatmentPlan: TreatmentPlan | null, strength: ThcStrength): boolean {
  const currentQuantity = getCurrentQuantity(treatmentPlan, strength);
  
  // Must have a current quantity > 0 and not be at maximum
  return currentQuantity > 0 && currentQuantity < Math.max(...QUANTITY_LEVELS);
}

/**
 * Gets comprehensive quantity status for a patient
 */
export function getPatientQuantityStatus(treatmentPlan: TreatmentPlan | null): PatientQuantityStatus {
  const thc22Current = getCurrentQuantity(treatmentPlan, '22');
  const thc29Current = getCurrentQuantity(treatmentPlan, '29');

  const thc22CanIncrease = canIncreaseQuantity(treatmentPlan, '22');
  const thc29CanIncrease = canIncreaseQuantity(treatmentPlan, '29');

  return {
    thc22: {
      current: thc22Current,
      canIncrease: thc22CanIncrease,
      nextLevel: thc22CanIncrease ? getNextQuantityLevel(thc22Current) : null,
      availableLevels: thc22CanIncrease ? getAvailableQuantityLevels(thc22Current) : [],
      maxLevel: Math.max(...QUANTITY_LEVELS)
    },
    thc29: {
      current: thc29Current,
      canIncrease: thc29CanIncrease,
      nextLevel: thc29CanIncrease ? getNextQuantityLevel(thc29Current) : null,
      availableLevels: thc29CanIncrease ? getAvailableQuantityLevels(thc29Current) : [],
      maxLevel: Math.max(...QUANTITY_LEVELS)
    },
    hasAnyIncreaseOptions: thc22CanIncrease || thc29CanIncrease
  };
}

/**
 * Checks if a patient is eligible for quantity increase questionnaire
 */
export function isEligibleForQuantityIncrease(treatmentPlan: TreatmentPlan | null): boolean {
  if (!isTreatmentPlanActive(treatmentPlan)) {
    return false;
  }
  
  const quantityStatus = getPatientQuantityStatus(treatmentPlan);
  return quantityStatus.hasAnyIncreaseOptions;
}

/**
 * Gets available strength options for quantity increase
 */
export function getAvailableStrengthOptions(treatmentPlan: TreatmentPlan | null): Array<{value: ThcStrength, label: string, current: number, availableLevels: number[]}> {
  const quantityStatus = getPatientQuantityStatus(treatmentPlan);
  const options: Array<{value: ThcStrength, label: string, current: number, availableLevels: number[]}> = [];

  if (quantityStatus.thc22.canIncrease && quantityStatus.thc22.availableLevels.length > 0) {
    const maxLevel = Math.max(...quantityStatus.thc22.availableLevels);
    options.push({
      value: '22',
      label: `22% THC (${quantityStatus.thc22.current}g → up to ${maxLevel}g)`,
      current: quantityStatus.thc22.current,
      availableLevels: quantityStatus.thc22.availableLevels
    });
  }

  if (quantityStatus.thc29.canIncrease && quantityStatus.thc29.availableLevels.length > 0) {
    const maxLevel = Math.max(...quantityStatus.thc29.availableLevels);
    options.push({
      value: '29',
      label: `29% THC (${quantityStatus.thc29.current}g → up to ${maxLevel}g)`,
      current: quantityStatus.thc29.current,
      availableLevels: quantityStatus.thc29.availableLevels
    });
  }

  return options;
}

/**
 * Validates a quantity increase request
 */
export function validateQuantityIncreaseRequest(
  treatmentPlan: TreatmentPlan | null,
  selectedStrength: ThcStrength,
  requestedQuantity: number
): { isValid: boolean; error?: string } {
  if (!treatmentPlan) {
    return { isValid: false, error: 'No treatment plan found' };
  }

  const currentQuantity = getCurrentQuantity(treatmentPlan, selectedStrength);
  const availableLevels = getAvailableQuantityLevels(currentQuantity);

  if (currentQuantity === 0) {
    return { isValid: false, error: `No current ${selectedStrength}% THC treatment plan found` };
  }

  if (availableLevels.length === 0) {
    return { isValid: false, error: `Already at maximum quantity for ${selectedStrength}% THC` };
  }

  if (!availableLevels.includes(requestedQuantity)) {
    return { isValid: false, error: `Invalid quantity requested. Available levels: ${availableLevels.join(', ')}g` };
  }

  return { isValid: true };
}

/**
 * Validates multiple quantity increase requests
 */
export function validateMultipleQuantityIncreaseRequests(
  treatmentPlan: TreatmentPlan | null,
  requests: Array<{ strength: ThcStrength; requestedQuantity: number }>
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!treatmentPlan) {
    return { isValid: false, errors: ['No treatment plan found'] };
  }

  if (requests.length === 0) {
    return { isValid: false, errors: ['No strength selected for increase'] };
  }

  // Validate each request
  requests.forEach(request => {
    const validation = validateQuantityIncreaseRequest(
      treatmentPlan,
      request.strength,
      request.requestedQuantity
    );

    if (!validation.isValid && validation.error) {
      errors.push(`${request.strength}% THC: ${validation.error}`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Calculates score for quantity increase questionnaire
 */
export function calculateQuantityIncreaseScore(formData: QuantityIncreaseFormData): {
  totalScore: number;
  maxScore: number;
  isEligible: boolean;
  questionScores: Record<string, number>;
} {
  const questionScores: Record<string, number> = {};
  let totalScore = 0;
  
  // Reason for request (max 8 points)
  let reasonScore = 0;
  if (formData.reasonNotLasting) reasonScore += 3;
  if (formData.reasonHigherDoses) reasonScore += 2;
  if (formData.reasonTolerance) reasonScore += 2;
  if (formData.reasonIncreasedSymptoms) reasonScore += 3;
  if (formData.reasonOther) reasonScore += 1;
  questionScores.reasonForRequest = Math.min(reasonScore, 8);
  totalScore += questionScores.reasonForRequest;
  
  // Current effectiveness (max 6 points, scaled from 1-10)
  const effectiveness = parseInt(formData.currentEffectiveness || '1');
  questionScores.currentEffectiveness = Math.max(0, Math.min(6, Math.floor((effectiveness - 1) * 6 / 9)));
  totalScore += questionScores.currentEffectiveness;
  
  // Side effects (penalty system, max 4 points)
  let sideEffectsScore = 4;
  if (formData.sideEffectsStrong) sideEffectsScore = 0;
  else if (formData.sideEffectsModerate) sideEffectsScore = 2;
  else if (formData.sideEffectsMild) sideEffectsScore = 3;
  else if (formData.sideEffectsNone) sideEffectsScore = 4;
  questionScores.sideEffects = sideEffectsScore;
  totalScore += questionScores.sideEffects;
  
  // Usage consistency (max 6 points)
  let usageScore = 0;
  switch (formData.usageConsistency) {
    case 'full-amount': usageScore = 6; break;
    case 'varies': usageScore = 4; break;
    case 'leftover': usageScore = 1; break;
    case 'other': usageScore = 2; break;
  }
  questionScores.usageConsistency = usageScore;
  totalScore += questionScores.usageConsistency;
  
  // Health changes (max 3 points)
  questionScores.healthChanges = formData.healthChanges === 'no-changes' ? 3 : 1;
  totalScore += questionScores.healthChanges;
  
  // Expectations (max 2 points - based on length and reasonableness)
  const expectationsLength = (formData.expectations || '').trim().length;
  questionScores.expectations = expectationsLength > 10 ? 2 : expectationsLength > 0 ? 1 : 0;
  totalScore += questionScores.expectations;
  
  // Concerns (max 2 points - awareness of risks is good)
  const concernsLength = (formData.concerns || '').trim().length;
  questionScores.concerns = concernsLength > 10 ? 2 : concernsLength > 0 ? 1 : 0;
  totalScore += questionScores.concerns;
  
  // Intended usage (max 4 points)
  let intendedUsageScore = 0;
  switch (formData.intendedUsage) {
    case 'worsening-symptoms': intendedUsageScore = 4; break;
    case 'breakthrough-symptoms': intendedUsageScore = 4; break;
    case 'extend-daily': intendedUsageScore = 3; break;
    case 'gradual-increase': intendedUsageScore = 3; break;
    case 'unsure-advice': intendedUsageScore = 2; break;
    case 'other': intendedUsageScore = 1; break;
  }
  questionScores.intendedUsage = intendedUsageScore;
  totalScore += questionScores.intendedUsage;
  
  // Consent (max 5 points)
  questionScores.consent = formData.consent === 'yes' ? 5 : 0;
  totalScore += questionScores.consent;
  
  const maxScore = QUANTITY_INCREASE_SCORING.MAX_SCORE;
  const isEligible = totalScore >= QUANTITY_INCREASE_SCORING.ELIGIBILITY_THRESHOLD;
  
  return {
    totalScore,
    maxScore,
    isEligible,
    questionScores
  };
}
