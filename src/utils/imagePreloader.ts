// Image preloader utility to cache critical images
const preloadedImages = new Set<string>();

export const preloadImage = (src: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    // If already preloaded, resolve immediately
    if (preloadedImages.has(src)) {
      resolve();
      return;
    }

    const img = new Image();
    
    img.onload = () => {
      preloadedImages.add(src);
      resolve();
    };
    
    img.onerror = () => {
      console.warn(`Failed to preload image: ${src}`);
      reject(new Error(`Failed to preload image: ${src}`));
    };
    
    img.src = src;
  });
};

export const preloadCriticalImages = async (): Promise<void> => {
  const criticalImages = [
    '/zenith/zenith-logo.png',
    // Add other critical images here
  ];

  try {
    await Promise.all(criticalImages.map(preloadImage));
   
  } catch (error) {
    console.warn('Some critical images failed to preload:', error);
  }
};

export const isImagePreloaded = (src: string): boolean => {
  return preloadedImages.has(src);
};
