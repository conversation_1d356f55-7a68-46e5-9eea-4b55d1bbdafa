import { Box, useTheme, useMediaQuery, CssBaseline, <PERSON><PERSON><PERSON>, ThemeProvider } from "@mui/material";
import FormRegister from "./components/zenith/forms/FormRegister";
import FormQuestionnaire from "./components/zenith/forms/FormQuestionaire";
import FormDischarge from "./components/zenith/forms/FormDischarge";
import FormConsult from "./components/zenith/forms/FormConsult";
import "./styles/zenith/main.css";
import Footer from "./components/zenith/layouts/Footer";
import Header from "./components/zenith/layouts/Header";
import MetaPixel from "./hooks/MetaPixelMain";
import AnyTrackPixel from "./hooks/AnyTrackPixelMain";
import { Navigate, Outlet, useLocation, ReactLocation, Route, Router } from "@tanstack/react-location";
import NotFound from "./utils/not-found";
import { SnackbarProvider } from "notistack";
import { AuthProvider } from "./hooks/auth-provider";
import HealthCheck from "./components/zenith/forms/healthCheck";

const Shell: React.FC = () => {
	const theme = useTheme();
	const isDesktopOrMobile = useMediaQuery(theme.breakpoints.up("sm"));
	const location = useLocation();
	const currentPath = location.current.pathname;

	let bodyElement = document.getElementsByTagName("body")[0];
	// Changing the class of body Before mounting
	bodyElement.className =
		currentPath.includes("patient") || currentPath.includes("health-survey") ? "zenith" : "harvest";

	// Check if the current route has custom headers
	const isLandingPage = currentPath === "/patient/home";
	const isProfilePage = currentPath === "/patient/profile";
	const isChatPage = currentPath === "/patient/chat";
	const hasCustomHeader = isLandingPage || isProfilePage || isChatPage;

	return (
		<>
			<SnackbarProvider anchorOrigin={{ horizontal: "right", vertical: "top" }}>
				<div>
					{currentPath.includes("patient") && !currentPath.includes("consent") && !hasCustomHeader ? (
						<>
							<MetaPixel />
							<AnyTrackPixel />
							<Header />
							<Toolbar />
						</>
					) : null}
					<center>
						<div
							style={
								currentPath.includes("consent") && !isDesktopOrMobile
									? { width: "100vw", padding: 0 }
									: hasCustomHeader
									? { width: "100%", padding: 0, margin: 0, maxWidth: "100%" }
									: isDesktopOrMobile
									? { width: "60vw" }
									: { width: "90vw" }
							}
						>
							<Box
								mt={currentPath.includes("consent") || hasCustomHeader ? 0 : 3}
								mb={0}
								px={
									(currentPath.includes("consent") || hasCustomHeader) && !isDesktopOrMobile
										? 0
										: undefined
								}
								sx={{
									width: hasCustomHeader ? "100%" : undefined,
									maxWidth: hasCustomHeader ? "100%" : undefined,
									padding: hasCustomHeader ? 0 : undefined,
									margin: hasCustomHeader ? 0 : undefined,
								}}
							>
								<Outlet />
							</Box>
						</div>
					</center>
					{(currentPath.includes("zenith") || currentPath.includes("patient")) &&
					!currentPath.includes("consent") &&
					!hasCustomHeader ? (
						<>
							<Footer />
						</>
					) : null}
				</div>
			</SnackbarProvider>
		</>
	);
};

export default Shell;
