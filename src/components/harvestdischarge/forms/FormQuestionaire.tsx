import React, { useEffect } from 'react';
import { useState } from 'react';
import { MobileStepper, Grid2, Box, Button, Stack, IconButton, Typography, FormGroup, FormControlLabel, Checkbox, makeStyles, TextField } from '@mui/material';
import { useNavigate } from '@tanstack/react-location';
import axios from 'axios';
import LoadingScreen from "../../../utils/loading-screen";
import { BorderAll } from '@mui/icons-material';
import { enqueueSnackbar } from 'notistack';

const inputStyle = {
    margin: '20px 0',
  '& .MuiOutlinedInput-root': {
    '&.Mui-focused fieldset': {
      borderColor: '#FD6440',
    },
    '& input': {
      color: '#FD6440',
    },
    '& fieldset': {
      borderColor: '#FD6440',
    },
    '&:hover fieldset': {
      borderColor: '#FD6440',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#FD6440',
  },
}

function FormQuestionaire() {

  const exp_api = import.meta.env.VITE_EXP_API;

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    firstname: "",
    lastname: "",
    email: "",
    phone: "",
    pain: "",
    sleep: "",
    anxiety: "",
    drained: "",
    recover: "",
    twoweeks: "",
    rest: "",
    use: "",
    active: "",
    quality: "",
  });
  const [formErrors, setFormErrors] = useState({
    firstname: true,
    lastname: true,
    email: true,
    phone: true,
  });
  const [canDoNext, setCanDoNext] = useState(false);


  const [selectedOptions, setSelectedOptions] = useState({
    a: 0,
    b: 0,
    c: 0,
    d: 0
  });

  const [issues, setIssues] = useState({
    sleep: 0,
    pain: 0,
    anxiety: 0
  });

  const [isLoading, setIsLoading] = useState(false);
  const [dataSubmitted, setDataSubmitted] = useState(false);
  const [wpToken, setWPToken] = useState('');

  const [suitable, setSuitable] = useState(0);

  const navigate = useNavigate();

  const handleSubmit = async () => {
    const selectCheck: (number)[] = Object.entries(selectedOptions).map(([key, value]) => value);
    const control: (string)[] = Object.entries(selectedOptions).map(([key, value]) => key);
    let mostSelectedIndex = selectCheck.reduce((a, b) => selectCheck[a] > selectCheck[b] ? a : b);
    let mostSelected = control[mostSelectedIndex];

    setIsLoading(true);
try {
      const result = await axios.post(
        `${
          import.meta.env.VITE_API_URL
        }/funnel/v1.0/patient/harvest/discharge`,
        formData,
        { withCredentials: true }
      );
      if (result.data) {
        enqueueSnackbar("Questionnaire Submitted", {
          variant: "success",
        });
        if (mostSelected == 'a') {
      navigate({ to: '/harvestdischarge/thankyou_a'});
    }
    else if (mostSelected == 'b') {
      navigate({ to: '/harvestdischarge/thankyou_b'});
    }
    else if (mostSelected == 'c') {
      navigate({ to: '/harvestdischarge/thankyou_c'});
    }
    else {
      navigate({ to: '/harvestdischarge/thankyou_d'});
    }

      }
    } catch (e) {
      enqueueSnackbar("Failed to submit Questionnaire", {
        variant: "error",
      });
      throw e;
    } finally {
      setIsLoading(false);
    }
  };
  const handleNextCLick = (event: React.MouseEvent<HTMLButtonElement> ) => {

    if(formData.firstname==''){
      setFormErrors({ ...formErrors, ['firstname']: true });
    }
    else if(formData.lastname==''){
      setFormErrors({ ...formErrors, ['lastname']: true });
    }
    else if (formData.email == "") {
      setFormErrors({ ...formErrors, ["email"]: true });
    } else if (formData.phone == "") {
      setFormErrors({ ...formErrors, ["phone"]: true });
    } else {
      if (activeStep !== steps.length - 1) {
        setTimeout(() => {
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
        }, 250);
      }
    }

  };

  const handleNextValueClick = (event: React.MouseEvent<HTMLDivElement>, step: string, newvalue = '' ) => {

    if (step != undefined) {
      setFormData({ ...formData, [step]: newvalue });
    }

    let selected: typeof selectedOptions = selectedOptions;
    selected[newvalue as keyof typeof selectedOptions] = selected[newvalue as keyof typeof selectedOptions] + 1;


    setSelectedOptions(selected);

    if (activeStep !== (steps.length - 1)) {
      setTimeout(() => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }, 250);
    }

  };

  const handleNext = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, step: string, newvalue = '') => {
    // setCanDoNext(false);
    if (newvalue == '') {
      const { name, value } = event.target;
      newvalue = value;
    }

    if (step != undefined) {
      setFormData({ ...formData, [step]: newvalue });
    }

    let selected: typeof selectedOptions = selectedOptions;
    selected[newvalue as keyof typeof selectedOptions] = selected[newvalue as keyof typeof selectedOptions] + 1;


    setSelectedOptions(selected);

    if (activeStep !== (steps.length - 1)) {
      setTimeout(() => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }, 250);
    }

  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    
    // Special handling for phone field
    if (name === "phone") {
      // Ensure phone always starts with '04' and only contains numbers
      let phoneValue = value;
      
      // Remove non-numeric characters
      phoneValue = phoneValue.replace(/[^0-9]/g, '');
      
      // Ensure it starts with '04'
      if (!phoneValue.startsWith('04')) {
        phoneValue = '04' + phoneValue.substring(phoneValue.startsWith('4') ? 1 : 0);
      }
      
      // Limit to 10 digits (04 + 8 more digits)
      phoneValue = phoneValue.substring(0, 10);
      
      setFormData((prev) => {
        return { ...prev, [name]: phoneValue };
      });
    } else {
      setFormData((prev) => {
        return { ...prev, [name]: value };
      });
    }

    let errors = formErrors;
    if (event.target.validity.valid) {
      errors = { ...errors, [name]: false };
      setFormErrors({ ...formErrors, [name]: false });
    } else {
      errors = { ...errors, [name]: true };
      setFormErrors({ ...formErrors, [name]: true });
    }
  };

  useEffect(()=>{
    const errorCheck: boolean[] = Object.entries(formErrors).map(
      ([key, value]) => value
    );
    setCanDoNext(errorCheck.every((v) => v === false));
  },[formErrors])

  const steps = [
    {
      number: 0,
      label: "Details",
      content: (
        <Stack>
          <TextField
            type="text"
            label="First name"
            name="firstname"
            onChange={handleChange}
            margin="normal"
            value={formData.firstname}
            required={true}
            error={formErrors.firstname}
            helperText={
              formErrors.firstname ? "Please enter your First name" : ""
            }
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          <TextField
            type="text"
            label="Last name"
            name="lastname"
            onChange={handleChange}
            margin="normal"
            value={formData.lastname}
            required={true}
            error={formErrors.lastname}
            helperText={
              formErrors.lastname ? "Please enter your Last name" : ""
            }
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          <TextField
            type="email"
            label="Email"
            name="email"
            onChange={handleChange}
            margin="normal"
            value={formData.email}
            required={true}
            onKeyPress={(event) => {
              if (event.key === " ") {
                event.preventDefault();
              }
            }}
            error={formErrors.email}
            helperText={formErrors.email ? "Please enter a valid email" : ""}
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          <TextField
            type="text"
            label="Phone number"
            name="phone"
            onChange={handleChange}
            margin="normal"
            value={formData.phone}
            required={true}
            slotProps={{ 
              htmlInput: { 
                maxLength: 10,
                pattern: "[0-9]*",
                inputMode: "numeric"
              } 
            }}
            onKeyPress={(event) => {
              // Only allow numeric values
              const char = event.key;
              const isNumeric = /^[0-9]+$/.test(char);
              if (!isNumeric || event.key === " ") {
                event.preventDefault();
              }
            }}
            error={formErrors.phone}
            helperText={formErrors.phone ? "Please enter a valid phone" : "Phone should start with 04 followed by 8 digits"}
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          {canDoNext ? (
            <Button
              type="button"
              onClick={handleNextCLick}
              fullWidth
              variant="contained"
              color="primary"
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
        </Stack>
      ),
    },
    {
      number: 1,
      label: "Pain",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How often do{" "}
            <Box component="span" color="primary.main">
              you experience
            </Box>{" "}
            physical pain, stiffness, or discomfort ?
          </Typography>
          <Typography
            fontWeight={"800"}
            textTransform={"uppercase"}
            marginBottom={2}
            color="primary.main"
          >
            (e.g., back pain, joint pain, or muscle tension)
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["pain"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["pain"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "pain", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Rarely, or only after intense physical activity
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["pain"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["pain"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "pain", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Occasionally, but it doesn't interfere with daily life
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["pain"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["pain"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "pain", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Frequently, and it affects my ability to perform daily tasks
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["pain"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["pain"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "pain", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Constantly, and the pain is severe
              </Typography>
            </Box>
            <img
              src="/harvest/illustration-01.png"
              width={"100px"}
              style={{ alignSelf: "end" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 2,
      label: "Sleep",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you have{" "}
            <Box component="span" color="primary.main">
              trouble
            </Box>{" "}
            sleeping or feel unrested even after a full night's sleep?
          </Typography>
          <img
            src={"/harvest/illustration-02.png"}
            width={"80px"}
            style={{ position: "absolute", left: "0", marginTop: "-70px" }}
          />
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["sleep"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["sleep"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "sleep", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Rarely, I sleep well most night
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["sleep"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["sleep"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "sleep", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Occasionally, when I'm stressed or have a lot on my mind
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["sleep"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["sleep"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "sleep", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Often, I struggle with falling asleep or staying asleep
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["sleep"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["sleep"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "sleep", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Almost every night, I feel restless or wake up tired
              </Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 3,
      label: "Anxiety",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How would you{" "}
            <Box component="span" color="primary.main">
              describe
            </Box>{" "}
            your stress or anxiety levels in daily life?
          </Typography>
          <Stack spacing={1}>
            <img
              src="/harvest/sloth-01.png"
              width={"70px"}
              style={{ alignSelf: "end", marginTop: "-35px" }}
            />
            <Box
              bgcolor={
                formData["anxiety"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["anxiety"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "anxiety", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Minimal - I feel calm and in control
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["anxiety"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["anxiety"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "anxiety", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Moderate - I feel stressed occasionally but manage it well
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["anxiety"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["anxiety"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "anxiety", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                High - I feel stressed or anxious often, and it affects my focus
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["anxiety"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["anxiety"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "anxiety", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Severe - I feel overwhelmed, unable to cope, or experience panic
                attacks
              </Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 4,
      label: "Drained",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How frequently do you feel mentally or{" "}
            <Box component="span" color="primary.main">
              emotionally drained?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["drained"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["drained"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "drained", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Rarely - I generally feel energetic and positive
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["drained"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["drained"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "drained", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Sometimes, especially after a tough day
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["drained"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["drained"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "drained", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Frequently - I often feel exhausted, even without much activity
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["drained"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["drained"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "drained", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Constantly - I feel drained and unable to concentrate most days
              </Typography>
            </Box>
            <img
              src="/harvest/illustration-016.png"
              width={"80px"}
              style={{ alignSelf: "start" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 5,
      label: "Recover",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            <Box component="span" color="primary.main">
              How well do you recover
            </Box>{" "}
            from injuries, illnesses, or physical exertion?
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["recover"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["recover"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "recover", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Quickly - my body bounces back easily
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["recover"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["recover"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "recover", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Fairly well - I recover at an average pace
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["recover"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["recover"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "recover", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Slowly - it takes longer than usual for me to feel better
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["recover"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["recover"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "recover", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Poorly - I often need additional support to recover
              </Typography>
            </Box>
            <img
              src="/harvest/illustration-08.png"
              width={"60px"}
              style={{ alignSelf: "end" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 6,
      label: "Twoweeks",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Have you experienced any of the following for more than{" "}
            <Box component="span" color="primary.main">
              two weeks?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["twoweeks"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["twoweeks"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "twoweeks", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Persistent sadness, low energy, or hopelessness
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["twoweeks"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["twoweeks"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "twoweeks", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Difficulty concentrating, irritability, or mood swings
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["twoweeks"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["twoweeks"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "twoweeks", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Physical tension, pain, or mobility issues
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["twoweeks"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["twoweeks"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "twoweeks", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Trouble sleeping, eating, or maintaining routines
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["twoweeks"] == "e" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["twoweeks"] == "e" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "twoweeks", "e")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">No I feel fine</Typography>
            </Box>
            <img
              src="/harvest/illustration-010.png"
              width={"80px"}
              style={{ alignSelf: "start" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 7,
      label: "Rest",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you experience chronic pain or discomfort that doesn't seem to
            improve with rest, even after two weeks?
          </Typography>
          <img
            src="/harvest/illustration-09.png"
            width={"40px"}
            style={{ position: "absolute", right: "0", marginTop: "-60px" }}
          />
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["rest"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["rest"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "rest", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">No, I feel fine physically</Typography>
            </Box>
            <Box
              bgcolor={
                formData["rest"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["rest"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "rest", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Occasionally, but it resolves on its own
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["rest"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["rest"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "rest", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Yes, and it's noticeable daily or during specific movements
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["rest"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["rest"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "rest", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Yes, it's constant and impacts my quality of life
              </Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 8,
      label: "Use",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            <Box component="span" color="primary.main">
              How often do
            </Box>{" "}
            you use natural or alternative methods to manage pain, stress, or
            sleep issues?
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["use"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={formData["use"] == "a" ? "primary.main" : "secondary.main"}
              onClick={(event) => handleNextValueClick(event, "use", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Never - I don't rely on alternative methods
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["use"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={formData["use"] == "b" ? "primary.main" : "secondary.main"}
              onClick={(event) => handleNextValueClick(event, "use", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Occasionally - I try things like herbal teas or meditation
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["use"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={formData["use"] == "c" ? "primary.main" : "secondary.main"}
              onClick={(event) => handleNextValueClick(event, "use", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Frequently - I use over-the-counter remedies or supplements
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["use"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={formData["use"] == "d" ? "primary.main" : "secondary.main"}
              onClick={(event) => handleNextValueClick(event, "use", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Regularly - I've explored medicinal cannabis or similar options
              </Typography>
            </Box>
            <img
              src="/harvest/illustration-011.png"
              width={"40px"}
              style={{ alignSelf: "start" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 9,
      label: "Active",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How often would you rate your ability to stay active or engaged in
            physical activities?
          </Typography>
          <img
            src="/harvest/illustration-05.png"
            width={"100px"}
            style={{
              position: "absolute",
              right: "-30px",
              marginTop: "-100px",
            }}
          />
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["active"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["active"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "active", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Excellent - I'm active and rarely experience issues
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["active"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["active"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "active", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Good - I'm active but occasionally experience minor setbacks
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["active"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["active"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "active", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Moderate - I face physical discomfort that limits my activity
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["active"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["active"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "active", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Poor - I avoid physical activity due to pain or stiffness
              </Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 10,
      label: "Quality",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How would you describe your overall{" "}
            <Box component="span" color="primary.main">
              quality of life?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData["quality"] == "a" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["quality"] == "a" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "quality", "a")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Excellent - I'm healthy, happy, and energetic
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["quality"] == "b" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["quality"] == "b" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "quality", "b")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Good - I'm content but experience occasional difficulties
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["quality"] == "c" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["quality"] == "c" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "quality", "c")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Fair - I have some challenges that interfere with my well-being
              </Typography>
            </Box>
            <Box
              bgcolor={
                formData["quality"] == "d" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData["quality"] == "d" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "quality", "d")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">
                Poor - My physical or mental health significantly affects my
                life
              </Typography>
            </Box>
            <img
              src="/harvest/illustration-015.png"
              width={"60px"}
              style={{ alignSelf: "start" }}
            />
            {formData["quality"] != "" ? (
              <Button
                type="button"
                onClick={handleSubmit}
                fullWidth
                variant="contained"
                color="primary"
                style={{ marginTop: "20px" }}
              >
                Submit
              </Button>
            ) : null}
          </Stack>
        </React.Fragment>
      ),
    },
  ];

  return (
    <>
      {isLoading && <LoadingScreen />}
      <form onSubmit={(e) => e.preventDefault()}>
        <Stack gap={2}>
          <MobileStepper
            variant="dots"
            steps={steps.length}
            position="static"
            activeStep={activeStep}
            sx={{ backgroundColor: "#000000" }}
            nextButton={
              <div>
                <Typography fontSize={'0.8em'} color='#ffffff'>{activeStep + 1} | {steps.length}</Typography>

              </div>
            }
            backButton={
              <Box bgcolor={"primary.main"} padding={1} borderRadius={'0'}>
              </Box>
            }
          />
          <center>
            <Box >
              {steps[activeStep].content}
            </Box>
          </center>
        </Stack>
      </form>
    </>
  );
}

export default FormQuestionaire;