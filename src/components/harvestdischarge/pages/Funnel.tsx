import * as React from "react";
import { useEffect } from "react";
import { Box, useTheme, useMediaQuery, Typography } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import harvestTheme from '../../../styles/harvest/theme';
import FormQuestionaire from "../forms/FormQuestionaire";
import '../../../styles/harvest/main.css';
import Footer from "../layouts/Footer";
import Header from "../layouts/Header";

function Home() {

    useEffect(() => {
        document.title = 'Find your path to wellness - Harvest';

        let link: HTMLLinkElement | null = document.querySelector("link[rel~='icon']");
        if (!link) {
            link = document.createElement('link');
            link.rel = 'icon';
            document.getElementsByTagName('head')[0].appendChild(link);
        }
        link.href = '/harvest-favicon.png';

    }, []);

    const [currentForm, setCurrentForm] = React.useState(0);

    const queryParameters = new URLSearchParams(window.location.search);
    const form = queryParameters.get("form");

    const theme = useTheme()
    const isDesktopOrMobile = useMediaQuery(theme.breakpoints.up('sm'));

    useEffect(() => {
        if (form != null) {
            setCurrentForm(parseInt(form) - 1);
        }
        else {
            setCurrentForm(0);
        }
    }, []);

    function _renderFormContent(form: number) {

        switch (form) {
            case 0:
                return <FormQuestionaire />;

            default:
                return <div>All Done!</div>;
        }
    }

    return (
        <ThemeProvider theme={harvestTheme}>
        <div>
            <Header />
            <center>
                <Typography variant="body1" textTransform={'uppercase'} fontWeight={'700'}>Find your path to wellness</Typography>
                <div style={isDesktopOrMobile ? { width: '20vw', minWidth:'380px', position:'relative' } : { width: '90vw' }}>
                    <Box mt={0}>
                        {_renderFormContent(currentForm)}
                    </Box>
                </div>
            </center>
            <Footer />
        </div>
        </ThemeProvider>
    );
}

export default Home;