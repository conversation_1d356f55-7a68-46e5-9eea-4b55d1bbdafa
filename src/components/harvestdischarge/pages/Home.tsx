import * as React from "react";
import { useEffect } from "react";
import { Box, useTheme, useMediaQuery, Typography, Button } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import harvestTheme from '../../../styles/harvest/theme';
import { useNavigate } from '@tanstack/react-location';
import '../../../styles/harvest/main.css';
import Footer from "../layouts/Footer";
import Header from "../layouts/Header";

function Startquiz() {

    const navigate = useNavigate();
    

    useEffect(() => {
        document.title = 'Find your path to wellness - Harvest';

        let link: HTMLLinkElement | null = document.querySelector("link[rel~='icon']");
        if (!link) {
            link = document.createElement('link');
            link.rel = 'icon';
            document.getElementsByTagName('head')[0].appendChild(link);
        }
        link.href = '/harvest-favicon.png';

    }, []);

    const theme = useTheme()
    const isDesktopOrMobile = useMediaQuery(theme.breakpoints.up('sm'));

    return (
        <ThemeProvider theme={harvestTheme}>
        <div>
            <Header />
            <center>
                <div style={isDesktopOrMobile ? { width: '60vw' } : { width: '95vw' }}>
                    <Box sx={{borderBottom: 1, borderColor:'#ffffff', width:'80%', height: '80px', marginBottom: '20px'}}>
                        <img src='/harvest/sloth-01.png' width={'100px'} />
                    </Box>
                    <Typography style={{lineHeight: '0.9em', textTransform: 'uppercase', fontSize:'30px', fontWeight:'bold',}} variant="body1">Find your path to</Typography>
                    <Typography style={{lineHeight: '0.9em', textTransform: 'uppercase', fontSize:'70px', fontWeight:'bold'}} variant="body1">wellness</Typography>
                    <Typography style={{lineHeight: '0.9em', textTransform: 'uppercase', fontSize:'39px', fontWeight:'bold',}} variant="body1"><Box component={'span'} color='primary.main' fontWeight={'700'}>Take the quiz</Box></Typography>
                    <img src='/harvest/illustration-05.png' width={'100px'} style={{ position: 'absolute', left: '-20px', marginTop: '-70px' }} />
                    <img src='/harvest/illustration-01.png' width={'100px'} style={{ position: 'absolute', right: '-10px', marginTop: '-70px' }} />
                    <Button variant="contained" color='primary' style={{padding:'0px 60px', marginTop:'20px', fontSize:'25px', borderRadius:'50px'}} onClick={() => navigate({to: '/harvestdischarge/quiz'})}>
                        Start Quiz Now
                    </Button>
                </div>
            </center>
            <div className={isDesktopOrMobile ? 'thankyouBanner' : 'thankyouBannerMobile'} style={{backgroundImage: 'url(/harvest/thankyoubanner-01.webp)', marginTop: '-20px'}}>
            </div>
            <Footer />
        </div>
        </ThemeProvider>
    );
}

export default Startquiz;