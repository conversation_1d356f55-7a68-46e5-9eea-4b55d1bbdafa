import * as React from "react";
import { useEffect } from "react";
import {
  Box,
  useTheme,
  useMediaQuery,
  Typography,
  Button,
} from "@mui/material";
import { ThemeProvider } from "@mui/material/styles";
import harvestTheme from "../../../styles/harvest/theme";
import FormQuestionaire from "../forms/FormQuestionaire";
import "../../../styles/harvest/main.css";
import Footer from "../layouts/Footer";
import Header from "../layouts/Header";

function ThankYouOther() {
  useEffect(() => {
    document.title = "Find your path to wellness - Harvest";

    let link: HTMLLinkElement | null =
      document.querySelector("link[rel~='icon']");
    if (!link) {
      link = document.createElement("link");
      link.rel = "icon";
      document.getElementsByTagName("head")[0].appendChild(link);
    }
    link.href = "/harvest-favicon.png";
    setTimeout(function () {
      window.location.href = `${
        import.meta.env.VITE_ZENITH_URL
      }/patient/phone-verification`;
    }, 20000);
  }, []);

  const theme = useTheme();
  const isDesktopOrMobile = useMediaQuery(theme.breakpoints.up("sm"));

  return (
    <ThemeProvider theme={harvestTheme}>
      <div>
        <Header />
        <div
          className={
            isDesktopOrMobile ? "thankyouBanner" : "thankyouBannerMobile"
          }
          style={{ backgroundImage: "url(/harvest/thankyoubanner-02.webp)" }}
        >
          <img
            src="/harvest/thankyoulegend.svg"
            style={{ marginBottom: "20px" }}
          />
        </div>
        <center>
          <div
            style={isDesktopOrMobile ? { width: "60vw" } : { width: "95vw" }}
          >
            <Typography m={5} ml={2} mr={2} variant="body1">
              Professional support is recommended. Chronic physical discomfort
              may require{" "}
              <Box component={"span"} color="primary.main" fontWeight={"700"}>
                physiotherapy
              </Box>
              , while emotional or mental health concerns may benefit from{" "}
              <Box component={"span"} color="primary.main" fontWeight={"700"}>
                psychiatry
              </Box>
              . Explore{" "}
              <Box component={"span"} color="primary.main" fontWeight={"700"}>
                alternative health
              </Box>{" "}
              solutions like medicinal cannabis for added relief.
            </Typography>
            <Typography ml={2} mr={2} variant="body1">
              You will be{" "}
              <Box component={"span"} color="primary.main" fontWeight={"700"}>
                redirected to book your appointment
              </Box>{" "}
              in twenty seconds...
            </Typography>
            <Button
              variant="contained"
              color="primary"
              style={{ margin: "40px" }}
              onClick={() =>
                (window.location.href = `${
                  import.meta.env.VITE_ZENITH_URL
                }/patient/phone-verification`)
              }
            >
              Go book your appointment
            </Button>
          </div>
        </center>
        <Footer />
      </div>
    </ThemeProvider>
  );
}

export default ThankYouOther;
