import React, { useEffect } from 'react';
import { useState } from 'react';
import { MobileStepper, Grid2, Box, Button, Stack, IconButton, Typography, FormGroup, FormControlLabel, Checkbox, makeStyles, TextField } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import { useNavigate } from '@tanstack/react-location';
import axios from 'axios';
import LoadingScreen from "../../../utils/loading-screen";
import { enqueueSnackbar } from 'notistack';

const inputStyle = {
  margin: '20px 0',
  '& .MuiOutlinedInput-root': {
    '&.Mui-focused fieldset': {
      borderColor: '#FD6440',
    },
    '& input': {
      color: '#FD6440',
    },
    '& fieldset': {
      borderColor: '#FD6440',
    },
    '&:hover fieldset': {
      borderColor: '#FD6440',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#FD6440',
  },
}

function FormQuestionaire() {

  const exp_api = import.meta.env.VITE_EXP_API;

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    firstname: "",
    lastname: "",
    email: "",
    phone: "",
    age: "",
    lifestyle: "",
    motivation: [""],
    consumption: "",
    reason: [""],
    relax: "",
    sleep: "",
    stress: "",
    gain: [""],
    impact: "",
    mood: "",
    fatigue: "",
    concentration: "",
    discomfort: "",
    wellbeing: [""],
    painduration: "",
    painaffect: [""],
    paintreatment: "",
    managepain: "",
    alternativepain: "",
    anxious: "",
    controlanxiety: "",
    anxietytreatment: "",
    anxietymanage: "",
    alternativeanxiety: "",
    sleeptrouble: "",
    helpsleeping: "",
  });

  const [formErrors, setFormErrors] = useState({
    firstname: true,
    lastname: true,
    email: true,
    phone: true,
  });
  const [canDoNext, setCanDoNext] = useState(false);

  const [issues, setIssues] = useState({
    sleep: 0,
    pain: 0,
    anxiety: 0
  });

  const [isLoading, setIsLoading] = useState(false);
  const [suitable, setSuitable] = useState(0);
  const [interested, setInterested] = useState(0);

  const suitableOptions = ['sleep', 'pain', 'anxiety', 'relaxation', 'coping', 'pain'];

  const navigate = useNavigate();

  const handleSubmit = async () => {

    let found = (interested > 0 && suitable > 0) ? true : false;

    setIsLoading(true);
    try {
      const result = await axios.post(
        `${
          import.meta.env.VITE_API_URL
        }/funnel/v1.0/patient/harvest/no-alcohol`,
        formData,
        { withCredentials: true }
      );
      if (result.data) {
        enqueueSnackbar("Questionnaire Submitted", {
          variant: "success",
        });
        if (found) {
          navigate({ to: "/harvest/thankyou" });
        } else {
          navigate({ to: "/harvest/thankyou_b" });
        }

      }
    } catch (e) {
      enqueueSnackbar("Failed to submit Questionnaire", {
        variant: "error",
      });
      throw e;
    } finally {
      setIsLoading(false);
    }
  };
  const handleNextCLick = (event: React.MouseEvent<HTMLButtonElement>) => {

    if (formData.firstname == '') {
      setFormErrors({ ...formErrors, ['firstname']: true });
    }
    else if (formData.lastname == '') {
      setFormErrors({ ...formErrors, ['lastname']: true });
    }
    else if (formData.email == "") {
      setFormErrors({ ...formErrors, ["email"]: true });
    } else if (formData.phone == "") {
      setFormErrors({ ...formErrors, ["phone"]: true });
    } else {
      if (activeStep !== steps.length - 1) {
        setTimeout(() => {
          setActiveStep((prevActiveStep) => prevActiveStep + 1);
        }, 250);
      }
    }

  };

  const handleNextValueClick = (event: React.MouseEvent<HTMLDivElement>, step: string, newvalue = '') => {

    if (step != undefined) {
      setFormData({ ...formData, [step]: newvalue });
    }

    if (['alternativepain', 'alternativeanxiety', 'helpsleeping'].includes(step)) {
      if (['Yes, definitely', 'Maybe'].includes(newvalue)) {
        setInterested((prevItem) => prevItem + 1);
      }
    }

    if (activeStep !== (steps.length - 1)) {
      setTimeout(() => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }, 250);
    }

  };

  const handleNext = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, step: string, newvalue = '') => {
    // setCanDoNext(false);
    if (newvalue == '') {
      const { name, value } = event.target;
      newvalue = value;
    }

    if (step != undefined) {
      setFormData({ ...formData, [step]: newvalue });
    }

    if (['alternativepain', 'alternativeanxiety', 'helpsleeping'].includes(step)) {
      if (['Yes, definitely', 'Maybe'].includes(newvalue)) {
        setInterested((prevItem) => prevItem + 1);
      }
    }

    if (activeStep !== (steps.length - 1)) {
      setTimeout(() => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
      }, 250);
    }

  };



  const handleCheck = (event: React.ChangeEvent<HTMLInputElement>, step: string, value = '') => {
    // setCanDoNext(false);
    let checked: string[] = [];

    switch (step) {
      case 'motivation':
        checked = formData.motivation;
        break;
      case 'reason':
        checked = formData.reason;
        break;
      case 'gain':
        checked = formData.gain;
        break;
      case 'wellbeing':
        checked = formData.wellbeing;
        break;
      case 'painaffect':
        checked = formData.painaffect;
        break;

      default:
        checked = [];
        break;
    }

    let currentIssues = issues;
    if (checked.includes(value)) {
      checked = checked.filter((ele, ind) => ele !== value);
      if (suitableOptions.includes(value)) {
        setSuitable((prevItem) => (prevItem > 0) ? prevItem - 1 : 0);

      }
    }
    else {
      checked.push(value);
      if (suitableOptions.includes(value)) {
        setSuitable((prevItem) => prevItem + 1);
      }
    }
    setFormData({ ...formData, [step]: checked });
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = event.target;
    
    // Special handling for phone field
    if (name === "phone") {
      // Ensure phone always starts with '04' and only contains numbers
      let phoneValue = value;
      
      // Remove non-numeric characters
      phoneValue = phoneValue.replace(/[^0-9]/g, '');
      
      // Ensure it starts with '04'
      if (!phoneValue.startsWith('04')) {
        phoneValue = '04' + phoneValue.substring(phoneValue.startsWith('4') ? 1 : 0);
      }
      
      // Limit to 10 digits (04 + 8 more digits)
      phoneValue = phoneValue.substring(0, 10);
      
      setFormData((prev) => {
        return { ...prev, [name]: phoneValue };
      });
    } else {
      setFormData((prev) => {
        return { ...prev, [name]: value };
      });
    }
    
    let errors = formErrors;
    if (event.target.validity.valid) {
      errors = { ...errors, [name]: false };
      setFormErrors({ ...formErrors, [name]: false });
    } else {
      errors = { ...errors, [name]: true };
      setFormErrors({ ...formErrors, [name]: true });
    }
  };

  useEffect(()=>{
      const errorCheck: boolean[] = Object.entries(formErrors).map(
        ([key, value]) => value
      );
      setCanDoNext(errorCheck.every((v) => v === false));
    },[formErrors])

  const handleBack = async () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };



  const steps = [
    {
      number: 0,
      label: "Details",
      content: (
        <Stack>
          <TextField
            type="text"
            label="First name"
            name="firstname"
            onChange={handleChange}
            margin="normal"
            value={formData.firstname}
            required={true}
            error={formErrors.firstname}
            helperText={
              formErrors.firstname ? "Please enter your First name" : ""
            }
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          <TextField
            type="text"
            label="Last name"
            name="lastname"
            onChange={handleChange}
            margin="normal"
            value={formData.lastname}
            required={true}
            error={formErrors.lastname}
            helperText={
              formErrors.lastname ? "Please enter your Last name" : ""
            }
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          <TextField
            type="email"
            label="Email"
            name="email"
            onChange={handleChange}
            margin="normal"
            value={formData.email}
            onKeyPress={(event) => {
              if (event.key === " ") {
                event.preventDefault();
              }
            }}
            required={true}
            error={formErrors.email}
            helperText={formErrors.email ? "Please enter a valid email" : ""}
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          <TextField
            type="text"
            label="Phone number"
            name="phone"
            onChange={handleChange}
            margin="normal"
            value={formData.phone}
            onKeyPress={(event) => {
              // Only allow numeric values
              const char = event.key;
              const isNumeric = /^[0-9]+$/.test(char);
              if (!isNumeric || event.key === " ") {
                event.preventDefault();
              }
            }}
            required={true}
            slotProps={{ 
              htmlInput: { 
                pattern: "[0-9]*",
                inputMode: "numeric"
              } 
            }}
            error={formErrors.phone}
            helperText={
              formErrors.phone ? "Please enter a valid phone number" : "Phone should start with 04 followed by 8 digits"
            }
            sx={{ m: 0, ...inputStyle }}
            autoComplete="off"
          />
          {canDoNext ? (
            <Button
              type="button"
              onClick={handleNextCLick}
              fullWidth
              variant="contained"
              color="primary"
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
        </Stack>
      ),
    },
    {
      number: 1,
      label: "Age",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            What is your{" "}
            <Box component="span" color="primary.main">
              age range?
            </Box>
          </Typography>
          <img
            src="/harvest/illustration-04.png"
            style={{ position: "absolute", margin: "-70px 0 0 -220px" }}
          />
          <Grid2 container rowSpacing={2} columnSpacing={2} marginTop={"40px"}>
            <Grid2
              size={6}
              onClick={(event) => handleNextValueClick(event, "age", "18")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.age == "18" ? "secondary.main" : "primary.main"
                }
                borderRadius={"5px 5px 0 0"}
                padding={1}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <img src="/harvest/age_18.png" width={"100%"} />
              </Box>
              <Box
                bgcolor={
                  formData.age == "18" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={formData.age == "18" ? "primary.main" : "secondary.main"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography variant="h6">Age : 18-25</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) => handleNextValueClick(event, "age", "26")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.age == "26" ? "secondary.main" : "primary.main"
                }
                padding={1}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img src="/harvest/age_26.png" width={"100%"} />
              </Box>
              <Box
                bgcolor={
                  formData.age == "26" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={formData.age == "26" ? "primary.main" : "secondary.main"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography variant="h6">Age : 26-35</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) => handleNextValueClick(event, "age", "36")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.age == "36" ? "secondary.main" : "primary.main"
                }
                padding={1}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img src="/harvest/age_36.png" width={"100%"} />
              </Box>
              <Box
                bgcolor={
                  formData.age == "36" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={formData.age == "36" ? "primary.main" : "secondary.main"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography variant="h6">Age : 36-50</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) => handleNextValueClick(event, "age", "50")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.age == "50" ? "secondary.main" : "primary.main"
                }
                padding={1}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img src="/harvest/age_50.png" width={"100%"} />
              </Box>
              <Box
                bgcolor={
                  formData.age == "50" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={formData.age == "50" ? "primary.main" : "secondary.main"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography variant="h6">Age : 50+</Typography>
              </Box>
            </Grid2>
          </Grid2>
          <Typography variant="body1" marginTop={5}>
            By continuing, you agree to our{" "}
            <a
              href="https://letsroll.harvest.delivery/terms-and-conditions/"
              target="blank"
            >
              Terms of service
            </a>{" "}
            and acknowledge our{" "}
            <a href="https://letsroll.harvest.delivery/privacy/" target="blank">
              Privacy policy and Cookie policy
            </a>
          </Typography>
        </React.Fragment>
      ),
    },
    {
      number: 2,
      label: "Lifestyle",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How would you describe your{" "}
            <Box component="span" color="primary.main">
              lifestyle?
            </Box>
          </Typography>
          <Grid2 container rowSpacing={2} columnSpacing={0}>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Very Active")
              }
            >
              <Box
                bgcolor={
                  formData.lifestyle == "Very Active"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.lifestyle == "Very Active"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography
                  variant="h6"
                  textTransform={"uppercase"}
                  fontWeight={
                    formData.lifestyle == "Very Active" ? "700" : "400"
                  }
                >
                  Very Active
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Very Active")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.lifestyle == "Very Active"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/shaka-head-04.png"
                  width={"65px"}
                  height={"65px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Moderately Active")
              }
            >
              <Box
                bgcolor={
                  formData.lifestyle == "Moderately Active"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.lifestyle == "Moderately Active"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography
                  variant="h6"
                  textTransform={"uppercase"}
                  fontWeight={
                    formData.lifestyle == "Moderately Active" ? "700" : "400"
                  }
                >
                  Moderately Active
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Moderately Active")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.lifestyle == "Moderately Active"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/shaka-head-03.png"
                  width={"65px"}
                  height={"65px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Lightly Active")
              }
            >
              <Box
                bgcolor={
                  formData.lifestyle == "Lightly Active"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.lifestyle == "Lightly Active"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography
                  variant="h6"
                  textTransform={"uppercase"}
                  fontWeight={
                    formData.lifestyle == "Lightly Active" ? "700" : "400"
                  }
                >
                  Lightly Active
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Lightly Active")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.lifestyle == "Lightly Active"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/shaka-head-02.png"
                  width={"65px"}
                  height={"65px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Sedentary")
              }
            >
              <Box
                bgcolor={
                  formData.lifestyle == "Sedentary"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.lifestyle == "Sedentary"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography
                  variant="h6"
                  textTransform={"uppercase"}
                  fontWeight={formData.lifestyle == "Sedentary" ? "700" : "400"}
                >
                  Sedentary
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "lifestyle", "Sedentary")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.lifestyle == "Sedentary"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/shaka-head-01.png"
                  width={"65px"}
                  height={"65px"}
                />
              </Box>
            </Grid2>
          </Grid2>
        </React.Fragment>
      ),
    },
    {
      number: 3,
      label: "Motivation",
      content: (
        <Stack>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            What motivates you to take the{" "}
            <Box component="span" color="primary.main">
              No Alcohol Challenge?
            </Box>
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.motivation.includes("health") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "motivation", "health")
                  }
                />
              }
              label="Improve physical health"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.motivation.includes("mental") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "motivation", "mental")
                  }
                />
              }
              label="Enhance mental clarity"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.motivation.includes("energy") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "motivation", "energy")
                  }
                />
              }
              label="Boost energy"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.motivation.includes("sleep") ? true : false}
                  onChange={(event) =>
                    handleCheck(event, "motivation", "sleep")
                  }
                />
              }
              label="Improve sleep"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.motivation.includes("stress") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "motivation", "stress")
                  }
                />
              }
              label="Reduce stress"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.motivation.includes("pain") ? true : false}
                  onChange={(event) => handleCheck(event, "motivation", "pain")}
                />
              }
              label="Manage pain or discomfort"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.motivation.includes("anxiety") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "motivation", "anxiety")
                  }
                />
              }
              label="Address anxiety or emotional well-being"
              className="CheckBoxLabel"
            />
          </FormGroup>
          {formData.motivation.length > 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNextCLick}
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
          <img
            src="/harvest/illustration-01.png"
            width={"100px"}
            style={{ alignSelf: "flex-end" }}
          />
        </Stack>
      ),
    },
    {
      number: 4,
      label: "Consumption",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How often do you{" "}
            <Box component="span" color="primary.main">
              consume alcohol?
            </Box>
          </Typography>
          {/* <img src='/harvest/illustration-02.png' width={'80px'} style={{ position: 'absolute', margin: '-30px 0 0 130px' }} /> */}
          <Grid2 container rowSpacing={2} columnSpacing={0} marginTop={"80px"}>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Daily")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.consumption == "Daily"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                bgcolor={
                  formData.consumption == "Daily"
                    ? "secondary.main"
                    : "primary.main"
                }
                height={"85px"}
                borderRadius={"5px 0 0 5px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <img
                  src="/harvest/consumption-01.svg"
                  width={"60px"}
                  height={"60px"}
                  style={{
                    backgroundColor: "#fff",
                    padding: "10px",
                    boxShadow: "4px 4px 4px 0px rgba(0,0,0,0.25)",
                    borderRadius: "5px",
                  }}
                />
              </Box>
            </Grid2>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Daily")
              }
            >
              <Box
                bgcolor={
                  formData.consumption == "Daily"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.consumption == "Daily"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                borderRadius={"0 5px 5px 0"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <Typography variant="h6">Daily</Typography>
              </Box>
            </Grid2>

            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Weekly")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.consumption == "Weekly"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                bgcolor={
                  formData.consumption == "Weekly"
                    ? "secondary.main"
                    : "primary.main"
                }
                height={"85px"}
                borderRadius={"5px 0 0 5px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <img
                  src="/harvest/consumption-02.svg"
                  width={"60px"}
                  height={"60px"}
                  style={{
                    backgroundColor: "#fff",
                    padding: "10px",
                    boxShadow: "4px 4px 4px 0px rgba(0,0,0,0.25)",
                    borderRadius: "5px",
                  }}
                />
              </Box>
            </Grid2>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Weekly")
              }
            >
              <Box
                bgcolor={
                  formData.consumption == "Weekly"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.consumption == "Weekly"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                borderRadius={"0 5px 5px 0"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <Typography variant="h6">Weekly</Typography>
              </Box>
            </Grid2>

            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Occasionally")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.consumption == "Occasionally"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                bgcolor={
                  formData.consumption == "Occasionally"
                    ? "secondary.main"
                    : "primary.main"
                }
                height={"85px"}
                borderRadius={"5px 0 0 5px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <img
                  src="/harvest/consumption-03.svg"
                  width={"60px"}
                  height={"60px"}
                  style={{
                    backgroundColor: "#fff",
                    padding: "10px",
                    boxShadow: "4px 4px 4px 0px rgba(0,0,0,0.25)",
                    borderRadius: "5px",
                  }}
                />
              </Box>
            </Grid2>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Occasionally")
              }
            >
              <Box
                bgcolor={
                  formData.consumption == "Occasionally"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.consumption == "Occasionally"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                borderRadius={"0 5px 5px 0"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <Typography variant="h6">Occasionally</Typography>
              </Box>
            </Grid2>

            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Rarely")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.consumption == "Rarely"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                bgcolor={
                  formData.consumption == "Rarely"
                    ? "secondary.main"
                    : "primary.main"
                }
                height={"85px"}
                borderRadius={"5px 0 0 5px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <img
                  src="/harvest/consumption-04.svg"
                  width={"60px"}
                  height={"60px"}
                  style={{
                    backgroundColor: "#fff",
                    padding: "10px",
                    boxShadow: "4px 4px 4px 0px rgba(0,0,0,0.25)",
                    borderRadius: "5px",
                  }}
                />
              </Box>
            </Grid2>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Rarely")
              }
            >
              <Box
                bgcolor={
                  formData.consumption == "Rarely"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.consumption == "Rarely"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                borderRadius={"0 5px 5px 0"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <Typography variant="h6">Rarely</Typography>
              </Box>
            </Grid2>

            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Never")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.consumption == "Never"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                bgcolor={
                  formData.consumption == "Never"
                    ? "secondary.main"
                    : "primary.main"
                }
                height={"85px"}
                borderRadius={"5px 0 0 5px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <img
                  src="/harvest/consumption-05.svg"
                  width={"60px"}
                  height={"60px"}
                  style={{
                    backgroundColor: "#fff",
                    padding: "10px",
                    boxShadow: "4px 4px 4px 0px rgba(0,0,0,0.25)",
                    borderRadius: "5px",
                  }}
                />
              </Box>
            </Grid2>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "consumption", "Never")
              }
            >
              <Box
                bgcolor={
                  formData.consumption == "Never"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.consumption == "Never"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                borderRadius={"0 5px 5px 0"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
              >
                <Typography variant="h6">Never</Typography>
              </Box>
            </Grid2>
          </Grid2>
        </React.Fragment>
      ),
    },
    {
      number: 5,
      label: "Reason",
      content: (
        <Stack>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            What is your primary reason for{" "}
            <Box component="span" color="primary.main">
              drinking alcohol?
            </Box>
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.reason.includes("socializing") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "reason", "socializing")
                  }
                />
              }
              label="Socializing"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.reason.includes("relaxation") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "reason", "relaxation")
                  }
                />
              }
              label="Relaxation"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.reason.includes("coping") ? true : false}
                  onChange={(event) => handleCheck(event, "reason", "coping")}
                />
              }
              label="Coping with stress or anxiety"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.reason.includes("pain") ? true : false}
                  onChange={(event) => handleCheck(event, "reason", "pain")}
                />
              }
              label="Managing pain or discomfort"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.reason.includes("other") ? true : false}
                  onChange={(event) => handleCheck(event, "reason", "other")}
                />
              }
              label="Other"
              className="CheckBoxLabel"
            />
          </FormGroup>
          {formData.reason.length > 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNextCLick}
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
          <img
            src="/harvest/illustration-03.png"
            width={"80px"}
            style={{ alignSelf: "flex-start" }}
          />
        </Stack>
      ),
    },
    {
      number: 6,
      label: "Relax",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How do you typically{" "}
            <Box component="span" color="primary.main">
              relax or unwind?
            </Box>
          </Typography>
          <Grid2
            container
            rowSpacing={2}
            columnSpacing={{ xs: 1, sm: 2, md: 3 }}
          >
            <Grid2
              size={6}
              onClick={(event) =>
                handleNextValueClick(event, "relax", "Excercise")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.relax == "Excercise"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                height={"125px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img
                  src="/harvest/sloth-05.png"
                  width={"90px"}
                  height={"90px"}
                />
              </Box>
              <Box
                bgcolor={
                  formData.relax == "Excercise"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.relax == "Excercise"
                    ? "primary.main"
                    : "secondary.main"
                }
                minHeight={"50px"}
                display={"flex"}
                flexDirection={"row"}
                justifyContent={"center"}
                alignItems={"center"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography
                  variant="h6"
                  fontWeight={formData.relax == "Excercise" ? "700" : "500"}
                >
                  Excercise
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) =>
                handleNextValueClick(event, "relax", "Meditation")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.relax == "Meditation"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                height={"125px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img
                  src="/harvest/sloth-04.png"
                  width={"90px"}
                  height={"90px"}
                />
              </Box>
              <Box
                bgcolor={
                  formData.relax == "Meditation"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.relax == "Meditation"
                    ? "primary.main"
                    : "secondary.main"
                }
                minHeight={"50px"}
                display={"flex"}
                flexDirection={"row"}
                justifyContent={"center"}
                alignItems={"center"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography
                  variant="h6"
                  fontWeight={formData.relax == "Meditation" ? "700" : "500"}
                >
                  Meditation
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) =>
                handleNextValueClick(event, "relax", "Watching TV")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.relax == "Watching TV"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                height={"125px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img
                  src="/harvest/sloth-01.png"
                  width={"90px"}
                  height={"90px"}
                />
              </Box>
              <Box
                bgcolor={
                  formData.relax == "Watching TV"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.relax == "Watching TV"
                    ? "primary.main"
                    : "secondary.main"
                }
                minHeight={"50px"}
                display={"flex"}
                flexDirection={"row"}
                justifyContent={"center"}
                alignItems={"center"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography
                  variant="h6"
                  fontWeight={formData.relax == "Watching TV" ? "700" : "500"}
                >
                  Watching TV
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) =>
                handleNextValueClick(event, "relax", "Drinking Alcohol")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.relax == "Drinking"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                height={"125px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img
                  src="/harvest/sloth-02.png"
                  width={"90px"}
                  height={"90px"}
                />
              </Box>
              <Box
                bgcolor={
                  formData.relax == "Drinking"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.relax == "Drinking"
                    ? "primary.main"
                    : "secondary.main"
                }
                minHeight={"50px"}
                display={"flex"}
                flexDirection={"row"}
                justifyContent={"center"}
                alignItems={"center"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography
                  variant="h6"
                  fontWeight={
                    formData.relax == "Drinking Alcohol" ? "700" : "500"
                  }
                >
                  Drinking Alcohol
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) =>
                handleNextValueClick(event, "relax", "Reading")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.relax == "Reading"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                height={"125px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img
                  src="/harvest/sloth-03.png"
                  width={"90px"}
                  height={"90px"}
                />
              </Box>
              <Box
                bgcolor={
                  formData.relax == "Reading"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.relax == "Reading"
                    ? "primary.main"
                    : "secondary.main"
                }
                minHeight={"50px"}
                display={"flex"}
                flexDirection={"row"}
                justifyContent={"center"}
                alignItems={"center"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography
                  variant="h6"
                  fontWeight={formData.relax == "Reading" ? "700" : "500"}
                >
                  Reading
                </Typography>
              </Box>
            </Grid2>
            <Grid2
              size={6}
              onClick={(event) => handleNextValueClick(event, "relax", "Other")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.relax == "Other" ? "secondary.main" : "primary.main"
                }
                padding={1}
                height={"125px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 5px 0 0"}
              >
                <img
                  src="/harvest/sloth-06.png"
                  width={"90px"}
                  height={"90px"}
                />
              </Box>
              <Box
                bgcolor={
                  formData.relax == "Other" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={
                  formData.relax == "Other" ? "primary.main" : "secondary.main"
                }
                minHeight={"50px"}
                display={"flex"}
                flexDirection={"row"}
                justifyContent={"center"}
                alignItems={"center"}
                borderRadius={"0 0 5px 5px"}
              >
                <Typography
                  variant="h6"
                  fontWeight={formData.relax == "Other" ? "700" : "500"}
                >
                  Other
                </Typography>
              </Box>
            </Grid2>
          </Grid2>
        </React.Fragment>
      ),
    },
    {
      number: 7,
      label: "Sleep",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How would you describe your current{" "}
            <Box component="span" color="primary.main">
              sleep quality?
            </Box>
          </Typography>
          <Grid2 container rowSpacing={2} columnSpacing={0}>
            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "sleep", "Excellent")
              }
            >
              <Box
                bgcolor={
                  formData.sleep == "Excellent"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.sleep == "Excellent"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography variant="h6">Excellent</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "sleep", "Excellent")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.sleep == "Excellent"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"flex-end"}
                paddingBottom={0}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/Shaka-04.png"
                  width={"80px"}
                  height={"80px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) => handleNextValueClick(event, "sleep", "Good")}
            >
              <Box
                bgcolor={
                  formData.sleep == "Good" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={
                  formData.sleep == "Good" ? "primary.main" : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography variant="h6">Good</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) => handleNextValueClick(event, "sleep", "Good")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.sleep == "Good" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"flex-end"}
                paddingBottom={0}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/Shaka-01.png"
                  width={"80px"}
                  height={"80px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) => handleNextValueClick(event, "sleep", "Fair")}
            >
              <Box
                bgcolor={
                  formData.sleep == "Fair" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={
                  formData.sleep == "Fair" ? "primary.main" : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography variant="h6">Fair</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) => handleNextValueClick(event, "sleep", "Fair")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.sleep == "Fair" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"flex-end"}
                paddingBottom={0}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/Shaka-02.png"
                  width={"80px"}
                  height={"80px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) => handleNextValueClick(event, "sleep", "Poor")}
            >
              <Box
                bgcolor={
                  formData.sleep == "Poor" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={
                  formData.sleep == "Poor" ? "primary.main" : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography variant="h6">Poor</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) => handleNextValueClick(event, "sleep", "Poor")}
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.sleep == "Poor" ? "secondary.main" : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"flex-end"}
                paddingBottom={0}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/Shaka-03.png"
                  width={"80px"}
                  height={"80px"}
                />
              </Box>
            </Grid2>

            <Grid2
              size={8}
              onClick={(event) =>
                handleNextValueClick(event, "sleep", "Very Poor")
              }
            >
              <Box
                bgcolor={
                  formData.sleep == "Very Poor"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={
                  formData.sleep == "Very Poor"
                    ? "primary.main"
                    : "secondary.main"
                }
                height={"85px"}
                display={"flex"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"5px 0 0 5px"}
              >
                <Typography variant="h6">Very Poor</Typography>
              </Box>
            </Grid2>
            <Grid2
              size={4}
              onClick={(event) =>
                handleNextValueClick(event, "sleep", "Very Poor")
              }
            >
              <Box
                border={"3px solid"}
                borderColor={
                  formData.sleep == "Very Poor"
                    ? "secondary.main"
                    : "primary.main"
                }
                padding={1}
                color={"#ffffff"}
                height={"85px"}
                display={"flex"}
                alignItems={"center"}
                flexDirection={"column"}
                justifyContent={"center"}
                borderRadius={"0 5px 5px 0"}
              >
                <img
                  src="/harvest/Shaka-05.png"
                  width={"80px"}
                  height={"80px"}
                />
              </Box>
            </Grid2>
          </Grid2>
        </React.Fragment>
      ),
    },
    {
      number: 8,
      label: "Stress",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you{" "}
            <Box component="span" color="primary.main">
              experience stress
            </Box>{" "}
            frequently?
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.stress == "Yes, daily"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.stress == "Yes, daily"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "stress", "Yes, daily")
              }
              display={"flex"}
              flexDirection={"row"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, daily</Typography>
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
            </Box>
            <Box
              bgcolor={
                formData.stress == "A few times a week"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.stress == "A few times a week"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "stress", "A few times a week")
              }
              display={"flex"}
              flexDirection={"row"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">A few times a week</Typography>
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
            </Box>
            <Box
              bgcolor={
                formData.stress == "Occasionally"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.stress == "Occasionally"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "stress", "Occasionally")
              }
              display={"flex"}
              flexDirection={"row"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Occasionally</Typography>
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
            </Box>
            <Box
              bgcolor={
                formData.stress == "Rarely" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.stress == "Rarely" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "stress", "Rarely")
              }
              display={"flex"}
              flexDirection={"row"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Rarely</Typography>
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
            </Box>
            <Box
              bgcolor={
                formData.stress == "Never" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.stress == "Never" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "stress", "Never")
              }
              display={"flex"}
              flexDirection={"row"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Never</Typography>
              <img
                src="/harvest/fallback.svg"
                width={"20px"}
                style={{ marginLeft: "5px" }}
              />
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 9,
      label: "Gain",
      content: (
        <Stack>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            What do you{" "}
            <Box component="span" color="primary.main">
              hope to gain
            </Box>{" "}
            from this challenge?
          </Typography>
          <img
            src="/harvest/illustration-06.png"
            width={"100px"}
            style={{ position: "absolute", marginTop: "20px", right: "-20px" }}
          />
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.gain.includes("health") ? true : false}
                  onChange={(event) => handleCheck(event, "gain", "health")}
                />
              }
              label="Better health"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.gain.includes("focus") ? true : false}
                  onChange={(event) => handleCheck(event, "gain", "focus")}
                />
              }
              label="Improved focus"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.gain.includes("weight") ? true : false}
                  onChange={(event) => handleCheck(event, "gain", "weight")}
                />
              }
              label="Weight loss"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.gain.includes("anxiety") ? true : false}
                  onChange={(event) => handleCheck(event, "gain", "anxiety")}
                />
              }
              label="Reduced anxiety or stress"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.gain.includes("sleep") ? true : false}
                  onChange={(event) => handleCheck(event, "gain", "sleep")}
                />
              }
              label="Better sleep"
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.gain.includes("pain") ? true : false}
                  onChange={(event) => handleCheck(event, "gain", "pain")}
                />
              }
              label="Relief from pain or physical discomfort"
              className="CheckBoxLabel"
            />
          </FormGroup>
          {formData.gain.length > 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNextCLick}
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
        </Stack>
      ),
    },
    {
      number: 10,
      label: "Impact",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            <Box component="span" color="primary.main">
              Do you feel that alcohol impacts
            </Box>{" "}
            your emotional state or mental health?
          </Typography>
          <img
            src="/harvest/illustration-05.png"
            width={"100px"}
            style={{ position: "absolute", left: "-30px", marginTop: "-100px" }}
          />
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.impact == "Yes, significantly"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.impact == "Yes, significantly"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "impact", "Yes, significantly")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, significantly</Typography>
            </Box>
            <Box
              bgcolor={
                formData.impact == "Yes, somewhat"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.impact == "Yes, somewhat"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "impact", "Yes, somewhat")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, somewhat</Typography>
            </Box>
            <Box
              bgcolor={
                formData.impact == "No" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.impact == "No" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "impact", "No")}
              borderRadius={"5px"}
            >
              <Typography variant="h6">No</Typography>
            </Box>
            <Box
              bgcolor={
                formData.impact == "Not Sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.impact == "Not Sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "impact", "Not Sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not Sure</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 11,
      label: "Mood",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How often do you struggle with maintaining{" "}
            <Box component="span" color="primary.main">
              a positive mood?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.mood == "Often" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.mood == "Often" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "mood", "Often")}
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/mood-01.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Often</Typography>
            </Box>
            <Box
              bgcolor={
                formData.mood == "Sometimes" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.mood == "Sometimes" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "mood", "Sometimes")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/mood-02.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Sometimes</Typography>
            </Box>
            <Box
              bgcolor={
                formData.mood == "Rarely" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.mood == "Rarely" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "mood", "Rarely")}
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/mood-03.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Rarely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.mood == "Never" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.mood == "Never" ? "primary.main" : "secondary.main"
              }
              onClick={(event) => handleNextValueClick(event, "mood", "Never")}
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/mood-04.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Never</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 12,
      label: "Fatigue",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            What time of day do you feel{" "}
            <Box component="span" color="primary.main">
              most fatigued or low in energy?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.fatigue == "Morning"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.fatigue == "Morning"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "fatigue", "Morning")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/fatigue-01.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Morning</Typography>
            </Box>
            <Box
              bgcolor={
                formData.fatigue == "Afternoon"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.fatigue == "Afternoon"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "fatigue", "Afternoon")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/fatigue-02.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Afternoon</Typography>
            </Box>
            <Box
              bgcolor={
                formData.fatigue == "Evening"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.fatigue == "Evening"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "fatigue", "Evening")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/fatigue-03.svg"
                width={"30px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Evening</Typography>
            </Box>
            <Box
              bgcolor={
                formData.fatigue == "Night" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.fatigue == "Night" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "fatigue", "Night")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"flex-start"}
              borderRadius={"5px"}
            >
              <img
                src="/harvest/fatigue-04.svg"
                width={"22px"}
                style={{ marginRight: "10px" }}
              />
              <Typography variant="h6">Night</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 13,
      label: "Concentration",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Have you ever experienced{" "}
            <Box component="span" color="primary.main">
              difficulty concentrating
            </Box>{" "}
            or{" "}
            <Box component="span" color="primary.main">
              staying focused?
            </Box>
          </Typography>
          <img
            src="/harvest/illustration-09.png"
            width={"40px"}
            style={{ position: "absolute", right: "10px", marginTop: "-30px" }}
          />
          <Stack spacing={1} marginTop={"60px"}>
            <Box
              bgcolor={
                formData.concentration == "Yes"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.concentration == "Yes"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "concentration", "Yes")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes</Typography>
            </Box>
            <Box
              bgcolor={
                formData.concentration == "Often"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.concentration == "Often"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "concentration", "Often")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Often</Typography>
            </Box>
            <Box
              bgcolor={
                formData.concentration == "Occasionally"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.concentration == "Occasionally"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "concentration", "Occasionally")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Occasionally</Typography>
            </Box>
            <Box
              bgcolor={
                formData.concentration == "Rarely"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.concentration == "Rarely"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "concentration", "Rarely")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Rarely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.concentration == "Never"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.concentration == "Never"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "concentration", "Never")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Never</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 14,
      label: "Discomfort",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you experience any physical discomfort that{" "}
            <Box component="span" color="primary.main">
              worsens after drinking?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.discomfort == "Yes, often"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.discomfort == "Yes, often"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "discomfort", "Yes, often")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, often</Typography>
            </Box>
            <Box
              bgcolor={
                formData.discomfort == "Sometimes"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.discomfort == "Sometimes"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "discomfort", "Sometimes")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Sometimes</Typography>
            </Box>
            <Box
              bgcolor={
                formData.discomfort == "No" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.discomfort == "No" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "discomfort", "No")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">No</Typography>
            </Box>
            <Box
              bgcolor={
                formData.discomfort == "Not sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.discomfort == "Not sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "discomfort", "Not sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not sure</Typography>
            </Box>
            <img
              src="/harvest/illustration-08.png"
              width={"80px"}
              style={{ alignSelf: "flex-end" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 15,
      label: "Well-being",
      content: (
        <Stack>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Which aspect of your well-being do you{" "}
            <Box component="span" color="primary.main">
              want to focus on most?
            </Box>
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.wellbeing.includes("sleep") ? true : false}
                  onChange={(event) => handleCheck(event, "wellbeing", "sleep")}
                />
              }
              label={
                <React.Fragment>
                  Sleep quality{" "}
                  <img src="/harvest/wellbeing-01.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.wellbeing.includes("energy") ? true : false}
                  onChange={(event) =>
                    handleCheck(event, "wellbeing", "energy")
                  }
                />
              }
              label={
                <React.Fragment>
                  Energy levels{" "}
                  <img src="/harvest/wellbeing-02.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.wellbeing.includes("mental") ? true : false}
                  onChange={(event) =>
                    handleCheck(event, "wellbeing", "mental")
                  }
                />
              }
              label={
                <React.Fragment>
                  Mental clarity{" "}
                  <img src="/harvest/wellbeing-03.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.wellbeing.includes("pain") ? true : false}
                  onChange={(event) => handleCheck(event, "wellbeing", "pain")}
                />
              }
              label={
                <React.Fragment>
                  Pain relief{" "}
                  <img src="/harvest/wellbeing-04.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.wellbeing.includes("anxiety") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "wellbeing", "anxiety")
                  }
                />
              }
              label={
                <React.Fragment>
                  Managing anxiety{" "}
                  <img src="/harvest/wellbeing-05.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.wellbeing.includes("other") ? true : false}
                  onChange={(event) => handleCheck(event, "wellbeing", "other")}
                />
              }
              label={
                <React.Fragment>
                  Other <img src="/harvest/wellbeing-06.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
          </FormGroup>
          {formData.wellbeing.length > 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNextCLick}
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
        </Stack>
      ),
    },
    {
      number: 16,
      label: "Pain Duration",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How long have you been{" "}
            <Box component="span" color="primary.main">
              experiencing pain?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.painduration == "Less than 3 months"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.painduration == "Less than 3 months"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "painduration",
                  "Less than 3 months"
                )
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Less than 3 months</Typography>
              <img src="/harvest/pain-duration-01.png" width={"50px"} />
            </Box>
            <Box
              bgcolor={
                formData.painduration == "3-6 months"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.painduration == "3-6 months"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "painduration", "3-6 months")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">3-6 months</Typography>
              <img src="/harvest/pain-duration-02.png" width={"50px"} />
            </Box>
            <Box
              bgcolor={
                formData.painduration == "Over 6 months"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.painduration == "Over 6 months"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "painduration", "Over 6 months")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Over 6 months</Typography>
              <img src="/harvest/pain-duration-03.png" width={"50px"} />
            </Box>
            <Box
              bgcolor={
                formData.painduration == "Not applicable"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.painduration == "Not applicable"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "painduration", "Not applicable")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not applicable</Typography>
              <img src="/harvest/pain-duration-04.png" width={"50px"} />
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 17,
      label: "Pain affect",
      content: (
        <Stack>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How does pain affect your{" "}
            <Box component="span" color="primary.main">
              daily ?
            </Box>
          </Typography>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.painaffect.includes("discomfort") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "painaffect", "discomfort")
                  }
                />
              }
              label={
                <React.Fragment>
                  Minor discomfort{" "}
                  <img src="/harvest/pain-affect-01.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.painaffect.includes("productivity") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "painaffect", "productivity")
                  }
                />
              }
              label={
                <React.Fragment>
                  Affects productivity{" "}
                  <img src="/harvest/pain-affect-02.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.painaffect.includes("activity") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "painaffect", "activity")
                  }
                />
              }
              label={
                <React.Fragment>
                  Limits physical activity{" "}
                  <img src="/harvest/pain-affect-03.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={
                    formData.painaffect.includes("lifestyle") ? true : false
                  }
                  onChange={(event) =>
                    handleCheck(event, "painaffect", "lifestyle")
                  }
                />
              }
              label={
                <React.Fragment>
                  Significantly impacts lifestyle{" "}
                  <img src="/harvest/pain-affect-04.svg" width={"20px"} />
                </React.Fragment>
              }
              className="CheckBoxLabel"
            />
          </FormGroup>
          {formData.painaffect.length > 0 ? (
            <Button
              variant="contained"
              color="primary"
              onClick={handleNextCLick}
              style={{ marginTop: "20px" }}
            >
              Continue
            </Button>
          ) : null}
        </Stack>
      ),
    },
    {
      number: 18,
      label: "Pain Treatment",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            What treatments have you{" "}
            <Box component="span" color="primary.main">
              tried to manage this pain?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.paintreatment == "Painkillers"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.paintreatment == "Painkillers"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "paintreatment", "Painkillers")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Painkillers</Typography>
              <img src="/harvest/pain-treat-02.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.paintreatment == "Physiotherapy"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.paintreatment == "Physiotherapy"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "paintreatment", "Physiotherapy")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Physiotherapy</Typography>
              <img src="/harvest/pain-treat-01.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.paintreatment == "Lifestyle changes"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.paintreatment == "Lifestyle changes"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "paintreatment",
                  "Lifestyle changes"
                )
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Lifestyle changes</Typography>
              <img src="/harvest/pain-treat-05.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.paintreatment == "Psychological therapy"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.paintreatment == "Psychological therapy"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "paintreatment",
                  "Psychological therapy"
                )
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Psychological therapy</Typography>
              <img src="/harvest/pain-treat-04.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.paintreatment == "None"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.paintreatment == "None"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "paintreatment", "None")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">None</Typography>
              <img src="/harvest/pain-treat-03.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.paintreatment == "Other"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.paintreatment == "Other"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "paintreatment", "Other")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Other</Typography>
              <img src="/harvest/pain-treat-06.svg" width={"30px"} />
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 19,
      label: "Manage Pain",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you feel that alcohol was used to{" "}
            <Box component="span" color="primary.main">
              manage this pain?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.managepain == "Yes, often"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.managepain == "Yes, often"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "managepain", "Yes, often")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, often</Typography>
            </Box>
            <Box
              bgcolor={
                formData.managepain == "Sometimes"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.managepain == "Sometimes"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "managepain", "Sometimes")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Sometimes</Typography>
            </Box>
            <Box
              bgcolor={
                formData.managepain == "No" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.managepain == "No" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "managepain", "No")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">No</Typography>
            </Box>
            <Box
              bgcolor={
                formData.managepain == "Not sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.managepain == "Not sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "managepain", "Not sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not sure</Typography>
            </Box>
            <img
              src="/harvest/illustration-010.png"
              width={"80px"}
              style={{ alignSelf: "flex-start" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 20,
      label: "Alternative Pain Management",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Would you be interested in exploring{" "}
            <Box component="span" color="primary.main">
              alternative, non-alcohol-based
            </Box>{" "}
            pain management solutions?
          </Typography>
          {/* <img src='/harvest/illustration-013.png' width={'70px'} style={{ position: 'absolute', marginTop: '-80px', right: '10px' }} /> */}
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.alternativepain == "Yes, definitely"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.alternativepain == "Yes, definitely"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "alternativepain",
                  "Yes, definitely"
                )
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, definitely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.alternativepain == "Maybe"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.alternativepain == "Maybe"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "alternativepain", "Maybe")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Maybe</Typography>
            </Box>
            <Box
              bgcolor={
                formData.alternativepain == "Not sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.alternativepain == "Not sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "alternativepain", "Not sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not sure</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 21,
      label: "Anxious",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            How often do you feel{" "}
            <Box component="span" color="primary.main">
              anxious or worried?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.anxious == "Daily" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.anxious == "Daily" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxious", "Daily")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Daily</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxious == "week" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.anxious == "week" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxious", "week")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Few times a week</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxious == "Occasionally"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxious == "Occasionally"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxious", "Occasionally")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Occasionally</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxious == "Rarely" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.anxious == "Rarely" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxious", "Rarely")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Rarely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxious == "Never" ? "secondary.main" : "primary.main"
              }
              padding={2}
              color={
                formData.anxious == "Never" ? "primary.main" : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxious", "Never")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Never</Typography>
            </Box>
            <img
              src="/harvest/illustration-016.png"
              width={"80px"}
              style={{ alignSelf: "flex-start" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 22,
      label: "Control Anxiety",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you find it difficult to{" "}
            <Box component="span" color="primary.main">
              control or manage your anxiety?
            </Box>
          </Typography>
          {/* <img src='/harvest/illustration-014.png' width={'70px'} style={{ position: 'absolute', marginTop: '-60px', right: '10px' }} /> */}
          <Stack spacing={1} marginTop={"60px"}>
            <Box
              bgcolor={
                formData.controlanxiety == "Yes, often"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.controlanxiety == "Yes, often"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "controlanxiety", "Yes, often")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, often</Typography>
            </Box>
            <Box
              bgcolor={
                formData.controlanxiety == "Sometimes"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.controlanxiety == "Sometimes"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "controlanxiety", "Sometimes")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Sometimes</Typography>
            </Box>
            <Box
              bgcolor={
                formData.controlanxiety == "No"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.controlanxiety == "No"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "controlanxiety", "No")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">No</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 23,
      label: "Anxiety Treatment",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Have you tried any therapies or treatments{" "}
            <Box component="span" color="primary.main">
              to manage anxiety?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.anxietytreatment == "Yes, medications"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietytreatment == "Yes, medications"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "anxietytreatment",
                  "Yes, medications"
                )
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, medications</Typography>
              <img src="/harvest/anxiety-manage-01.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.anxietytreatment == "Yes, counseling"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietytreatment == "Yes, counseling"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "anxietytreatment",
                  "Yes, counseling"
                )
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, counseling</Typography>
              <img src="/harvest/anxiety-manage-02.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.anxietytreatment == "Yes, lifestyle changes"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietytreatment == "Yes, lifestyle changes"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "anxietytreatment",
                  "Yes, lifestyle changes"
                )
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, lifestyle changes</Typography>
              <img src="/harvest/anxiety-manage-03.svg" width={"30px"} />
            </Box>
            <Box
              bgcolor={
                formData.anxietytreatment == "No"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietytreatment == "No"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxietytreatment", "No")
              }
              display={"flex"}
              flexDirection={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
              borderRadius={"5px"}
            >
              <Typography variant="h6">No</Typography>
              <img src="/harvest/anxiety-manage-04.svg" width={"30px"} />
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 24,
      label: "Manage Anxiety",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Did alcohol help you{" "}
            <Box component="span" color="primary.main">
              manage or reduce anxiety?
            </Box>
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.anxietymanage == "Yes, significantly"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietymanage == "Yes, significantly"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "anxietymanage",
                  "Yes, significantly"
                )
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, significantly</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxietymanage == "A little"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietymanage == "A little"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxietymanage", "A little")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">A little</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxietymanage == "No"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietymanage == "No"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxietymanage", "No")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">No</Typography>
            </Box>
            <Box
              bgcolor={
                formData.anxietymanage == "Not sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.anxietymanage == "Not sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "anxietymanage", "Not sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not sure</Typography>
            </Box>
            <img
              src="/harvest/illustration-015.png"
              width={"80px"}
              style={{ alignSelf: "flex-start" }}
            />
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 25,
      label: "Alternative Anxiety Management",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Are you open to{" "}
            <Box component="span" color="primary.main">
              exploring alternative approaches
            </Box>{" "}
            for managing anxiety?
          </Typography>
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.alternativeanxiety == "Yes, definitely"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.alternativeanxiety == "Yes, definitely"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(
                  event,
                  "alternativeanxiety",
                  "Yes, definitely"
                )
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, definitely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.alternativeanxiety == "Maybe"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.alternativeanxiety == "Maybe"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "alternativeanxiety", "Maybe")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Maybe</Typography>
            </Box>
            <Box
              bgcolor={
                formData.alternativeanxiety == "Not sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.alternativeanxiety == "Not sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "alternativeanxiety", "Not sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not sure</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 26,
      label: "Trouble Sleeping",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Do you have trouble{" "}
            <Box component="span" color="primary.main">
              falling or staying asleep?
            </Box>
          </Typography>
          <img
            src="/harvest/illustration-011.png"
            width={"50px"}
            style={{ position: "absolute", marginTop: "-40px", right: "20px" }}
          />
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.sleeptrouble == "Yes, often"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.sleeptrouble == "Yes, often"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "sleeptrouble", "Yes, often")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, often</Typography>
            </Box>
            <Box
              bgcolor={
                formData.sleeptrouble == "Occasionally"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.sleeptrouble == "Occasionally"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "sleeptrouble", "Occasionally")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Occasionally</Typography>
            </Box>
            <Box
              bgcolor={
                formData.sleeptrouble == "Rarely"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.sleeptrouble == "Rarely"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "sleeptrouble", "Rarely")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Rarely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.sleeptrouble == "Never"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.sleeptrouble == "Never"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "sleeptrouble", "Never")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Never</Typography>
            </Box>
          </Stack>
        </React.Fragment>
      ),
    },
    {
      number: 27,
      label: "Help Sleeping",
      content: (
        <React.Fragment>
          <Typography
            variant="h4"
            fontWeight={"800"}
            textTransform={"uppercase"}
            color="secondary.main"
          >
            Would you like to{" "}
            <Box component="span" color="primary.main">
              explore natural solutions
            </Box>{" "}
            to support better sleep?
          </Typography>
          <img
            src="/harvest/illustration-05.png"
            width={"70px"}
            style={{ position: "absolute", marginTop: "-60px", right: "20px" }}
          />
          <Stack spacing={1}>
            <Box
              bgcolor={
                formData.helpsleeping == "Yes, definitely"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.helpsleeping == "Yes, definitely"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "helpsleeping", "Yes, definitely")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Yes, definitely</Typography>
            </Box>
            <Box
              bgcolor={
                formData.helpsleeping == "Maybe"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.helpsleeping == "Maybe"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "helpsleeping", "Maybe")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Maybe</Typography>
            </Box>
            <Box
              bgcolor={
                formData.helpsleeping == "Not sure"
                  ? "secondary.main"
                  : "primary.main"
              }
              padding={2}
              color={
                formData.helpsleeping == "Not sure"
                  ? "primary.main"
                  : "secondary.main"
              }
              onClick={(event) =>
                handleNextValueClick(event, "helpsleeping", "Not sure")
              }
              borderRadius={"5px"}
            >
              <Typography variant="h6">Not sure</Typography>
            </Box>
            {formData.helpsleeping != "" ? (
              <Button
                type="button"
                onClick={handleSubmit}
                fullWidth
                variant="contained"
                color="primary"
                style={{ marginTop: "20px" }}
              >
                Submit
              </Button>
            ) : null}
          </Stack>
        </React.Fragment>
      ),
    },
  ];

  return (
    <>
      {isLoading && <LoadingScreen />}
      <form onSubmit={(e) => e.preventDefault()}>
        <Stack gap={2}>
          <MobileStepper
            variant="dots"
            steps={steps.length}
            position="static"
            activeStep={activeStep}
            sx={{ backgroundColor: "#000000" }}
            nextButton={
              <div>
                <Typography fontSize={'0.8em'} color='#ffffff'>{activeStep + 1} | {steps.length}</Typography>

              </div>
            }
            backButton={
              <IconButton className='StepperNextButton' color="primary" onClick={handleBack} disabled={activeStep === 0}>
                <ChevronLeftIcon />
              </IconButton>
            }
          />
          <center>
            <Box >
              {steps[activeStep].content}
            </Box>
          </center>
        </Stack>
      </form>
    </>
  );
}

export default FormQuestionaire;