import * as React from "react";
import { useEffect } from "react";
import { Box, useTheme, useMediaQuery, Typography, Button } from '@mui/material';
import { ThemeProvider } from '@mui/material/styles';
import harvestTheme from '../../../styles/harvest/theme';
import '../../../styles/harvest/main.css';
import Footer from "../layouts/Footer";
import Header from "../layouts/Header";

function ThankYouOther() {

    useEffect(() => {
        document.title = 'No Alcohol Challenge - Harvest';

        let link: HTMLLinkElement | null = document.querySelector("link[rel~='icon']");
        if (!link) {
            link = document.createElement('link');
            link.rel = 'icon';
            document.getElementsByTagName('head')[0].appendChild(link);
        }
        link.href = '/harvest-favicon.png';

    }, []);

    const theme = useTheme()
    const isDesktopOrMobile = useMediaQuery(theme.breakpoints.up('sm'));

    return (
        <ThemeProvider theme={harvestTheme}>
        <div>
            <Header />
            <div className={isDesktopOrMobile ? 'thankyouBanner' : 'thankyouBannerMobile'} style={{backgroundImage: 'url(/harvest/thankyoubanner-02.webp)'}}>
                <img src='/harvest/thankyoulegend.svg' style={{marginBottom: "20px"}} />
            </div>
            <center>
                <div style={isDesktopOrMobile ? { width: '60vw' } : { width: '95vw' }}>
                    <Typography m={5} ml={2} mr={2} variant="body1">Thank you for <Box component={'span'} color='primary.main' fontWeight={'700'}>completing your wellness profile!</Box></Typography>
                    <Typography m={5} ml={2} mr={2} variant="body1">You’re all set to join the No Alcohol Challenge and begin your journey toward better health and well-being.</Typography>
                    <Button variant="contained" color='primary' style={{margin:'40px'}}>
                        Join the Challenge Now
                    </Button>
                </div>
            </center>
            <Footer />
        </div>
        </ThemeProvider>
    );
}

export default ThankYouOther;