import React, { useState } from 'react';
import { Stack, Box, Typography, Button, Toolbar, MobileStepper } from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { CardElement, useElements, useStripe } from '@stripe/react-stripe-js';
import Grid from "@mui/material/Grid2";
import LoadingScreen from '../../../utils/loading-screen';
import axios from 'axios';
import { useSnackbar } from 'notistack';
import { useFlow } from '../../../hooks/flow-controller';

const PaymentForm: React.FC = () => {
    const elements = useElements();
    const stripe = useStripe();
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null);
    const { enqueueSnackbar } = useSnackbar()
    const { user } = useFlow()

    const style = {
        base: {
            iconColor: '#007F00',
            color: 'rgba(0, 0, 0, 0.87)',
            fontWeight: '500',
            fontFamily: 'Roboto, Open Sans, Segoe UI, sans-serif',
            fontSize: '16px',
            fontSmoothing: 'antialiased',
            '::placeholder': {
                color: 'rgba(0, 0, 0, 0.87)',
            },
        },
        invalid: {
            iconColor: '#F00',
            color: '#F00',
        },
    };

    const handleSubmit = async () => {
        setIsLoading(true)
        setError(null);

        try {
            const response = await axios.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/create-hold`, { user }, { withCredentials: true });
            const clientSecret = response.data.data.clientSecret
            if (!stripe || !elements) {
                setError('Stripe has not loaded correctly. Please refresh the page.');
                setIsLoading(false);
                return;
            }

            const card = elements.getElement(CardElement);
            if (!card) {
                setError('Card details are not available.');
                setIsLoading(false);
                return;
            }

            if (!clientSecret) {
                setError('Failed to create payment hold. Please try again later.');
                setIsLoading(false);
                return;
            }

            const result = await stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    card,
                },
            });

            if (result.error) {
                setError(result.error.message || 'Payment authorization failed. Please try again.');
                enqueueSnackbar('Payment authorization failed. Please try again.', {
                    variant: 'error'
                })
            } else if (result.paymentIntent?.status === 'requires_capture') {
                // result.paymentIntent.id
                enqueueSnackbar('Payment authorization successfully created.', {
                    variant: 'success'
                })

                axios.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`, { user }, { withCredentials: true })
                    .then((response) => {
                        const encryptedLeadID = response.data.data.leadID
                        const baseUrl = 'https://doctor.zenith.clinic/schedule';
                        const url = `${baseUrl}?token=${encryptedLeadID}`;
                        window.location.href = url;
                    })
            } else {
                setError('Unexpected payment status. Please contact support.');
                enqueueSnackbar('Unexpected payment status. Please contact support.', {
                    variant: 'error'
                })
            }
        } catch (e) {
            setIsLoading(false);
            setError('An error occurred. Please try again later.');
            enqueueSnackbar('An error occurred. Please try again later.', {
                variant: 'error'
            })
            throw e
        }
    };

    return (
        <>
            {isLoading && <LoadingScreen />}

            <Stack alignItems={'center'} gap={2}>
                <Grid container direction={'column'} padding={'20px 0'} sx={{'borderRadius':'10px', width: "100%"}} boxShadow={'0px 3px 3px 0px rgba(0,0,0,0.25)'}>
                    <Grid>
                        <Typography sx={{ fontSize: '38px', fontWeight: 'bold', lineHeight: '1em', color: 'green' }}>
                        Pre-Screening
                        </Typography>
                    </Grid>
                    </Grid>

                    <Grid container direction={'column'} sx={{ backgroundColor: 'green', borderRadius: 2, width: '100%', p: 2 }} boxShadow={'0px 3px 3px 0px rgba(0,0,0,0.25)'}>
                    <Grid>
                        <Typography sx={{ fontSize: '20px', fontWeight: 'bold', lineHeight: '1em', color: 'white' }}>
                        Consults Are Free
                        </Typography>
                    </Grid>
                </Grid>

                <Typography sx={{ fontSize: '14px' }} gutterBottom>
                    You only pay if you miss your appointment.
                </Typography>
                <Grid container sx={{ backgroundColor: 'green', width: "100%", p: 2, borderRadius: 2 }} direction={'column'}>
                    <Grid sx={{ width: '100%' }}>
                        <Typography variant="body1" gutterBottom align='left' sx={{ color: 'white', fontSize: '14px', fontWeight: 400 }}>
                            This is an authorised hold only. You will not be charged unless you cancel within 24 hours or miss your consult time.
                        </Typography>
                    </Grid>

                    <Grid sx={{ width: '100%', backgroundColor: 'white', borderRadius: 3, mt: 2 }} container alignItems={'center'}>
                        <Grid sx={{ ml: 2 }}>
                            <Typography sx={{ fontSize: '20px', fontWeight: 'bold', color: 'green' }}>Total</Typography>
                        </Grid>
                        <Toolbar sx={{ flexGrow: 1 }} />
                        <Grid sx={{ mr: 2 }}>
                            <Typography sx={{ fontSize: '20px', fontWeight: 'bold', color: '#535353' }}>$29.99</Typography>
                        </Grid>
                    </Grid>

                    <Box alignContent={'center'} sx={{ mt: 2, width: '100%' }}>
                        <Box sx={{ p: 2, border: '1px solid rgba(0, 0, 0, 0.87)', backgroundColor: 'white', borderRadius: 3, width: '100%' }}>
                            <CardElement options={{ style }} />
                        </Box>
                        {error &&
                            <Grid sx={{ width: '100%', mt: 1, backgroundColor: 'white', borderRadius: 2, p: 1 }}>
                                <Typography color="error" align='left' sx={{ fontSize: '14px', fontWeight: 'bold' }}>
                                    {error}
                                </Typography>
                            </Grid>
                        }
                    </Box>
                </Grid>
                <Button variant='contained' onClick={handleSubmit} fullWidth>
                    Confirm Booking
                </Button>
                
                <Typography sx={{ fontSize: '12px', color: 'grey', mt: 3 }}>
                Any information you provide today is confidential and compliant with the Medical Board of Australia Good Medical Practice code, RACGP Standards of General Practice and our Medical Confidentiality Duty of Conduct for doctors in Australia, which means we protect your privacy and right to confidentiality.
                </Typography>
            </Stack>
        </>
    );
};

export default PaymentForm;
