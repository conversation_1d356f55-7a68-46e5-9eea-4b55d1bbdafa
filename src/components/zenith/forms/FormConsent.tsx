import { useState, useEffect, useRef } from "react";
import {
  Box,
  Button,
  Stack,
  <PERSON><PERSON><PERSON>,
  Checkbox,
  FormControlLabel,
  ThemeProvider,
  TextField,
  Tooltip,
  IconButton,
  Divider,
} from "@mui/material";
import styles from "../../../css/consent.module.css";
// We use window.location.href for navigation instead of useNavigate
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import axios from 'axios';
import LoadingScreen from "../../../utils/loading-screen";
// No longer need auth provider as we're using URL parameters
// import { useAuth } from "../../../hooks/auth-provider";

function FormConsent() {
  const [isLoading, setIsLoading] = useState(false);
  // We use window.location.href for direct navigation
  const { enqueueSnackbar } = useSnackbar();

  // Define types for user details and request payload
  interface UserDetails {
    fullName?: string;
    firstname?: string;
    lastname?: string;
    email?: string;
    zohoID?: string;
    leadId?: string;
    lead_id?: string;
    [key: string]: any; // Allow for other properties
  }

  interface ConsentFormPayload {
    // User data
    userId: string;
    userFullName: string;
    userEmail: string;
    userDetails?: UserDetails;

    // Consent form data
    voluntary_consent: boolean | null;
    legally_competent: boolean | null;
    sufficient_information: boolean | null;
    understanding_risks: boolean | null;
    medical_cannabis_unapproved: boolean | null;
    illegal_prescription: boolean | null;
    drug_interactions: boolean | null;
    no_use_while_treated: boolean | null;
    illegal_to_minors: boolean | null;
    signature: string;

    // Evidence data
    signatureEvidence: {
      ipAddress: string;
      deviceInfo: DeviceInfo;
    };

    // Zoho data
    updateZoho: boolean;
    zohoID?: string;
    contactId?: string;

    // Authentication flag
    isPublicSubmission?: boolean;
  }

  interface FetchStatus {
    email: boolean;
    userDetails: boolean;
    consentStatus: boolean;
  }

  const [userEmail, setUserEmail] = useState<string>('');
  const [contactId, setContactId] = useState<string>('');
  const [userName, setUserName] = useState<string>('');
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [fetchStatus, setFetchStatus] = useState<FetchStatus>({
    email: false,
    userDetails: false,
    consentStatus: false
  });

  // Add refs to track fetch status
  const userDetailsFetchedRef = useRef(false);
  const emailFetchedRef = useRef(false);
  const consentStatusCheckedRef = useRef(false);

  // Single initialization ref
  const hasInitialized = useRef(false);

  useEffect(() => {
    const initialize = async () => {
      // Prevent multiple initializations
      if (hasInitialized.current) return;
      hasInitialized.current = true;

      try {
        setIsLoading(true);

        // Get contactId from URL path
        const pathSegments = window.location.pathname.split('/');
        const contactIdFromPath = pathSegments[pathSegments.length - 1];

        if (!contactIdFromPath) {
          enqueueSnackbar("Contact ID is missing from the URL", {
            variant: "error",
          });
          return;
        }

        // Ensure contactId starts with 'p'
        const formattedContactId = contactIdFromPath.startsWith('p') ? contactIdFromPath : `p${contactIdFromPath}`;
        setContactId(formattedContactId);

        // Step 1: Get email from contact ID
        const emailResponse = await axiosInstance.get(`/zoho/v1.0/contacts/${contactIdFromPath}`);
        if (!emailResponse.data?.success || !emailResponse.data?.email) {
          enqueueSnackbar("Could not retrieve email for this contact. Please try again or contact support.", {
            variant: "error",
          });
          return;
        }

        const email = emailResponse.data.email;
        setUserEmail(email);

        // Step 2: Get user details
        const userDetailsResponse = await axiosInstance.get(
          `/funnel/v1.0/patient/details?email=${encodeURIComponent(email)}`
        );

        if (userDetailsResponse.data?.success && userDetailsResponse.data?.data) {
          const userData = userDetailsResponse.data.data;
          setUserDetails(userData);

          // Set user name
          if (userData.fullName) {
            setUserName(userData.fullName);
          } else if (userData.firstname || userData.lastname) {
            setUserName(`${userData.firstname || ''} ${userData.lastname || ''}`.trim());
          }

          // Check if consent form is already completed
          if (userData.consent_form_completed === true) {
            setConsentFormCompleted(true);
            enqueueSnackbar("You have already completed the consent form", {
              variant: "info",
            });
            return;
          }
        }

        // Step 3: Check consent status only if not already determined
        try {
          const consentResponse = await axiosInstance.get(
            `/funnel/v1.0/patient/consent-form/status-by-email?email=${encodeURIComponent(email)}`
          );

          if (consentResponse.data?.success && consentResponse.data?.data?.consent_form_completed === true) {
            setConsentFormCompleted(true);
            enqueueSnackbar("You have already completed the consent form", {
              variant: "info",
            });
          }
        } catch (error) {
          // Silently handle consent check errors as this is a secondary check
          console.error('Error checking consent status:', error);
        }

      } catch (error) {
        console.error('Error during initialization:', error);
        enqueueSnackbar("An error occurred while loading your information. Please try again or contact support.", {
          variant: "error",
        });
      } finally {
        setIsLoading(false);
      }
    };

    initialize();
  }, []); // Empty dependency array as we use ref for initialization check

  // Standalone fetch functions
  const fetchEmail = async (contactIdFromPath: string): Promise<string | null> => {
    if (fetchStatus.email) return null;
    
    try {
      setIsLoading(true);
      const response = await axiosInstance.get(`/zoho/v1.0/contacts/${contactIdFromPath}`);

      if (response.data && response.data.success && response.data.email) {
        const email = response.data.email;
        setUserEmail(email);
        return email;
      }
      return null;
    } catch (error) {
      console.error('Error fetching email from contact ID:', error);
      enqueueSnackbar("Failed to retrieve contact information. Please try again or contact support.", {
        variant: "error",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserDetails = async (email: string): Promise<boolean> => {
    if (fetchStatus.userDetails) return false;
    
    try {
      setIsLoading(true);
      const response = await axiosInstance.get(
        `/funnel/v1.0/patient/details?email=${encodeURIComponent(email)}`
      );

      if (response.data && response.data.success && response.data.data) {
        setUserDetails(response.data.data);
        const userData = response.data.data;
        
        if (userData.fullName) {
          setUserName(userData.fullName);
        } else if (userData.firstname || userData.lastname) {
          setUserName(`${userData.firstname || ''} ${userData.lastname || ''}`.trim());
        }

        if (response.data.data.consent_form_completed === true) {
          setConsentFormCompleted(true);
          enqueueSnackbar("You have already completed the consent form", {
            variant: "info",
          });
        }
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error fetching user details:', error);
      enqueueSnackbar("We couldn't find your account information. Please check your email address or contact support.", {
        variant: "error",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const checkConsentStatus = async (email: string): Promise<void> => {
    if (fetchStatus.consentStatus) return;
    
    try {
      const response = await axiosInstance.get(
        `/funnel/v1.0/patient/consent-form/status-by-email?email=${encodeURIComponent(email)}`
      );

      if (response.data?.success && response.data?.data?.consent_form_completed === true) {
        setConsentFormCompleted(true);
        enqueueSnackbar("You have already completed the consent form", {
          variant: "info",
        });
      }
    } catch (error) {
      console.error('Error checking consent status:', error);
    }
  };

  // Main initialization function
  // const initializeUserData = async (contactIdFromPath: string) => {
  //   // Set initial fetch status
  //   setFetchStatus(prev => ({ ...prev, email: true }));
    
  //   // Step 1: Fetch email
  //   const email = await fetchEmail(contactIdFromPath);
  //   if (!email) {
  //     setFetchStatus(prev => ({ ...prev, email: false }));
  //     return;
  //   }

  //   // Step 2: Fetch user details
  //   setFetchStatus(prev => ({ ...prev, userDetails: true }));
  //   const userDetailsFetched = await fetchUserDetails(email);
  //   if (!userDetailsFetched) {
  //     setFetchStatus(prev => ({ ...prev, userDetails: false }));
  //     return;
  //   }

  //   // Step 3: Check consent status if not already completed
  //   if (!consentFormCompleted) {
  //     setFetchStatus(prev => ({ ...prev, consentStatus: true }));
  //     await checkConsentStatus(email);
  //   }
  // };

  // Define device info type
  interface DeviceInfo {
    userAgent: string;
    platform: string;
    language: string;
    screenWidth: number;
    screenHeight: number;
    timeZone: string;
    timestamp: string;
  }

  const [ipAddress, setIpAddress] = useState('Fetching...');
  const [deviceInfo, _setDeviceInfo] = useState<DeviceInfo>({
    userAgent: window.navigator.userAgent,
    // Use a more modern approach than the deprecated platform property
    platform: (() => {
      const ua = window.navigator.userAgent;
      if (/(iPad|iPhone|iPod)/g.test(ua)) return 'iOS';
      if (/Android/i.test(ua)) return 'Android';
      if (/Win/i.test(ua)) return 'Windows';
      if (/Mac/i.test(ua)) return 'MacOS';
      if (/Linux/i.test(ua)) return 'Linux';
      return 'Unknown';
    })(),
    language: window.navigator.language,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timestamp: new Date().toISOString()
  });

  // Effect to fetch the user's public IP address
  useEffect(() => {
    const fetchIpAddress = async () => {
      // Try multiple services for redundancy
      const ipServices = [
        'https://api.ipify.org?format=json',
        'https://api.ipdata.co?api-key=test', // Falls back to free tier with limited data
        'https://api.ip.sb/jsonip'
      ];

      for (const service of ipServices) {
        try {

          const response = await axios.get(service, { timeout: 5000 }); // 5 second timeout

          // Different APIs return IP in different formats
          const ip = response.data.ip || response.data.ipAddress;

          if (ip && /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/.test(ip)) {

            setIpAddress(ip);
            return; // Exit once we have a valid IP
          }
        } catch (error) {
          //console.error(`Error fetching IP from ${service}:`, error);
          // Continue to next service
        }
      }

      // If all services fail, try to get IP from the server side
      try {
        const response = await axiosInstance.get('/funnel/v1.0/patient/get-client-ip');
        if (response.data && response.data.ip) {
          setIpAddress(response.data.ip);
          return;
        }
      } catch (error) {
        console.error('Error fetching IP from server:', error);
      }

      // If all methods fail
      setIpAddress('Could not determine IP');
    };

    fetchIpAddress();
  }, []);

  // State to track if consent form has already been completed
  const [consentFormCompleted, setConsentFormCompleted] = useState(false);

  // Ensure loading state is cleared when component mounts
  useEffect(() => {
    // Set a short timeout to ensure loading state is cleared
    const initialLoadingTimeout = setTimeout(() => {
      setIsLoading(false);
    }, 3000); // 3 seconds timeout

    return () => {
      clearTimeout(initialLoadingTimeout);
    };
  }, []);

  // Common styles
  const checkboxStyle = { color: '#007F00', '&.Mui-checked': { color: '#007F00' } };

  // Using the naming convention from the documentation
  const [consentData, setConsentData] = useState({
    voluntary_consent: null,
    legally_competent: null,
    sufficient_information: null,
    understanding_risks: null,
    medical_cannabis_unapproved: null,
    illegal_prescription: null,
    drug_interactions: null,
    no_use_while_treated: null,
    illegal_to_minors: null,
    signature: "",
  });

  const [formErrors, setFormErrors] = useState({
    signature: false,
    voluntary_consent: false,
    legally_competent: false,
    sufficient_information: false,
    understanding_risks: false,
    medical_cannabis_unapproved: false,
    illegal_prescription: false,
    drug_interactions: false,
    no_use_while_treated: false,
    illegal_to_minors: false,
  });

  const handleRadioChange = (name: string, value: boolean) => {
    setConsentData((prev) => {
      return { ...prev, [name]: value };
    });

    // Clear error for this field if it's set to true
    if (value === true) {
      setFormErrors(prev => ({
        ...prev,
        [name]: false
      }));
    } else if (value === false) {
      // Show a message when "No" is selected
      // enqueueSnackbar("You must select 'Yes' for all questions to submit the form", {
      //   variant: "warning",
      // });
    }
  };

  const handleSignatureChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = event.target;
    setConsentData((prev) => {
      return { ...prev, [name]: value };
    });

    // Validate against the user's displayed name
    const displayedName = getUserDisplayedName().toLowerCase();
    const inputValue = value.trim().toLowerCase();
    const isValid = inputValue !== "" && inputValue === displayedName;

    setFormErrors({
      ...formErrors,
      [name]: !isValid,
    });
  };

  // Define a type for our consent data
  type ConsentValue = boolean | null;

  // Check if a consent value is explicitly true
  const isTrue = (value: ConsentValue): boolean => value === true;

  const allQuestionsAnswered = () => {
    return (
      isTrue(consentData.voluntary_consent) &&
      isTrue(consentData.legally_competent) &&
      isTrue(consentData.sufficient_information) &&
      isTrue(consentData.understanding_risks) &&
      isTrue(consentData.medical_cannabis_unapproved) &&
      isTrue(consentData.illegal_prescription) &&
      isTrue(consentData.drug_interactions) &&
      isTrue(consentData.no_use_while_treated) &&
      isTrue(consentData.illegal_to_minors)
    );
  };


  // Get the user's displayed name from API data
  const getUserDisplayedName = () => {
    // Use the name fetched from API if available
    if (userName) {
      return userName;
    }

    // Fallback to email if no name is available
    return userEmail;
  };

  // Validate signature against the user's displayed name
  const isSignatureValid = () => {
    const displayedName = getUserDisplayedName().toLowerCase();
    const signature = consentData.signature.trim().toLowerCase();
    return signature !== "" && signature === displayedName;
  };

  // Get a more detailed message about what's missing
  const getMissingRequirementsMessage = () => {
    const missingItems = [];

    // Check each question
    if (!isTrue(consentData.voluntary_consent)) missingItems.push("Question 1");
    if (!isTrue(consentData.legally_competent)) missingItems.push("Question 2");
    if (!isTrue(consentData.sufficient_information)) missingItems.push("Question 3");
    if (!isTrue(consentData.understanding_risks)) missingItems.push("Question 4");
    if (!isTrue(consentData.medical_cannabis_unapproved)) missingItems.push("Question 5");
    if (!isTrue(consentData.illegal_prescription)) missingItems.push("Question 6");
    if (!isTrue(consentData.drug_interactions)) missingItems.push("Question 7");
    if (!isTrue(consentData.no_use_while_treated)) missingItems.push("Question 8");
    if (!isTrue(consentData.illegal_to_minors)) missingItems.push("Question 9");

    // Check signature
    if (!isSignatureValid()) missingItems.push("Signature");

    if (missingItems.length === 0) return "";

    if (missingItems.length === 1) {
      if (missingItems[0] === "Signature") {
        return "Your signature must match your name exactly as shown";
      } else {
        return `You must select 'Yes' for ${missingItems[0]}`;
      }
    }

    // Multiple items missing
    const questionsCount = missingItems.filter(item => item !== "Signature").length;
    const hasSignature = missingItems.includes("Signature");

    let message = "";

    if (questionsCount > 0) {
      message += `You must select 'Yes' for all questions`;
    }

    if (hasSignature) {
      if (questionsCount > 0) message += " and ";
      message += "enter your signature exactly as shown";
    }

    return message;
  };

  const canSubmit = () => {
    return allQuestionsAnswered() && isSignatureValid();
  };

  const handleSubmit = async () => {
    // Validate all fields
    const newErrors = {
      signature: !isSignatureValid(),
      voluntary_consent: consentData.voluntary_consent !== true,
      legally_competent: consentData.legally_competent !== true,
      sufficient_information: consentData.sufficient_information !== true,
      understanding_risks: consentData.understanding_risks !== true,
      medical_cannabis_unapproved: consentData.medical_cannabis_unapproved !== true,
      illegal_prescription: consentData.illegal_prescription !== true,
      drug_interactions: consentData.drug_interactions !== true,
      no_use_while_treated: consentData.no_use_while_treated !== true,
      illegal_to_minors: consentData.illegal_to_minors !== true,
    };

    setFormErrors(newErrors);

    // Check if any fields are invalid
    if (!canSubmit()) {
      // Scroll to the top of the form to show errors
      window.scrollTo({ top: 0, behavior: 'smooth' });

      enqueueSnackbar("Please complete all required fields", {
        variant: "error",
      });
      return;
    }



    setIsLoading(true);

    try {
      // Ensure we have an email from URL parameters
      if (!userEmail) {
        enqueueSnackbar("Email parameter is missing from the URL", {
          variant: "error",
        });
        setIsLoading(false);
        return;
      }

      // Ensure we have user details before submitting
      if (!userDetails) {
        enqueueSnackbar("Unable to retrieve your user details. Please refresh the page or contact support.", {
          variant: "error",
        });
        setIsLoading(false);
        return;
      }

      // Check if we have a contact ID (either directly or from user details)
      if (!contactId && (!userDetails || !userDetails.zohoID)) {
        enqueueSnackbar("Contact ID is missing. Please ensure the URL contains a valid contact parameter.", {
          variant: "error",
        });
        setIsLoading(false);
        return;
      }

      // Prepare user data from API response or URL parameters
      const userData = {
        userId: userEmail,
        userFullName: userName || userEmail,
        userEmail: userEmail,
        // Include any additional user details from API if available
        ...(userDetails ? { userDetails } : {})
      };

      // Prepare request payload with signature evidence
      const requestPayload: ConsentFormPayload = {
        // User data
        ...userData,

        // Consent form data
        ...consentData,

        // Evidence data
        signatureEvidence: {
          ipAddress,
          deviceInfo
        },

        // Zoho data
        updateZoho: true,

        // Flag to indicate this is a public submission (no authentication)
        isPublicSubmission: true
      };

      // Add contact ID if available - clearly labeled as contactId
      if (contactId) {
        requestPayload.contactId = contactId;
        // Also set zohoID for backward compatibility
        requestPayload.zohoID = contactId;
      }

      // If user details has a zohoID, use that as a fallback
      if (!contactId && userDetails && userDetails.zohoID) {
        requestPayload.zohoID = userDetails.zohoID;
      }



      // Use the relative path since axiosInstance already has the baseURL configured
      const result = await axiosInstance.post(
        '/funnel/v1.0/patient/consent-form',
        requestPayload
      );


      if (result.data && result.data.success) {
        enqueueSnackbar("Consent form submitted successfully", {
          variant: "success",
        });

        // Set consent form completed to trigger redirection
        setConsentFormCompleted(true);

        // Redirection will be handled by the useEffect
      } else {
        // Make sure we have a valid message string
        const errorMessage = result.data && typeof result.data.message === 'string'
          ? result.data.message
          : "Failed to submit consent form";

        enqueueSnackbar(errorMessage, {
          variant: "error",
        });
      }
    } catch (error: any) {
      console.error('Error submitting consent form:', error);

      // Log error details
      if (error.response) {
        console.log('Error response:', error.response.data);

        // Check if the response contains HTML error message
        const responseData = error.response.data;
        if (typeof responseData === 'string' && responseData.includes('<!DOCTYPE html>')) {
         
          enqueueSnackbar("Server error. Please contact support.", {
            variant: "error",
          });
        }
        // Check for authentication errors
        else if (error.response.status === 403 || error.response.status === 401 ||
                (error.response.data && error.response.data.authenticated === false)) {
         
          enqueueSnackbar("The server requires authentication for this form. Please contact support.", {
            variant: "error",
          });
        }
        else {
          // Use the error message from the response if available
          const errorMessage =
            error.response.data?.message ||
            "Failed to submit consent form. Please try again.";

          enqueueSnackbar(errorMessage, {
            variant: "error",
          });
        }
      } else if (error.request) {
       
        enqueueSnackbar("Network error. Please check your connection and try again.", {
          variant: "error",
        });
      } else {
        console.log('Error message:', error.message);
        enqueueSnackbar("An unexpected error occurred. Please try again.", {
          variant: "error",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const currentDate = new Date().toLocaleDateString('en-AU', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(',', ' at');

  // Force clear loading state if it's been loading for too long
  useEffect(() => {
    if (isLoading) {

    }
  }, [isLoading]);

  // Update the redirection logic in the useEffect for form completion
  useEffect(() => {
    // If consent form is completed, redirect after a short delay
    if (consentFormCompleted) {
      const redirectTimeout = setTimeout(() => {
        // Build the new redirect URL with email and contactId
        if (userEmail && contactId) {
          // Ensure contactId starts with 'p'
          const safeContactId = contactId.startsWith('p') ? contactId : `p${contactId}`;
          
          const redirectUrl = `https://letsroll.harvest.delivery/members-shop?email=${encodeURIComponent(userEmail)}&contact=${encodeURIComponent(safeContactId)}`;
          window.location.href = redirectUrl;
        } else {
          // Show error if required params are missing
          enqueueSnackbar("Cannot redirect: missing email or contact information. Please contact support.", {
            variant: "error",
          });
        }
      }, 2000);

      // Clean up the timeout if the component unmounts
      return () => clearTimeout(redirectTimeout);
    }
  }, [consentFormCompleted, userEmail, contactId, enqueueSnackbar]);

  // Helper component for question text with error handling
  const QuestionText = ({ fieldName, text }: { fieldName: string, text: string }) => {
    const hasError = formErrors[fieldName as keyof typeof formErrors];

    return (
      <Typography
        variant="body2"
        className={styles.questionText}
        sx={{ color: hasError ? '#f44336' : 'inherit', mt: 1 }}
      >
        {text}*
        {hasError && (
          <span style={{ marginLeft: '5px', color: '#f44336' }}>(Required)</span>
        )}
      </Typography>
    );
  };

  // Redirect to login if no contactId in URL
  useEffect(() => {
    // Only run on client
    if (typeof window === "undefined") return;
    const pathSegments = window.location.pathname.split("/");
    // Handles both /patient/consent and /patient/consent/
    const isConsentBase =
      pathSegments.length >= 3 &&
      pathSegments[1] === "patient" &&
      pathSegments[2] === "consent" &&
      (!pathSegments[3] || pathSegments[3] === "");
    if (isConsentBase) {
      // Use URL parameters instead of localStorage
      const currentPath = '/patient/consent';
      const loginUrl = (import.meta.env.VITE_ZENITH_LOGIN_URL || '/patient/login') + 
                      `?return_to=${encodeURIComponent(currentPath)}`;
      window.location.href = loginUrl;
    }
  }, []);

  return (
    <>
      {isLoading && <LoadingScreen />}
      <ThemeProvider theme={zenithTheme}>
        {consentFormCompleted === true ? (
          // Show a message and loading indicator when consent form is already completed
          <Stack gap={0} sx={{ width: "100%", maxWidth: "100vw", minHeight: "100vh", display: "flex", flexDirection: "column" }}>
            {/* Header */}
            <Box className={styles.header} sx={{ borderRadius: 0 }}>
              <Box
                sx={{ display: "flex", alignItems: "center", width: "100%" }}
              >
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    flex: 1,
                  }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      color: "white",
                      fontWeight: "normal",
                      fontSize: { xs: "1.8rem", sm: "2.2rem" },
                    }}
                  >
                    <Box component="span" sx={{ fontWeight: "bold" }}>
                      Zenith
                    </Box>
                    Clinics
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ color: "white", fontSize: "0.6rem", mt: -0.5 }}
                  >
                    Clinic & Dispensary
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Content */}
            <Box
              sx={{
                padding: 3,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                flex: 1,
              }}
            >
              <img width={200} src="/harvest/harvest_logo.svg" />
              <Typography
                variant="caption"
                sx={{
                  textAlign: "center",
                  mb: 3,
                }}
              >
                Parterning with{" "}
                <span style={{ color: "#007F00", fontWeight: "bold" }}>
                  ZenithClinics
                </span>
              </Typography>
              <Typography variant="caption" sx={{ textAlign: "center", mb: 4, fontSize: "18px" }}>
                <span style={{ fontWeight: "bold" }}>
                  Zenith Clinics has partnered with Harvest
                </span>{" "}
                to support your journey toward natural health. Through your
                treatment plan, you can access{" "}
                <span style={{ fontWeight: "bold" }}>
                  Harvest's private shop to purchase prescribed plant-bases
                  medecine.
                </span>
              </Typography>
              <Typography variant="caption" sx={{ textAlign: "center", mb: 4, fontSize: "18px" }}>
                <span style={{ fontWeight: "bold" }}>Harvest is a trusted</span>{" "}
                community championing plant-bases alternatives, connecting you
                with treatment plan, you can access{" "}
                <span style={{ fontWeight: "bold" }}>like-minded Aussies</span>{" "}
                and standing strong against Big Pharma.
              </Typography>
              {/* Show a circular progress indicator */}
              <Box sx={{ display: "flex", justifyContent: "center", mb: 2 }}>
                <Box sx={{ width: 40, height: 40, color: "#007F00" }}>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="40"
                    height="40"
                    viewBox="0 0 40 40"
                  >
                    <circle
                      cx="20"
                      cy="20"
                      r="18"
                      fill="none"
                      stroke="#e0e0e0"
                      strokeWidth="4"
                    />
                    <circle
                      cx="20"
                      cy="20"
                      r="18"
                      fill="none"
                      stroke="#FD6440"
                      strokeWidth="4"
                      strokeDasharray="113"
                      strokeDashoffset="30"
                      transform="rotate(-90 20 20)"
                    >
                      <animateTransform
                        attributeName="transform"
                        type="rotate"
                        from="0 20 20"
                        to="360 20 20"
                        dur="1s"
                        repeatCount="indefinite"
                        additive="sum"
                      />
                    </circle>
                  </svg>
                </Box>
              </Box>
            </Box>

            {/* Footer */}
            <Box className={styles.footer} sx={{ marginTop: "auto", borderRadius: 0, backgroundColor: "white", padding: "20px 0" }}>
              <Divider sx={{ width: "90%", mx: "auto", mb: 3, bgcolor: "black" }} />
              <Typography 
                variant="body1" 
                sx={{ 
                  color: "#333", 
                  textAlign: "center",
                  fontSize: "14.54px"
                }}
              >
                Provided by <span style={{ color: "#007F00", fontWeight: "bold" }}>ZenithClinics</span> Pty Ltd
              </Typography>
            </Box>
          </Stack>
        ) : (
          // Show the regular consent form if not completed
          <Stack
            gap={0}
            className={styles.consentContainer}
            sx={{ width: "100%", maxWidth: "100vw" }}
          >
            {/* Header */}
            <Box className={styles.header} sx={{ borderRadius: 0 }}>
              <Box
                sx={{ display: "flex", alignItems: "center", width: "100%" }}
              >
                <Box
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    flex: 1,
                  }}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      color: "white",
                      fontWeight: "normal",
                      fontSize: { xs: "1.8rem", sm: "2.2rem" },
                    }}
                  >
                    <Box component="span" sx={{ fontWeight: "bold" }}>
                      Zenith
                    </Box>
                    Clinics
                  </Typography>
                  <Typography
                    variant="caption"
                    sx={{ color: "white", fontSize: "0.6rem", mt: -0.5 }}
                  >
                    Proudly associated with Harvest
                  </Typography>
                </Box>
              </Box>
            </Box>

            {/* Content */}
            <Box className={styles.content}>
              <Typography variant="h4" className={styles.title}>
                Consent
              </Typography>

              <Typography variant="subtitle1" className={styles.subtitle}>
                Read the questions below and sign
              </Typography>

              <Divider sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }} />

              <Stack spacing={1}>
                <QuestionText
                  fieldName="voluntary_consent"
                  text="Do you provide consent voluntarily?"
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.voluntary_consent === true}
                        onChange={() =>
                          handleRadioChange("voluntary_consent", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.voluntary_consent === false}
                        onChange={() =>
                          handleRadioChange("voluntary_consent", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="legally_competent"
                  text="Are you lawfully competent to provide consent?"
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.legally_competent === true}
                        onChange={() =>
                          handleRadioChange("legally_competent", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.legally_competent === false}
                        onChange={() =>
                          handleRadioChange("legally_competent", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="sufficient_information"
                  text="Have you been provided with sufficient information allowing you to evaluate the benefits, risks, side effects, and costs associated with the use of medicinal cannabis to treat your condition, before providing this informed consent?"
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.sufficient_information === true}
                        onChange={() =>
                          handleRadioChange("sufficient_information", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.sufficient_information === false}
                        onChange={() =>
                          handleRadioChange("sufficient_information", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="understanding_risks"
                  text="Are you providing consent with an understanding of the material risks involved in Medicinal Cannabis treatment, including but not limited to workplace drug screening, and operating machinery/vehicles?"
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.understanding_risks === true}
                        onChange={() =>
                          handleRadioChange("understanding_risks", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.understanding_risks === false}
                        onChange={() =>
                          handleRadioChange("understanding_risks", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="medical_cannabis_unapproved"
                  text="I am aware that Medicinal Cannabis is an unapproved medicine and as such has not been assessed by the TGA for safety or efficacy."
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={
                          consentData.medical_cannabis_unapproved === true
                        }
                        onChange={() =>
                          handleRadioChange("medical_cannabis_unapproved", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={
                          consentData.medical_cannabis_unapproved === false
                        }
                        onChange={() =>
                          handleRadioChange(
                            "medical_cannabis_unapproved",
                            false
                          )
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="illegal_prescription"
                  text="I am aware that if I drive while being treated then I am breaking the law, a prescription is not a defense."
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.illegal_prescription === true}
                        onChange={() =>
                          handleRadioChange("illegal_prescription", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.illegal_prescription === false}
                        onChange={() =>
                          handleRadioChange("illegal_prescription", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="drug_interactions"
                  text="I am aware that Medicinal Cannabis may interact with various medications even if they are unprescribed or currently unknown."
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.drug_interactions === true}
                        onChange={() =>
                          handleRadioChange("drug_interactions", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.drug_interactions === false}
                        onChange={() =>
                          handleRadioChange("drug_interactions", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="no_use_while_treated"
                  text="I am in agreement not to use other cannabis while undergoing treatment."
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.no_use_while_treated === true}
                        onChange={() =>
                          handleRadioChange("no_use_while_treated", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.no_use_while_treated === false}
                        onChange={() =>
                          handleRadioChange("no_use_while_treated", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>

                <Divider
                  sx={{ my: 2, borderColor: "#aaaaaa", height: "1px" }}
                />

                <QuestionText
                  fieldName="illegal_to_minors"
                  text="I am aware that giving prescription cannabis to other people is illegal."
                />
                <Box className={styles.checkboxGroup}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.illegal_to_minors === true}
                        onChange={() =>
                          handleRadioChange("illegal_to_minors", true)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>Yes</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={consentData.illegal_to_minors === false}
                        onChange={() =>
                          handleRadioChange("illegal_to_minors", false)
                        }
                        size="small"
                        sx={checkboxStyle}
                      />
                    }
                    label={
                      <Typography sx={{ fontSize: "12px" }}>No</Typography>
                    }
                    className={styles.checkboxOption}
                  />
                </Box>
              </Stack>
            </Box>

            <Divider sx={{ borderColor: "#aaaaaa", height: "1px" }} />

            <Box className={styles.signatureSection}>
              <Typography
                variant="h4"
                sx={{
                  mb: 3,
                  color: "#007F00",
                  fontWeight: "bold",
                  textAlign: "center",
                  width: "100%",
                }}
              >
                Signature
              </Typography>

              <Typography
                variant="body1"
                sx={{ mb: 2, fontSize: "16px", textAlign: "left" }}
              >
                I{" "}
                <Box component="span" sx={{ fontWeight: "bold" }}>
                  {getUserDisplayedName()}
                </Box>{" "}
                confirm that the above is true.
                <br />
                Today's date: {currentDate}
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  mb: 1,
                  fontSize: "16px",
                  fontWeight: "bold",
                  textAlign: "left",
                  color: formErrors.signature ? "#f44336" : "inherit",
                }}
              >
                Signature:*
                {formErrors.signature && (
                  <span
                    style={{
                      marginLeft: "5px",
                      color: "#f44336",
                      fontWeight: "normal",
                    }}
                  >
                    (Required)
                  </span>
                )}
              </Typography>
              <TextField
                fullWidth
                name="signature"
                value={consentData.signature}
                onChange={handleSignatureChange}
                error={formErrors.signature}
                helperText={
                  formErrors.signature
                    ? `Signature must match your name: ${getUserDisplayedName()}`
                    : ""
                }
                sx={{
                  mb: 3,
                  "& .MuiInputBase-root": {
                    fontSize: "16px",
                    fontStyle: "italic",
                  },
                  "& .MuiOutlinedInput-root": {
                    backgroundColor: "white",
                    borderRadius: "4px",
                    border: formErrors.signature
                      ? "2px solid #f44336"
                      : "2px solid #007F00",
                  },
                  "& .Mui-error .MuiOutlinedInput-notchedOutline": {
                    border: "none",
                  },
                  "& .MuiFormHelperText-root": {
                    color: "#f44336",
                    fontWeight: "bold",
                    marginLeft: 0,
                  },
                }}
                size="medium"
                placeholder={getUserDisplayedName()}
              />

              <Typography
                variant="body1"
                sx={{
                  mb: 1,
                  fontSize: "16px",
                  fontWeight: "bold",
                  textAlign: "left",
                }}
              >
                Signature Evidence:
              </Typography>
              <Typography
                variant="body1"
                sx={{ mb: 3, fontSize: "14px", textAlign: "left" }}
              >
                IP Address: {ipAddress}
                <br />
                Device: {deviceInfo.platform || "Unknown"}
                <br />
                Browser:{" "}
                {(() => {
                  const ua = deviceInfo.userAgent;
                  if (ua.includes("Firefox")) return "Firefox";
                  if (ua.includes("Chrome") && !ua.includes("Edg"))
                    return "Chrome";
                  if (ua.includes("Safari") && !ua.includes("Chrome"))
                    return "Safari";
                  if (ua.includes("Edg")) return "Edge";
                  if (ua.includes("MSIE") || ua.includes("Trident/"))
                    return "Internet Explorer";
                  return "Unknown";
                })()}
                <br />
                Screen: {deviceInfo.screenWidth}x{deviceInfo.screenHeight}
                <br />
                Time Zone: {deviceInfo.timeZone}
              </Typography>
            </Box>

            <Divider sx={{ borderColor: "#aaaaaa", height: "1px" }} />

            <Box sx={{ px: { xs: 1, sm: 2 }, pb: 2, pt: 3 }}>
              <Tooltip
                title={!canSubmit() ? getMissingRequirementsMessage() : ""}
                placement="top"
                arrow
              >
                <span style={{ width: "100%" }}>
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    color="primary"
                    disabled={!canSubmit()}
                    onClick={handleSubmit}
                    className={styles.submitButton}
                    sx={{
                      py: 2,
                      fontSize: "18px",
                      fontWeight: "bold",
                      opacity: canSubmit() ? 1 : 0.5,
                      "&.Mui-disabled": {
                        backgroundColor: "#cccccc",
                        color: "#666666",
                        cursor: "not-allowed",
                      },
                    }}
                  >
                    SUBMIT
                  </Button>
                </span>
              </Tooltip>
            </Box>

            {/* Footer */}
            <Box className={styles.footer} sx={{ marginTop: "auto", borderRadius: 0, backgroundColor: "white", padding: "20px 0" }}>
              <Divider sx={{ width: "90%", mx: "auto", mb: 3, bgcolor: "black" }} />
              <Typography 
                variant="body1" 
                sx={{ 
                  color: "#333", 
                  textAlign: "center",
                  fontSize: "18px"
                }}
              >
                Provided by <span style={{ color: "#007F00", fontWeight: "bold" }}>ZenithClinics</span> Pty Ltd
              </Typography>
            </Box>
          </Stack>
        )}
      </ThemeProvider>
    </>
  );
}

export default FormConsent;
