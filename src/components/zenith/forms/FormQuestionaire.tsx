import { useEffect, useState } from "react";
import {
	MobileStepper,
	Box,
	Button,
	Stack,
	TextField,
	Typography,
	FormControlLabel,
	Checkbox,
	FormControl,
	FormGroup,
	Radio,
	RadioGroup,
	ThemeProvider,
} from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import { DatePicker } from "@mui/x-date-pickers";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterMoment } from "@mui/x-date-pickers/AdapterMoment";
import Grid from "@mui/material/Grid2";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";

import moment from "moment";
import { Questionnaire } from "../../../types";
import { useNavigate } from "@tanstack/react-location";
import axios from "axios";
import LoadingScreen from "../../../utils/loading-screen";
import { useSnackbar } from "notistack";
import { CheckBox } from "@mui/icons-material";
import zenithTheme from "../../../styles/zenith/theme";
import UserSteps from "../../../types/enum";
import { useFlow } from "../../../hooks/flow-controller";
import { metaConversionApi } from "../../../utils/metaTrack";

const inputStyle = {
	backgroundColor: "white",
	borderRadius: "13px",
	/*"& .MuiOutlinedInput-root": {
    borderRadius: "13px",
    "&:hover": {
      borderColor: "black",
    },
    "&.Mui-focused fieldset": {
      borderColor: "black",
      borderRadius: "13px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "white",
  },*/
};

function FormQuestionnaire() {
	const event_id = crypto.randomUUID();
  const [activeStep, setActiveStep] = useState(0);
  const [canDoNext, setCanDoNext] = useState(false);
  const [isOlderThanEighteen, setIsOlderThanEighteen] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const { user, setUser } = useFlow();

	const navigate = useNavigate();
	const [formData, setFormData] = useState<Questionnaire>({
		dob: null,
		condition: "",
		first_medication: "",
		second_medication: "",
		children: "",
		disorder: "",
		diseases: "",
		addiction: "",
		treatment: "",
		alternative_medecine: "",
		trial: "",
		gender: "",
	});
	const [formErrors, setFormErrors] = useState({
		dob: false,
		condition: false,
		children: false,
		disorder: false,
		diseases: false,
		addiction: false,
		treatment: false,
		trial: false,
		gender: false,
	});

	const handleSteps = (step?: number, value?: string) => {
		if (activeStep === 0) {
			if (
				formData.dob &&
				formData.condition !== "" &&
				formData.gender !== "" &&
				formData.alternative_medecine !== ""
			) {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}

		if (activeStep === 1) {
			if (formData.children !== "" && formData.disorder !== "" && formData.diseases !== "") {
				setCanDoNext(true);
			} else {
				setCanDoNext(false);
			}
		}

		if (activeStep === 2) {
			if (formData.addiction !== "" && formData.treatment !== "" && formData.trial !== "") {
				setCanDoNext(true);
			}

			if (step === 7 && value) {
				setCanDoNext(true);
			}
		}
	};

	useEffect(() => {
		handleSteps();
	}, [
		formData.gender,
		formData.condition,
		formData.alternative_medecine,
		formData.diseases,
		formData.children,
		formData.disorder,
	]);
	const handleChange = (event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { name, value } = event.target;
		setFormData({ ...formData, [name]: value });
		if (event.target.validity.valid) {
			setFormErrors({ ...formErrors, [name]: false });
		} else {
			setFormErrors({ ...formErrors, [name]: true });
		}
	};

	const handleDateChange = (date: moment.Moment | null) => {
		const formattedDate = moment(date, "DD-MM-YYYY");
		const years = moment().diff(formattedDate, "years", false);

		if (years >= 18) {
			setFormData({ ...formData, dob: formattedDate });
			handleSteps();
			setIsOlderThanEighteen(true);
		} else {
			setIsOlderThanEighteen(false);
			handleSteps();
			setCanDoNext(false);
		}
	};

	const handleSubmit = async () => {
		const formattedDateString = moment(formData.dob).format("YYYY-MM-DD");
		const formDataFormatted = {
			...formData,
			dob: formattedDateString,
		};
		setIsLoading(true);
		try {
			const result = await axios.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/questionnaire/upload`,
				formDataFormatted,
				{ withCredentials: true }
			);
			if (result.data) {
				fireMetaPixelEvent();
				if (result.data.isValid) {
					enqueueSnackbar("Questionnaire Submitted", {
						variant: "success",
					});
					user!.laststep = UserSteps.QUESTIONNAIRE;
					setUser(user);
					if (!result.data.softReject) {
						navigate({ to: "/patient/discharge-letter" });
					} else {
						navigate({ to: "/patient/pending-review" });
					}
				} else {
					navigate({ to: "/patient/not-approved" });
					// setOpenNotApproved(true);
				}
			} else {
				enqueueSnackbar("Failed to submit Questionnaire", {
					variant: "error",
				});
				user!.laststep = UserSteps.QUESTIONNAIRE;
				setUser(user);

				// add delay to ensure meta pixel fires before redirect
				const timer = setTimeout(() => {
					navigate({ to: "/patient/discharge-letter" });
				}, 1000);
			}
		} catch (e) {
			enqueueSnackbar("Failed to submit Questionnaire", {
				variant: "error",
			});
			throw e;
		} finally {
			setIsLoading(false);
		}
	};

	const handleNext = () => {
		setCanDoNext(false);
		setActiveStep((prevActiveStep) => prevActiveStep + 1);
	};

	const handleBack = () => {
		setCanDoNext(true);
		setActiveStep((prevActiveStep) => prevActiveStep - 1);
	};

  const fireMetaPixelEvent = () => {
    // @ts-ignore
    if (typeof window.fbq !== "undefined") {
      // @ts-ignore
      window.fbq("trackSingleCustom", "2897882420379934", "QuestionnaireDone", {
		value: 0.00,
		currency: "AUD",
        source: "ZenithQuestionnaire",
        eventID: event_id
      });
      // @ts-ignore
	//   Removed Dual Brand Dataset
    //   window.fbq("trackSingleCustom", "1107806040959291", "QuestionnaireDone", {
	// 	value: 0.00,
	// 	currency: "AUD",
    //     source: "ZenithQuestionnaire",
    //     eventID: event_id
    //   });
    } else {
      console.warn("Meta Pixel not initialized");
    }

    metaConversionApi('QuestionnaireDone', window.location.href, event_id, user?.email, user?.phone, user?.firstname, user?.lastname);

  };

	useEffect(() => {
		handleSteps();
	}, [activeStep]);
	function redirectToSchedule() {
		axios
			.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`, { user }, { withCredentials: true })
			.then((response) => {
				const encryptedLeadID = response.data.data.leadID;
				const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
				const url = `${baseUrl}?token=${encryptedLeadID}`;
				window.location.href = url;
			});
	}
	const steps = [
		{
			label: "Step 1",
			content: (
				<Stack sx={{ pb: 3 }} spacing={2}>
					<Grid>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										color: "white",
										mb: 1,
										fontWeight: "400",
									}}
									align="left"
								>
									What is your date of birth? *
								</Typography>
							</Grid>
							<LocalizationProvider dateAdapter={AdapterMoment}>
								<DatePicker
									views={["year", "month", "day"]}
									sx={{ ...inputStyle, width: "100%" }}
									name="dob"
									value={formData.dob}
									slotProps={{
										textField: {
											size: "small",
											error: isOlderThanEighteen ? false : true,
											helperText: isOlderThanEighteen ? undefined : "Must be older than 18",
										},
									}}
									onChange={handleDateChange}
								/>
							</LocalizationProvider>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								mt: 2,
								borderRadius: "13px",
							}}
						>
							<Grid>
								<Grid sx={{ width: "100%" }}>
									<Typography
										sx={{
											fontSize: "16px",
											color: "white",
											mb: 1,
											fontWeight: "400",
										}}
										align="left"
									>
										What condition or symptom are you having issues with? *
									</Typography>
								</Grid>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="condition"
									autoComplete="off"
									size="small"
									onChange={handleChange}
									margin="normal"
									fullWidth
									value={formData.condition}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								mt: 2,
								borderRadius: "13px",
							}}
						>
							<Grid>
								<Grid sx={{ width: "100%", alignItems: "start" }}>
									<Typography
										sx={{
											fontSize: "16px",
											color: "white",
											mb: 1,
											fontWeight: "400",
										}}
										align="left"
									>
										What was your Gender at Birth? *
									</Typography>
								</Grid>
								<RadioGroup value={formData.gender} name="gender" onChange={handleChange}>
									<Grid alignSelf={"start"}>
										<FormControlLabel
											value="male"
											id="male"
											control={
												<Radio
													sx={{
														"&.Mui-checked, &.MuiRadio-root": {
															color: "white",
															borderRadius: 1,
															backgroundColor: "white",
															width: "20px",
															height: "20px",
															m: 1,
														},
													}}
													checked={formData.gender == "male"}
													onClick={(e) => handleChange}
													checkedIcon={
														<CheckIcon sx={{ color: "green", fontSize: "medium" }} />
													}
												/>
											}
											label={<span style={{ color: "#ffffff", fontSize: "0.8em" }}>Male</span>}
										/>
										<FormControlLabel
											value="female"
											id="female"
											control={
												<Radio
													sx={{
														"&.Mui-checked, &.MuiRadio-root": {
															color: "white",
															borderRadius: 1,
															backgroundColor: "white",
															width: "20px",
															height: "20px",
															m: 1,
														},
													}}
													checked={formData.gender == "female"}
													onClick={(e) => handleChange}
													checkedIcon={
														<CheckIcon sx={{ color: "green", fontSize: "medium" }} />
													}
												/>
											}
											label={<span style={{ color: "#ffffff", fontSize: "0.8em" }}>Female</span>}
										/>
									</Grid>
								</RadioGroup>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								mt: 2,
								borderRadius: "13px",
							}}
						>
							<Grid>
								<Grid sx={{ width: "100%" }}>
									<Typography
										sx={{
											fontSize: "16px",
											color: "white",
											mb: 1,
											fontWeight: "400",
										}}
										align="left"
									>
										Have you used alternative medicine before? *
									</Typography>
								</Grid>
								<RadioGroup
									value={formData.alternative_medecine}
									name="alternative_medecine"
									onChange={handleChange}
								>
									<FormControlLabel
										value="medically"
										id="medically"
										control={
											<Radio
												sx={{
													"&.Mui-checked, &.MuiRadio-root": {
														color: "white",
														borderRadius: 1,
														backgroundColor: "white",
														width: "20px",
														height: "20px",
														m: 1,
													},
												}}
												checked={formData.alternative_medecine == "medically"}
												onClick={(e) => handleChange}
												checkedIcon={<CheckIcon sx={{ color: "green", fontSize: "medium" }} />}
											/>
										}
										label={<span style={{ color: "#ffffff", fontSize: "0.8em" }}>Medically</span>}
									/>
									<FormControlLabel
										value="recreationally"
										id="recreationally"
										control={
											<Radio
												sx={{
													"&.Mui-checked, &.MuiRadio-root": {
														color: "white",
														borderRadius: 1,
														backgroundColor: "white",
														width: "20px",
														height: "20px",
														m: 1,
													},
												}}
												checked={formData.alternative_medecine == "recreationally"}
												onClick={(e) => handleChange}
												checkedIcon={<CheckIcon sx={{ color: "green", fontSize: "medium" }} />}
											/>
										}
										label={
											<span style={{ color: "#ffffff", fontSize: "0.8em" }}>Recreationally</span>
										}
									/>
									<FormControlLabel
										value="no"
										id="alternative_no"
										control={
											<Radio
												sx={{
													"&.Mui-checked, &.MuiRadio-root": {
														color: "white",
														borderRadius: 1,
														backgroundColor: "white",
														width: "20px",
														height: "20px",
														m: 1,
													},
												}}
												checked={formData.alternative_medecine == "no"}
												onClick={(e) => handleChange}
												checkedIcon={<CheckIcon sx={{ color: "green", fontSize: "medium" }} />}
											/>
										}
										label={<span style={{ color: "#ffffff", fontSize: "0.8em" }}>No</span>}
									/>
								</RadioGroup>
							</Grid>
						</Box>
					</Grid>
				</Stack>
			),
		},
		{
			label: "Step 2",
			content: (
				<>
					<Grid container direction={"column"}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Are you planning to have children within the next 6 months? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												children: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.children === "yes" ? "#515151" : "white",
										color: formData.children === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												children: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.children === "no" ? "#515151" : "white",
										color: formData.children === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you suffer from psychosis, bipolar disorder or schizophrenia? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												disorder: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.disorder === "yes" ? "#515151" : "white",
										color: formData.disorder === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												disorder: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.disorder === "no" ? "#515151" : "white",
										color: formData.disorder === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you suffer from any cardiovascular diseases, including irregular heartbeat
									(arrhythmia)? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												diseases: "yes",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.diseases === "yes" ? "#515151" : "white",
										color: formData.diseases === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												diseases: "no",
											};
										});
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.diseases === "no" ? "#515151" : "white",
										color: formData.diseases === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
					</Grid>
				</>
			),
		},
		{
			label: "Step 3",
			content: (
				<>
					<Grid container direction={"column"}>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Do you have an addiction to any psychoactive substances and/or drugs, including
									alcohol, but excluding nicotine and caffeine? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												addiction: "yes",
											};
										});

										handleSteps(5, "yes");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.addiction === "yes" ? "#515151" : "white",
										color: formData.addiction === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												addiction: "no",
											};
										});

										handleSteps(5, "no");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.addiction === "no" ? "#515151" : "white",
										color: formData.addiction === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Have you discussed other treatment options with your doctor? Including medical and
									conservative therapies. *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												treatment: "yes",
											};
										});

										handleSteps(6, "yes");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.treatment === "yes" ? "#515151" : "white",
										color: formData.treatment === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												treatment: "no",
											};
										});

										handleSteps(6, "no");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.treatment === "no" ? "#515151" : "white",
										color: formData.treatment === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Knowing the alternative management options, do you still want to trial Alternative
									Medicine as a treatment option for your condition? *
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												trial: "yes",
											};
										});

										handleSteps(7, "yes");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.trial === "yes" ? "#515151" : "white",
										color: formData.trial === "yes" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									Yes
								</Button>
								<Button
									onClick={() => {
										setFormData((prev) => {
											return {
												...prev,
												trial: "no",
											};
										});

										handleSteps(7, "no");
									}}
									variant="contained"
									sx={{
										backgroundColor: formData.trial === "no" ? "#515151" : "white",
										color: formData.trial === "no" ? "white" : "black",
										width: "35%",
										borderRadius: 10,
										fontWeight: "bold",
									}}
								>
									No
								</Button>
							</Grid>
						</Box>
					</Grid>
				</>
			),
		},
		{
			label: "Step 4",
			content: (
				<>
					<Grid container direction={"column"}>
						<Typography sx={{ fontSize: "12px", color: "grey" }}>
							In order to be suitable for Alternative Medicine, we need to identify whether you have
							attempted to treat your condition in at least two different ways, over a three month period.
						</Typography>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 1,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Please add the first medication, treatment or therapy you trialled.*
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="first_medication"
									size="small"
									autoComplete="off"
									onChange={handleChange}
									margin="normal"
									fullWidth
									value={formData.first_medication}
								/>
							</Grid>
						</Box>
						<Box
							boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
							sx={{
								mt: 2,
								backgroundColor: "green",
								padding: 2,
								borderRadius: "13px",
							}}
						>
							<Grid sx={{ width: "100%" }}>
								<Typography
									sx={{
										fontSize: "16px",
										fontWeight: 400,
										color: "white",
										mb: 1,
									}}
									align="left"
								>
									Please add the second medication, treatment or therapy you trialled.*
								</Typography>
							</Grid>

							<Grid
								sx={{ width: "100%" }}
								spacing={2}
								container
								alignItems={"center"}
								justifyContent={"space-between"}
							>
								<TextField
									sx={{ m: 0, ...inputStyle }}
									type="text"
									name="second_medication"
									size="small"
									autoComplete="off"
									onChange={handleChange}
									margin="normal"
									fullWidth
									value={formData.second_medication}
								/>
							</Grid>
						</Box>
						<Grid size={12}>
							<Typography sx={{ color: "gray", fontSize: "bold" }} variant="body1" fontWeight={700}>
								*For clinical assessment purposes, please list the specific names of the treatments or
								medications you have previously trialled.
							</Typography>
						</Grid>
					</Grid>
				</>
			),
		},
	];

	return (
		<>
			{isLoading && <LoadingScreen />}
			<ThemeProvider theme={zenithTheme}>
				<Stack gap={2}>
					<Grid
						container
						direction={"column"}
						padding={"20px 0"}
						sx={{ borderRadius: "10px" }}
						boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
					>
						<Grid>
							<Typography
								sx={{
									fontSize: "38px",
									fontWeight: "bold",
									lineHeight: "1em",
									color: "green",
								}}
							>
								Pre-Screening
							</Typography>
						</Grid>
					</Grid>
					<MobileStepper
						variant="progress"
						steps={steps.length}
						position="static"
						activeStep={activeStep}
						sx={{ flexGrow: 1 }}
						nextButton={
							<Button
								style={{ borderRadius: "13px" }}
								variant="contained"
								onClick={handleNext}
								disabled={activeStep === steps.length - 1 || !canDoNext}
							>
								<ArrowForwardIcon />
							</Button>
						}
						backButton={
							<Button
								style={{ borderRadius: "13px" }}
								variant="contained"
								onClick={handleBack}
								disabled={activeStep === 0}
							>
								<ArrowBackIcon />
							</Button>
						}
					/>
					<center>
						<Box>
							{steps[activeStep].content}
							{activeStep === steps.length - 1 &&
							formData.addiction !== "" &&
							formData.first_medication !== "" &&
							formData.second_medication !== "" ? (
								<Button
									type="submit"
									fullWidth
									variant="contained"
									sx={{ mt: 2 }}
									onClick={handleSubmit}
								>
									Submit
								</Button>
							) : null}
						</Box>
					</center>
					<Typography sx={{ fontSize: "12px", color: "grey", mt: 3 }}>
						Any information you provide today is confidential and compliant with the Medical Board of
						Australia Good Medical Practice code, RACGP Standards of General Practice and our Medical
						Confidentiality Duty of Conduct for doctors in Australia, which means we protect your privacy
						and right to confidentiality.
					</Typography>
				</Stack>
			</ThemeProvider>
		</>
	);
}

export default FormQuestionnaire;