import React, { useState, useEffect } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { Stack, Box, Typography, Button, Toolbar, MobileStepper, ThemeProvider } from '@mui/material';
import Grid from "@mui/material/Grid2";
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { loadStripe } from '@stripe/stripe-js';
import PaymentForm from './PaymentForm';
import zenithTheme from '../../../styles/zenith/theme';
import { useFlow } from '../../../hooks/flow-controller';

const FormConsult: React.FC = () => {

  const { user } = useFlow()
  const stripePromise = loadStripe(`${user?.fullName?.includes('TESTX') ? 'pk_test_51PntzyP2Dwh932qzYZ02bFaS7o8bSrRF0uRZAmGNHOV38sM728KNiqoBUdVRwBpouznQFehQ1VWy8EkywUmTfwOm00CetBUT35' : import.meta.env.VITE_STRIPE_API_KEY}`);
   
  const [activeTestimonial, setActiveTestimonial] = useState(0);

  useEffect(() => {
      //Implementing the setInterval method
      const interval = setInterval(() => {
        autoNext(activeTestimonial);
      }, 3500);

      //Clearing the interval
      return () => clearInterval(interval);
  }, [activeTestimonial]);

  
  const autoNext = (autoStep: number) => {
    let nextStep = autoStep + 1;
    if(nextStep >= testimonials.length)
      nextStep = 0;

    setActiveTestimonial(nextStep);
  };

  const handleNext = () => {
    let nextStep = activeTestimonial + 1;
    if(nextStep >= testimonials.length)
      nextStep = 0;

    setActiveTestimonial(nextStep);
  };

  const handleBack = () => {
      let prevStep = activeTestimonial - 1;
      if(prevStep < 0)
        prevStep = testimonials.length -1;

      setActiveTestimonial(prevStep);
  };

const testimonials = [
    {
        content:
        <Stack alignItems={'center'} gap={2} width={'100%'} >
            <Grid container direction={'column'} margin={'10px'} padding={'20px 10px'} sx={{'borderRadius':'10px', width: "100%", minHeight:"105px"}} boxShadow={'0px 3px 3px 0px rgba(0,0,0,0.25)'}>
                <Grid container>
                    <Grid size={8}>
                        <Typography textAlign={'left'} sx={{ fontSize: '12px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                            Jacob Jones
                        </Typography>
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Zenith Clinics Patient
                        </Typography>
                    </Grid>
                    <Grid size={4} container alignItems={'center'} justifyContent={'flex-end'}>
                        <img src='/like.svg' width={'20px'} />
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', pl: "10px", fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Testimonial
                        </Typography>
                    </Grid>
                </Grid>
                <Grid>
                    <Typography textAlign={'left'} sx={{ pt: '10px', fontSize: '11px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                    “So far my experiance has been excellent, i have ordered product and eagerly awaiting delivery”
                    </Typography>
                </Grid>
            </Grid>
        </Stack>
    },
    {
        content:
        <Stack alignItems={'center'} gap={2} width={'100%'} >
            <Grid container direction={'column'} margin={'10px'} padding={'20px 10px'} sx={{'borderRadius':'10px', width: "100%", minHeight:"105px"}} boxShadow={'0px 3px 3px 0px rgba(0,0,0,0.25)'}>
                <Grid container>
                    <Grid size={8}>
                        <Typography textAlign={'left'} sx={{ fontSize: '12px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                            Shara Anderson
                        </Typography>
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Zenith Clinics Patient
                        </Typography>
                    </Grid>
                    <Grid size={4} container alignItems={'center'} justifyContent={'flex-end'}>
                        <img src='/like.svg' width={'20px'} />
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', pl: "10px", fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Testimonial
                        </Typography>
                    </Grid>
                </Grid>
                <Grid>
                    <Typography textAlign={'left'} sx={{ pt: '10px', fontSize: '11px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                    “...it was great thanks”
                    </Typography>
                </Grid>
            </Grid>
        </Stack>
    },
    {
        content:
        <Stack alignItems={'center'} gap={2} width={'100%'} >
            <Grid container direction={'column'} margin={'10px'} padding={'20px 10px'} sx={{'borderRadius':'10px', width: "100%", minHeight:"105px"}} boxShadow={'0px 3px 3px 0px rgba(0,0,0,0.25)'}>
                <Grid container>
                    <Grid size={8}>
                        <Typography textAlign={'left'} sx={{ fontSize: '12px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                            Mark Sidney
                        </Typography>
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Zenith Clinics Patient
                        </Typography>
                    </Grid>
                    <Grid size={4} container alignItems={'center'} justifyContent={'flex-end'}>
                        <img src='/like.svg' width={'20px'} />
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', pl: "10px", fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Testimonial
                        </Typography>
                    </Grid>
                </Grid>
                <Grid>
                    <Typography textAlign={'left'} sx={{ pt: '10px', fontSize: '11px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                    “...it was a great all round experience and I highly recommend you to other people”
                    </Typography>
                </Grid>
            </Grid>
        </Stack>
    },
    {
        content:
        <Stack alignItems={'center'} gap={2} width={'100%'} >
            <Grid container direction={'column'} margin={'10px'} padding={'20px 10px'} sx={{'borderRadius':'10px', width: "100%", minHeight:"105px"}} boxShadow={'0px 3px 3px 0px rgba(0,0,0,0.25)'}>
                <Grid container>
                    <Grid size={8}>
                        <Typography textAlign={'left'} sx={{ fontSize: '12px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                            Michelle Jaine
                        </Typography>
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Zenith Clinics Patient
                        </Typography>
                    </Grid>
                    <Grid size={4} container alignItems={'center'} justifyContent={'flex-end'}>
                        <img src='/like.svg' width={'20px'} />
                        <Typography textAlign={'left'} sx={{ fontSize: '10px', pl: "10px", fontWeight: 'bold', lineHeight: '1em', color: '#505050' }}>
                            Testimonial
                        </Typography>
                    </Grid>
                </Grid>
                <Grid>
                    <Typography textAlign={'left'} sx={{ pt: '10px', fontSize: '11px', fontWeight: 'bold', lineHeight: '1em', color: '#000' }}>
                    “Very nice to speak to and understanding lovely doctor”
                    </Typography>
                </Grid>
            </Grid>
        </Stack>
    },
];

  return (
    <>
      <ThemeProvider theme={zenithTheme}>

      <Elements stripe={stripePromise}>
        <PaymentForm />
      </Elements>
      <>
        <Box width={'100%'} >
            <Box sx={{ mt: 2 }}>
                {testimonials[activeTestimonial].content}
            </Box>
        </Box>
        <MobileStepper
            variant="dots"
            steps={testimonials.length}
            position="static"
            activeStep={activeTestimonial}
            sx={{ flexGrow: 1 }}
            nextButton={
                <Button variant="text" onClick={handleNext} >
                <ArrowForwardIcon />
                </Button>
            }
            backButton={
                <Button variant="text" onClick={handleBack}>
                <ArrowBackIcon />
                </Button>
            }
            />
        </>
        </ThemeProvider>
    </>
  );
}

export default FormConsult;
