import React from 'react';
import { <PERSON><PERSON>, TextField, Box, Autocomplete, Button, ThemeProvider } from '@mui/material';
import { GoogleMap, Marker, MarkerF, useLoadScript } from "@react-google-maps/api";
import usePlacesAutocomplete, {
  getGeocode,
  getLatLng,
} from "use-places-autocomplete";
import "../../../styles/zenith/googlemap.css";
import zenithTheme from '../../../styles/zenith/theme';


function FormAddress() {

  const [mapPoint, setMapPoint] = React.useState({ lat: -25.6060796, lng: 132.3584252 });
  const [currentAddress, setCurrentAddress] = React.useState({
    'street': '',
    'city': '',
    'state': '',
    'postcode': ''
  });
  const [mapZoom, setMapZoom] = React.useState(4);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: 'AIzaSyBj8KhRNq0Z4sLb3vVzKXL1IvH7dTLoMkw',
    libraries: ["places"],
  });

  const center = mapPoint; //{ lat: -25.6060796, lng: 132.3584252 };
  // const onLoadMarker = (marker: Marker) => {
  // };

  const PlacesAutoComplete = () => {
    const {
      ready,
      value,
      setValue,
      suggestions: { data },
      clearSuggestions,
    } = usePlacesAutocomplete({});

    const handleSelect = async (_event: React.SyntheticEvent<Element, Event>, address: string | null) => {
      if (address) {
        setValue(address, false);
      }
      clearSuggestions();

      const result = await getGeocode({ address }); //get geocoding object

      var selectedAddress = {
        'street': '',
        'city': '',
        'state': '',
        'postcode': ''
      };

      result[0]['address_components'].forEach(element => {

        if (element['types'].includes('street_number'))
          selectedAddress.street = element['long_name'];
        if (element['types'].includes('route'))
          selectedAddress.street = selectedAddress.street + ' ' + element['long_name'];
        if (element['types'].includes('administrative_area_level_1'))
          selectedAddress.state = element['short_name'];
        if (element['types'].includes('locality'))
          selectedAddress.city = element['long_name'];
        if (element['types'].includes('postal_code'))
          selectedAddress.postcode = element['long_name'];

      });

      setCurrentAddress(selectedAddress);

      const { lat, lng } = await getLatLng(result[0]);
      setMapPoint({ 'lat': lat, 'lng': lng });
      setMapZoom(17);
    };

    return (
      <Autocomplete
        id="address-pick"
        onChange={(e, v) => handleSelect(e, v)}
        options={data.map((option) => option.description)}
        renderInput={(params) => <TextField onChange={(e) => setValue(e.target.value)} {...params} label="Select Your Location" />}
      />
    );
  };


  const handleSubmit = () => {
    // Handle form submission logic

  };

  return (
    <ThemeProvider theme={zenithTheme}>
    <form onSubmit={handleSubmit}>
      <Stack gap={2}>
        {!isLoaded ? (
          <h3>Loading…..</h3>
        ) : (
          <Stack gap={2}>
            <PlacesAutoComplete />

            <TextField
              type='text'
              value={currentAddress['street']}
              label='House No & Street'
              size='small'
              fullWidth
            />
            <TextField
              type='text'
              value={currentAddress['city']}
              label='City'
              size='small'
              fullWidth
            />
            <TextField
              type='text'
              value={currentAddress['state']}
              label='State'
              size='small'
              fullWidth
            />
            <TextField
              type='text'
              value={currentAddress['postcode']}
              label='Post Code'
              size='small'
              fullWidth
            />

            <GoogleMap
              mapContainerClassName="map_container"
              center={mapPoint}
              zoom={mapZoom}
            >
              {/* <MarkerF position={mapPoint} onLoad={(e) => onLoadMarker(e as Marker)} /> */}
            </GoogleMap>
          </Stack>
        )
        }

        <Button type="submit" fullWidth variant="outlined">
          Submit
        </Button>

      </Stack>
    </form>
    </ThemeProvider>
  );

}

export default FormAddress;