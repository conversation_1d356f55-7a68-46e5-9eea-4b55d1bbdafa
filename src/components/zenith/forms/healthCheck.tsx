import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, Divider, Fab, FormControl, FormControlLabel, FormHelperText, Radio, RadioGroup, TextField, Typography, ThemeProvider } from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useEffect, useRef, useState } from "react";
import CancelIcon from '@mui/icons-material/Cancel';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CheckIcon from '@mui/icons-material/Check';

import axios from "axios";
import { useSnackbar } from "notistack";
import LoadingScreen from "../../../utils/loading-screen";
import zenithTheme from "../../../styles/zenith/theme";

// Using zenithTheme will automatically style MUI components with the correct colors

const questions: { [key: string]: string } = {
    question1: 'In general, would you say your health is:',
    question2: 'Compared to one year ago, how would you rate your health in general now?',
    question3: ' Vigorous activities, such as running, lifting heavy objects, participating in strenuous sports',
    question4: 'Moderate activities, such as moving a table, pushing a vacuum cleaner, bowling, or playing golf',
    question5: 'Lifting or carrying groceries',
    question6: 'Climbing several flights of stairs',
    question7: 'Climbing one flight of stairs',
    question8: 'Bending, kneeling, or stooping',
    question9: 'Walking more than a mile',
    question10: 'Walking several blocks',
    question11: 'Walking one block',
    question12: 'Bathing or dressing yourself',
    question13: 'Cut down the amount of time you spent on work or other activities',
    question14: 'Accomplished less than you would like',
    question15: 'Were limited in the kind of work or other activities',
    question16: 'Had difficulty performing the work or other activities (for example, it took extra effort)',
    question17: 'Cut down the amount of time you spent on work or other activities',
    question18: 'Accomplished less than you would like',
    question19: 'Didn\'t do work or other activities as carefully as usual',
    question20: 'During the past 4 weeks, to what extent has your physical health or emotional problems interfered with your normal social activities with family, friends, neighbors, or groups?',
    question21: 'How much bodily pain have you had during the past 4 weeks?',
    question22: 'During the past 4 weeks, how much did pain interfere with your normal work (including both work outside the home and housework)?',
    question23: 'Did you feel full of pep?',
    question24: 'Have you been a very nervous person?',
    question25: 'Have you felt so down in the dumps that nothing could cheer you up?',
    question26: 'Have you felt calm and peaceful?',
    question27: 'Did you have a lot of energy?',
    question28: 'Have you felt downhearted and blue?',
    question29: 'Did you feel worn out?',
    question30: 'Have you been a happy person?',
    question31: 'Did you feel tired?',
    question32: 'During the past 4 weeks, how much of the time has your physical health or emotional problems interfered with your social activities (like visiting with friends, relatives, etc.)?',
    question33: 'I seem to get sick a little easier than other people',
    question34: 'I am as healthy as anybody I know',
    question35: 'I expect my health to get worse',
    question36: 'My health is excellent'
};

type ObjQuestion = {
    [key: string]: string
    answer: string
    question: string
}

const HealthCheck: React.FC = () => {

    const [items, setItems] = useState<ObjQuestion[]>([])
    const [error, setError] = useState<string>('')
    const [email, setEmail] = useState('')
    const [openErrorDialog, setOpenErrorDialog] = useState(false)
    const [openSuccessDialog, setOpenSuccessDialog] = useState(false)
    const bottom = useRef<HTMLButtonElement>(null);
    const [showFab, setShowFab] = useState(true);
    const { enqueueSnackbar } = useSnackbar()
    const [isLoading, setIsLoading] = useState(false)
    const [compeleted, setCompleted] = useState(false)
    const scrollToBottom = () => {
        bottom.current?.scrollIntoView({ behavior: 'smooth' });
    };

    // Function to handle post-survey redirection
    const handlePostSurveyRedirection = () => {
        // Use environment variable with fallback URL
        window.location.href = import.meta.env.VITE_HEALTH_SURVEY_REDIRECTION_URL || "https://letsroll.harvest.delivery/members-shop/";
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setError('')
        setItems((prev) => {
            const existingQuestionIndex = prev.findIndex((item) => item[name]);

            if (existingQuestionIndex === -1) {
                return [
                    ...prev,
                    {
                        [name]: questions[name],
                        question: questions[name],
                        answer: value,
                    },
                ];
            }

            const updatedItems = [...prev];
            updatedItems[existingQuestionIndex] = {
                [name]: questions[name],
                question: questions[name],
                answer: value,
            };

            return updatedItems;

        })
    }

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setEmail(e.target.value)
    }

    const submitHealthCheck = async () => {
        setIsLoading(true)
        let foundMissingQuestion = false
        Object.keys(questions).forEach((key) => {
            const missingQuestion = items.findIndex((item) => item[key]);
            if (missingQuestion === -1) {
                setError(() => {
                    return key
                })
                setIsLoading(false)
                setOpenErrorDialog(true)
                foundMissingQuestion = true
                return
            }
        })

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email === '' || !emailRegex.test(email)) {
            setError(() => {
                return 'email'
            })
            setIsLoading(false)
            setOpenErrorDialog(true)
            return
        }
        const data = {
            data: items,
            user: email.trim()
        }
        if (!foundMissingQuestion) {
            try {
                await axios.post(`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/health-survey`, data)
                setIsLoading(false)
                setCompleted(true)
                setOpenSuccessDialog(true)
            }
            catch (e) {
                enqueueSnackbar('Failed to submit your survey. Try Again', {
                    variant: 'error'
                })
                setIsLoading(false)
            }
            finally {
                setIsLoading(false)
            }

        }
    }

    const handleScroll = () => {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight;
        const clientHeight = document.documentElement.clientHeight;

        if (scrollTop + clientHeight >= scrollHeight - 5) {
            setShowFab(false);
        } else {
            setShowFab(true);
        }
    };

    useEffect(() => {
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    return (
        <ThemeProvider theme={zenithTheme}>
            <div style={{ backgroundColor: '#ffffff', color: '#000000', padding: '20px' }}>
                {isLoading && <LoadingScreen />}

                {showFab && (
                    <Fab
                        size="small"
                        color="primary"
                        aria-label="scroll to bottom"
                        sx={{
                            position: 'fixed',
                            bottom: 16,
                            right: 16,
                            zIndex: 1000,
                        }}
                        onClick={scrollToBottom}
                    >
                        <ArrowDownwardIcon />
                    </Fab>
                )}
            <Dialog open={openErrorDialog} fullWidth={true} maxWidth={'xs'} onClose={() => setOpenErrorDialog(false)}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }} align="center">
                            {error === 'email' ? 'Missing User Email' : 'Missing Response'}
                        </Typography>
                        <CancelIcon sx={{ width: '100px', height: '100px', color: 'red', mb: 2 }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            {error === 'email' ? 'Incorrect email address' : `Question ${error.split('question')[1]} is missing`}
                        </Typography>
                        <Grid sx={{ mt: 2 }}>
                            <Button variant="contained" color="primary" onClick={() => setOpenErrorDialog(false)}>
                                Close
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>

            <Dialog open={openSuccessDialog} fullWidth={true} maxWidth={'xs'} onClose={() => setOpenSuccessDialog(false)}>
                <DialogContent>
                    <Grid container direction={'column'} sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                        <Typography sx={{ fontSize: '18px', fontWeight: 'bold', mb: 3 }} align="center">
                            Successfully submitted your survey
                        </Typography>
                        <CheckCircleIcon sx={{ width: '100px', height: '100px', color: 'green', mb: 2 }} />
                        <Typography sx={{ fontSize: '14px' }} align="center">
                            Thank you for completing this survey.
                        </Typography>
                        <Grid sx={{ mt: 2 }}>
                            <Button variant="contained" color="primary" onClick={() => {
                                setOpenSuccessDialog(false);
                                handlePostSurveyRedirection();
                            }}>
                                End
                            </Button>
                        </Grid>
                    </Grid>
                </DialogContent>
            </Dialog>

            <Grid container direction={'column'}>
                <Grid container sx={{ width: '100%' }}>
                    <Typography sx={{ fontSize: '28px', fontWeight: 700 }} align="left">
                        Monthly Health Check
                    </Typography>
                </Grid>

                <Grid container sx={{ width: '100%' }}>
                    <Grid container direction={'column'} alignItems={'start'} sx={{ width: '100%', mt: 2 }} spacing={1}>
                        <Grid sx={{ width: '100%' }}>
                            <Typography sx={{ fontSize: '14px' }} align="left">
                                Enter your email (use your login email)
                            </Typography>
                        </Grid>
                        <TextField size="small" fullWidth type="email" value={email} onChange={handleEmailChange} />
                    </Grid>
                </Grid>

                {/* GROUP 1 */}
                <Grid container direction={'column'}>
                    <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                        <Typography sx={{ fontSize: '28px', fontWeight: 700, color: 'green' }} align="left">
                            Health Survey
                        </Typography>
                    </Grid>
                    <Divider style={{ width: '100%' }} />

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                1. In general, would you say your health is:
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question1" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'excellent'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Excellent</Typography>}
                                />
                                <FormControlLabel
                                    value={'very good'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Very Good</Typography>}
                                />
                                <FormControlLabel
                                    value={'good'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Good</Typography>}
                                />
                                <FormControlLabel
                                    value={'fair'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Fair</Typography>}
                                />
                                <FormControlLabel
                                    value={'poor'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Poor</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question1' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}

                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                2. Compared to one year ago, how would you rate your health in general now?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question2" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'excellent'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Excellent</Typography>}
                                />
                                <FormControlLabel
                                    value={'very good'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Very Good</Typography>}
                                />
                                <FormControlLabel
                                    value={'good'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Good</Typography>}
                                />
                                <FormControlLabel
                                    value={'fair'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Fair</Typography>}
                                />
                                <FormControlLabel
                                    value={'poor'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Poor</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question2' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>
                </Grid>

                {/* GROUP 2 */}
                <Grid container direction={'column'}>
                    <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                        <Typography sx={{ fontSize: '28px', fontWeight: 700, color: 'green' }} align="left">
                            The following items are about activities you might do during a typical day. Does your health now limit
                            you in these activities? If so, how much?
                        </Typography>
                    </Grid>
                    <Divider style={{ width: '100%' }} />

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                3. Vigorous activities, such as running, lifting heavy objects, participating in strenuous sports
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question3" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question3' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                4. Moderate activities, such as moving a table, pushing a vacuum cleaner, bowling, or playing golf
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question4" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question4' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                5. Lifting or carrying groceries
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question5" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question5' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                6. Climbing several flights of stairs
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question6" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question6' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                7. Climbing one flight of stairs
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question7" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question7' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                8. Bending, kneeling, or stooping
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question8" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question8' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                9. Walking more than a mile
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question9" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question9' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                10. Walking several blocks
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question10" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question10' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                11. Walking one block
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question11" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question11' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                12. Bathing or dressing yourself
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question12" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes, limited a lot'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a lot</Typography>}
                                />
                                <FormControlLabel
                                    value={'Yes, limited a little'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes, limited a little</Typography>}
                                />
                                <FormControlLabel
                                    value={'No, not limited at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No, not limited at all</Typography>}
                                />

                            </RadioGroup>
                            {error === 'question12' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>
                </Grid>

                {/* GROUP 3 */}
                <Grid container direction={'column'}>
                    <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                        <Typography sx={{ fontSize: '28px', fontWeight: 700, color: 'green' }} align="left">
                            During the past 4 weeks, have you had any of the following problems with your work or other regular daily activities as a result of your physical health?
                        </Typography>
                    </Grid>
                    <Divider style={{ width: '100%' }} />

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                13. Cut down the amount of time you spent on work or other activities
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question13" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question13' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                14. Accomplished less than you would like
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question14" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question14' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                15. Were limited in the kind of work or other activities
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question15" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question15' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                16. Had difficulty performing the work or other activities (for example, it took extra effort)
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question16" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question16' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>
                </Grid>

                {/* GROUP 4 */}
                <Grid container direction={'column'}>
                    <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                        <Typography sx={{ fontSize: '28px', fontWeight: 700, color: 'green' }} align="left">
                            During the past 4 weeks, have you had any of the following problems with your work or other regular daily activities as a result of any emotional problems (such as feeling depressed or anxious)?
                        </Typography>
                    </Grid>
                    <Divider style={{ width: '100%' }} />

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                17. Cut down the amount of time you spent on work or other activities
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question17" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question17' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                18. Accomplished less than you would like
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question18" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question18' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                19. Didn't do work or other activities as carefully as usual
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question19" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Yes'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Yes</Typography>}
                                />
                                <FormControlLabel
                                    value={'No'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">No</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question19' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                20. During the past 4 weeks, to what extent has your physical health or emotional problems interfered with your normal social activities with family, friends, neighbors, or groups?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question20" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Not at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Not at all</Typography>}
                                />
                                <FormControlLabel
                                    value={'Slightly'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Slightly</Typography>}
                                />
                                <FormControlLabel
                                    value={'Moderately'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Moderately</Typography>}
                                />
                                <FormControlLabel
                                    value={'Quite a bit'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Quite a bit</Typography>}
                                />
                                <FormControlLabel
                                    value={'Extremely'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Extremely</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question20' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                21. How much bodily pain have you had during the past 4 weeks?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question21" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'None'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None</Typography>}
                                />
                                <FormControlLabel
                                    value={'Very mild'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Very mild</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mild'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mild</Typography>}
                                />
                                <FormControlLabel
                                    value={'Moderate'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Moderate</Typography>}
                                />
                                <FormControlLabel
                                    value={'Severe'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Severe</Typography>}
                                />

                                <FormControlLabel
                                    value={'Very severe'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Very severe</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question21' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                22. During the past 4 weeks, how much did pain interfere with your normal work (including both work outside the home and housework)?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question22" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Not at all'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Not at all</Typography>}
                                />
                                <FormControlLabel
                                    value={'Slightly'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Slightly</Typography>}
                                />
                                <FormControlLabel
                                    value={'Moderately'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Moderately</Typography>}
                                />
                                <FormControlLabel
                                    value={'Quite a bit'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Quite a bit</Typography>}
                                />
                                <FormControlLabel
                                    value={'Extremely'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Extremely</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question22' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>
                </Grid>

                {/* GROUP 5 */}
                <Grid container direction={'column'}>
                    <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                        <Typography sx={{ fontSize: '28px', fontWeight: 700, color: 'green' }} align="left">
                            These questions are about how you feel and how things have been with you during the past 4 weeks. For each question, please give the one answer that comes closest to the way you have been feeling. How much of the time during the past 4 weeks...
                        </Typography>
                    </Grid>
                    <Divider style={{ width: '100%' }} />

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                23. Did you feel full of pep?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question23" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question23' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                24. Have you been a very nervous person?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question24" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question24' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                25. Have you felt so down in the dumps that nothing could cheer you up?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question25" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question25' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                26. Have you felt calm and peaceful?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question26" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question26' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                27. Did you have a lot of energy?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question27" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question27' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                28. Have you felt downhearted and blue?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question28" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question28' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>


                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                29. Did you feel worn out?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question29" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question29' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                30. Have you been a happy person?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question30" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question30' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                31. Did you feel tired?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question31" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'All of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">All of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Most of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Most of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A good bit of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A good bit of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'Some of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Some of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'A little of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">A little of the time</Typography>}
                                />
                                <FormControlLabel
                                    value={'None of the time'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">None of the time</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question31' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                32. During the past 4 weeks, how much of the time has your physical health or emotional problems interfered with your social activities (like visiting with friends, relatives, etc.)?
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question32" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Definitely true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Don\'t know'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Don't know</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly false</Typography>}
                                />
                                <FormControlLabel
                                    value={'Definitely false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely false</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question32' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>
                </Grid>

                {/* GROUP 6 */}
                <Grid container direction={'column'}>
                    <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                        <Typography sx={{ fontSize: '28px', fontWeight: 700, color: 'green' }} align="left">
                            How TRUE or FALSE is each of the following statements for you.
                        </Typography>
                    </Grid>
                    <Divider style={{ width: '100%' }} />


                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                33. I seem to get sick a little easier than other people
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question33" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Definitely true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Don\'t know'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Don't know</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly false</Typography>}
                                />
                                <FormControlLabel
                                    value={'Definitely false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely false</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question33' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                34. I am as healthy as anybody I know
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question34" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Definitely true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Don\'t know'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Don't know</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly false</Typography>}
                                />
                                <FormControlLabel
                                    value={'Definitely false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely false</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question34' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                35. I expect my health to get worse
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question35" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Definitely true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Don\'t know'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Don't know</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly false</Typography>}
                                />
                                <FormControlLabel
                                    value={'Definitely false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely false</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question35' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>

                    <Grid container sx={{ width: '100%' }}>
                        <Grid sx={{ width: '100%', mt: 4, mb: 1 }}>
                            <Typography sx={{ fontSize: '16px', fontWeight: 500 }} align="left">
                                36. My health is excellent
                            </Typography>
                        </Grid>
                        <FormControl>
                            <RadioGroup name="question36" onChange={handleChange} sx={{ textAlign: 'left' }}>
                                <FormControlLabel
                                    value={'Definitely true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly true'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly true</Typography>}
                                />
                                <FormControlLabel
                                    value={'Don\'t know'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Don't know</Typography>}
                                />
                                <FormControlLabel
                                    value={'Mostly false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Mostly false</Typography>}
                                />
                                <FormControlLabel
                                    value={'Definitely false'}
                                    control={
                                        <Radio />
                                    }
                                    label={<Typography fontSize="14px">Definitely false</Typography>}
                                />
                            </RadioGroup>
                            {error === 'question36' && <FormHelperText error>Please select an option before proceeding.</FormHelperText>}
                        </FormControl>
                    </Grid>
                </Grid>

                <Button disabled={compeleted} ref={bottom} variant='contained' color="primary" sx={{ mt: 2, width: '100%' }} onClick={submitHealthCheck}>Submit</Button>
            </Grid>
        </div>
        </ThemeProvider>
    )
}

export default HealthCheck
