import React, { useState, useEffect } from "react";
import {
	Box,
	Typography,
	Button,
	FormControl,
	FormControlLabel,
	RadioGroup,
	Radio,
	TextField,
	Checkbox,
	FormGroup,
	Slider,
	Grid2 as <PERSON><PERSON>,
	ThemeP<PERSON>ider,
	Icon<PERSON>utton,
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON>abel,
	Divider,
} from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { ArrowBack } from "@mui/icons-material";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import axios from "axios";
import { useFlow } from "../../../hooks/flow-controller";
import { Add22ThcFormData, Add22ThcScoringState } from "../../../types";
import { isEligibleForAdd22ThcQuestionnaire } from "../../../utils/treatmentPlanValidation";
import axiosInstance from "../../../services/axios";
import Banner from "../layouts/Banner";
import CachedImage from "../common/CachedImage";
import zenithTheme from "../../../styles/zenith/theme";

const Add22Thc: React.FC = () => {
	const navigate = useNavigate();
	const { enqueueSnackbar } = useSnackbar();
	const { user } = useFlow();

	// Component state
	const [step, setStep] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [canProceed, setCanProceed] = useState(false);
	const [contactId, setContactId] = useState<string>("");
	const [treatmentPlan, setTreatmentPlan] = useState<any>(null);
	const [drawerOpen, setDrawerOpen] = useState(false);

	// Form data state
	const [formData, setFormData] = useState<Add22ThcFormData>({
		// Question 1: Reason for requesting 22% THC
		reasonSideEffects: false,
		reasonGentlerEffect: false,
		reasonDifferentStrain: false,
		reasonTolerance: false,
		reasonOther: false,
		reasonOtherText: "",

		// Question 2: Current response to 29% THC
		symptomImprovement: "1",  // Initialize with "1" instead of empty string
		sideEffectsNone: false,
		sideEffectsMild: false,
		sideEffectsModerate: false,
		sideEffectsStrong: false,
		sideEffectsDescription: "",

		// Question 3: Health changes
		healthChanges: "",
		healthChangesDescription: "",

		// Question 4: Expectations and preferences
		expectations: "",
		concerns: "",
		usagePlan: "",

		// Question 5: Consent
		consent: "",
	});

	// Scoring state
	const [scoring, setScoring] = useState<Add22ThcScoringState>({
		totalScore: 0,
		maxScore: 33, // Updated from 50 to 33 to match actual maximum achievable score
		isEligible: false,
		questionScores: {},
	});

	// Check eligibility on component mount
	useEffect(() => {
		const checkEligibility = async () => {
			if (!user?.email) {
				enqueueSnackbar("Please log in to access this questionnaire", { variant: "error" });
				navigate({ to: "/patient/login" });
				return;
			}

			try {
				// Fetch treatment plan data
				const treatmentResponse = await axiosInstance.get(
					`/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`
				);

				if (treatmentResponse.data?.success && treatmentResponse.data?.treatmentPlan) {
					const plan = treatmentResponse.data.treatmentPlan;
					setTreatmentPlan(plan);
					setContactId(treatmentResponse.data.contactId);

					// Check if patient is eligible for 22% THC addition
					if (!isEligibleForAdd22ThcQuestionnaire(plan)) {
						enqueueSnackbar(
							"You are not eligible for this questionnaire. You must have an active 29% THC treatment plan only.",
							{ variant: "error" }
						);
						navigate({ to: "/patient/home" });
						return;
					}
				} else {
					enqueueSnackbar("Unable to verify your treatment plan. Please contact support.", {
						variant: "error",
					});
					navigate({ to: "/patient/home" });
					return;
				}
			} catch (error) {
				console.error("Error checking eligibility:", error);
				enqueueSnackbar("Error verifying eligibility. Please try again.", { variant: "error" });
				navigate({ to: "/patient/home" });
			}
		};

		checkEligibility();
	}, [user?.email, navigate, enqueueSnackbar]);

	// Scoring mapping object - maps answer values to points
	const scoringMap: Record<string, Record<string, number>> = {
		// Question 1: Reasons (checkbox values)
		reasonSideEffects: { true: 4, false: 0 },
		reasonGentlerEffect: { true: 3, false: 0 },
		reasonDifferentStrain: { true: 2, false: 0 },
		reasonTolerance: { true: 3, false: 0 },
		reasonOther: { true: 1, false: 0 },

		// Question 2: Symptom improvement (1-10 scale)
		symptomImprovement: {
			"1": 0, "2": 0, "3": 1, "4": 1, "5": 2,
			"6": 2, "7": 3, "8": 3, "9": 4, "10": 4
		},

		// Question 2: Side effects (checkbox values)
		sideEffectsNone: { true: 4, false: 0 },
		sideEffectsMild: { true: 2, false: 0 },
		sideEffectsModerate: { true: 1, false: 0 },
		sideEffectsStrong: { true: 0, false: 0 },

		// Question 3: Health changes
		healthChanges: {
			"no-changes": 3,
			"yes": 1
		},

		// Question 4: Usage plan
		usagePlan: {
			"alternative-situations": 4,
			"rotation-time-symptoms": 3,
			"unsure-advice": 2,
			"other": 1
		},

		// Question 5: Consent
		consent: {
			"yes": 5,
			"no": 0
		}
	};

	// Calculate score for individual question
	const calculateQuestionScore = (questionKey: keyof Add22ThcFormData, answer: string | boolean): number => {
		const questionScoring = scoringMap[questionKey];
		if (!questionScoring) return 0;

		const answerKey = typeof answer === 'boolean' ? answer.toString() : answer;
		return questionScoring[answerKey] || 0;
	};

	// Calculate total score from all answers
	const calculateTotalScore = (data: Add22ThcFormData): number => {
		let totalScore = 0;
		const questionScores: Record<string, number> = {};

		// Calculate score for each question
		Object.entries(data).forEach(([key, value]) => {
			// Skip text fields that don't contribute to scoring
			if (key !== 'reasonOtherText' && key !== 'sideEffectsDescription' && 
				key !== 'healthChangesDescription' && key !== 'expectations' && key !== 'concerns') {
				const questionKey = key as keyof Add22ThcFormData;
				const score = calculateQuestionScore(questionKey, value);
				questionScores[key] = score;
				totalScore += score;
			}
		});

		return totalScore;
	};

	// Check eligibility based on score
	const checkEligibility = (score: number): boolean => {
		return score >= 7; // 20% of 33 points
	};

	// Update scoring when form data changes
	useEffect(() => {
		const totalScore = calculateTotalScore(formData);
		const isEligible = checkEligibility(totalScore);

		setScoring(prev => ({
			...prev,
			totalScore,
			isEligible,
			questionScores: {}
		}));
	}, [formData]);

	// Handle checkbox changes
	const handleCheckboxChange = (questionKey: keyof Add22ThcFormData, checked: boolean) => {
		setFormData(prev => ({
			...prev,
			[questionKey]: checked
		}));
	};

	// Handle radio button changes
	const handleRadioChange = (questionKey: keyof Add22ThcFormData, value: string) => {
		setFormData(prev => ({
			...prev,
			[questionKey]: value
		}));
	};

	// Handle text field changes
	const handleTextChange = (questionKey: keyof Add22ThcFormData, value: string) => {
		setFormData(prev => ({
			...prev,
			[questionKey]: value
		}));
	};

	// Handle slider changes
	const handleSliderChange = (questionKey: keyof Add22ThcFormData, value: number) => {
		setFormData(prev => ({
			...prev,
			[questionKey]: value.toString()
		}));
	};

	// Validate current step
	const validateCurrentStep = (): boolean => {
		switch (step) {
			case 1:
				// At least one reason must be selected
				return formData.reasonSideEffects || formData.reasonGentlerEffect ||
					   formData.reasonDifferentStrain || formData.reasonTolerance || formData.reasonOther;
			case 2:
				// Symptom improvement rating and at least one side effect option
				// Also require description if moderate or strong side effects are selected
				const sideEffectSelected = formData.sideEffectsNone || formData.sideEffectsMild ||
										 formData.sideEffectsModerate || formData.sideEffectsStrong;
				
				const descriptionRequired = (formData.sideEffectsModerate || formData.sideEffectsStrong);
				const descriptionValid = !descriptionRequired || 
									   (descriptionRequired && formData.sideEffectsDescription.trim() !== "");
				
				return formData.symptomImprovement !== "" && sideEffectSelected && descriptionValid;
			case 3:
				// Health changes selection
				return formData.healthChanges !== "";
			case 4:
				// All three fields must be filled and usage plan selected
				return formData.expectations.trim() !== "" &&
					   formData.concerns.trim() !== "" &&
					   formData.usagePlan !== "";
			case 5:
				// Consent must be given
				return formData.consent !== "";
			default:
				return false;
		}
	};

	// Update canProceed when step or formData changes
	useEffect(() => {
		setCanProceed(validateCurrentStep());
	}, [step, formData]);

	// Navigation functions
	const toggleDrawer = () => {
		setDrawerOpen(!drawerOpen);
	};

	const handleLogout = () => {
		// Clear authentication data
		localStorage.removeItem("zenith_auth_user");
		localStorage.removeItem("zenith_authenticated");

		// Close drawer
		setDrawerOpen(false);

		// Navigate to login
		navigate({ to: "/patient/login" });
	};

	const navigateToShop = () => {
		try {
			const shopUrl = import.meta.env.VITE_ZENITH_SHOP_URL;
			if (shopUrl) {
				window.open(shopUrl, "_blank");
			} else {
				enqueueSnackbar("Shop URL not configured", {
					variant: "error",
				});
			}
		} catch (error) {
			enqueueSnackbar("Error opening shop", {
				variant: "error",
			});
		}
	};

	// Helper function to get symptom improvement text
	const getSymptomImprovementText = (value: string): string => {
		const num = parseInt(value);
		if (num === 10) return "Complete relief";
		if (num >= 9) return "Very effective";
		if (num >= 7) return "Good improvement";
		if (num >= 5) return "Some improvement";
		if (num >= 3) return "Minimal improvement";
		return "No improvement";
	};

	// Note: Individual question scoring is now handled by calculateQuestionScore() function
	// This provides better analytics by tracking each checkbox/option separately



	// Submit questionnaire
	const submitQuestionnaire = async () => {
		setIsLoading(true);
		try {
			// Create grouped questions and answers array for better doctor review
			const questionsAndAnswers = [];

			// Question 1: Reason for requesting 22% THC (individual checkboxes with scores)
			// Add individual checkbox entries for better analytics
			questionsAndAnswers.push({
				questionKey: "reasonSideEffects",
				questionText: "Reason: Experiencing side effects with 29%",
				answerValue: formData.reasonSideEffects,
				answerText: formData.reasonSideEffects ? "Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)" : "Not experiencing side effects with 29%",
				score: calculateQuestionScore('reasonSideEffects', formData.reasonSideEffects)
			});

			questionsAndAnswers.push({
				questionKey: "reasonGentlerEffect",
				questionText: "Reason: Prefer a gentler effect for daily use",
				answerValue: formData.reasonGentlerEffect,
				answerText: formData.reasonGentlerEffect ? "Prefer a gentler effect for daily or daytime use" : "Do not prefer gentler effect",
				score: calculateQuestionScore('reasonGentlerEffect', formData.reasonGentlerEffect)
			});

			questionsAndAnswers.push({
				questionKey: "reasonDifferentStrain",
				questionText: "Reason: Trying a different strain for symptom targeting",
				answerValue: formData.reasonDifferentStrain,
				answerText: formData.reasonDifferentStrain ? "Trying a different strain for symptom targeting (e.g., sleep, focus, mood)" : "Not interested in different strain",
				score: calculateQuestionScore('reasonDifferentStrain', formData.reasonDifferentStrain)
			});

			questionsAndAnswers.push({
				questionKey: "reasonTolerance",
				questionText: "Reason: Building tolerance to higher THC strain",
				answerValue: formData.reasonTolerance,
				answerText: formData.reasonTolerance ? "Building tolerance to higher THC strain" : "Not building tolerance",
				score: calculateQuestionScore('reasonTolerance', formData.reasonTolerance)
			});

			questionsAndAnswers.push({
				questionKey: "reasonOther",
				questionText: "Reason: Other",
				answerValue: formData.reasonOther,
				answerText: formData.reasonOther ? `Other reason: ${formData.reasonOtherText || 'No description provided'}` : "No other reason",
				score: calculateQuestionScore('reasonOther', formData.reasonOther)
			});

			// Question 2: Current response to 29% THC (slider)
			if (formData.symptomImprovement) {
				questionsAndAnswers.push({
					questionKey: "symptomImprovement",
					questionText: "How well has your current 29% THC treatment worked for you?",
					answerValue: formData.symptomImprovement,
					answerText: `${formData.symptomImprovement} - ${getSymptomImprovementText(formData.symptomImprovement)}`,
					score: calculateQuestionScore('symptomImprovement', formData.symptomImprovement)
				});
			}

			// Question 3: Side effects experienced (individual checkboxes with scores)
			questionsAndAnswers.push({
				questionKey: "sideEffectsNone",
				questionText: "Side effects: None",
				answerValue: formData.sideEffectsNone,
				answerText: formData.sideEffectsNone ? "None" : "Experiencing some side effects",
				score: calculateQuestionScore('sideEffectsNone', formData.sideEffectsNone)
			});

			questionsAndAnswers.push({
				questionKey: "sideEffectsMild",
				questionText: "Side effects: Mild",
				answerValue: formData.sideEffectsMild,
				answerText: formData.sideEffectsMild ? "Mild (e.g., dry mouth, mild sedation, tiredness)" : "No mild side effects",
				score: calculateQuestionScore('sideEffectsMild', formData.sideEffectsMild)
			});

			questionsAndAnswers.push({
				questionKey: "sideEffectsModerate",
				questionText: "Side effects: Moderate",
				answerValue: formData.sideEffectsModerate,
				answerText: formData.sideEffectsModerate ? "Moderate (e.g., dizziness, nausea, appetite changes)" : "No moderate side effects",
				score: calculateQuestionScore('sideEffectsModerate', formData.sideEffectsModerate)
			});

			questionsAndAnswers.push({
				questionKey: "sideEffectsStrong",
				questionText: "Side effects: Strong",
				answerValue: formData.sideEffectsStrong,
				answerText: formData.sideEffectsStrong ? "Strong (e.g., anxiety, mood changes, racing heart, confusion)" : "No strong side effects",
				score: calculateQuestionScore('sideEffectsStrong', formData.sideEffectsStrong)
			});

			// Question 4: Side effects description (if provided)
			if (formData.sideEffectsDescription && formData.sideEffectsDescription.trim()) {
				questionsAndAnswers.push({
					questionKey: "sideEffectsDescription",
					questionText: "Please describe your side effects in more detail:",
					answerValue: formData.sideEffectsDescription,
					answerText: formData.sideEffectsDescription,
					score: 0
				});
			}

			// Question 5: Health changes
			if (formData.healthChanges) {
				const healthChangesText = formData.healthChanges === 'yes' ? 'Yes, there have been changes' :
										 formData.healthChanges === 'no-changes' ? 'No changes' : formData.healthChanges;
				questionsAndAnswers.push({
					questionKey: "healthChanges",
					questionText: "Have there been any changes in your health, medications, or lifestyle since your last consultation?",
					answerValue: formData.healthChanges,
					answerText: healthChangesText,
					score: calculateQuestionScore('healthChanges', formData.healthChanges)
				});
			}

			// Question 6: Health changes description (if provided)
			if (formData.healthChangesDescription && formData.healthChangesDescription.trim()) {
				questionsAndAnswers.push({
					questionKey: "healthChangesDescription",
					questionText: "Please describe the changes:",
					answerValue: formData.healthChangesDescription,
					answerText: formData.healthChangesDescription,
					score: 0
				});
			}

			// Question 7: Expectations
			if (formData.expectations && formData.expectations.trim()) {
				questionsAndAnswers.push({
					questionKey: "expectations",
					questionText: "What do you hope to achieve by adding a 22% THC product?",
					answerValue: formData.expectations,
					answerText: formData.expectations,
					score: 0
				});
			}

			// Question 8: Concerns
			if (formData.concerns && formData.concerns.trim()) {
				questionsAndAnswers.push({
					questionKey: "concerns",
					questionText: "Do you have any concerns about adding this option?",
					answerValue: formData.concerns,
					answerText: formData.concerns,
					score: 0
				});
			}

			// Question 9: Usage plan
			if (formData.usagePlan) {
				const usagePlanText = formData.usagePlan === 'alternative-situations' ? 'As an alternative for specific times/situations' :
									  formData.usagePlan === 'rotation-time-symptoms' ? 'In rotation with 29% depending on time of day or symptoms' :
									  formData.usagePlan === 'unsure-advice' ? 'Unsure – would like advice from my doctor' :
									  formData.usagePlan === 'other' ? 'Other' : formData.usagePlan;
				questionsAndAnswers.push({
					questionKey: "usagePlan",
					questionText: "How do you plan to use the 22% THC product alongside your 29%?",
					answerValue: formData.usagePlan,
					answerText: usagePlanText,
					score: calculateQuestionScore('usagePlan', formData.usagePlan)
				});
			}

			// Question 10: Consent
			if (formData.consent) {
				questionsAndAnswers.push({
					questionKey: "consent",
					questionText: "Do you consent to your doctor reviewing this information, accessing your MyHealth Record if needed, and updating your treatment plan if appropriate?",
					answerValue: formData.consent,
					answerText: formData.consent === 'yes' ? 'Yes' : 'No',
					score: calculateQuestionScore('consent', formData.consent)
				});
			}

			// Prepare submission data
			const submissionData = {
				questionsAndAnswers,
				totalScore: scoring.totalScore,
				maxScore: scoring.maxScore,
				isEligible: scoring.isEligible,
				submittedAt: new Date().toISOString(),
			};

			const result = await axios.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/add-22-thc-questionnaire`,
				submissionData,
				{ withCredentials: true }
			);

			if (result.data) {
				// Show results based on eligibility
				if (scoring.isEligible) {
					enqueueSnackbar("Congratulations! Your request to add 22% THC is under review.", {
						variant: "success",
					});
				} else {
					enqueueSnackbar("Congratulations! Your request to add 22% THC is under review.", {
						variant: "success",
					});
				}

				// Navigate back to home page to show approval/rejection status
				navigate({ to: "/patient/home" });
			}
		} catch (error) {
			console.error('Error submitting 22% THC addition questionnaire:', error);
			enqueueSnackbar("Failed to submit questionnaire. Please try again.", { variant: "error" });
		} finally {
			setIsLoading(false);
		}
	};

	// Step 1: Reason for requesting 22% THC
	const stepOne = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Why do you want to add a 22% THC product to your treatment plan? (Select all that apply)
				</FormLabel>
				<FormGroup sx={{ mt: 2 }}>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonSideEffects}
								onChange={(e) => handleCheckboxChange('reasonSideEffects', e.target.checked)}
							/>
						}
						label="Experiencing side effects with 29% (e.g., anxiety, dizziness, increased heart rate)"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonGentlerEffect}
								onChange={(e) => handleCheckboxChange('reasonGentlerEffect', e.target.checked)}
							/>
						}
						label="Prefer a gentler effect for daily or daytime use"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonDifferentStrain}
								onChange={(e) => handleCheckboxChange('reasonDifferentStrain', e.target.checked)}
							/>
						}
						label="Trying a different strain for symptom targeting (e.g., sleep, focus, mood)"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonTolerance}
								onChange={(e) => handleCheckboxChange('reasonTolerance', e.target.checked)}
							/>
						}
						label="Building tolerance to higher THC strain"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonOther}
								onChange={(e) => handleCheckboxChange('reasonOther', e.target.checked)}
							/>
						}
						label="Other (please describe):"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					{formData.reasonOther && (
						<TextField
							fullWidth
							multiline
							rows={2}
							value={formData.reasonOtherText}
							onChange={(e) => handleTextChange('reasonOtherText', e.target.value)}
							placeholder="Please describe your other reason..."
							sx={{ marginTop: "10px", marginLeft: "32px" }}
						/>
					)}
				</FormGroup>
			</FormControl>
		</Stack>
	);

	// Step 2: Current response to 29% THC
	const stepTwo = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					How well has your current 29% THC treatment worked for you?
				</FormLabel>
				<Typography sx={{ marginBottom: "10px", mt: 1 }}>
					Rate your symptom improvement on a scale of 1-10 (1 = no improvement, 10 = complete relief):
				</Typography>
				<Box sx={{ padding: "0 20px", marginBottom: "20px" }}>
					<Slider
						value={formData.symptomImprovement ? parseInt(formData.symptomImprovement) : 1}
						onChange={(_, value) => handleSliderChange('symptomImprovement', value as number)}
						min={1}
						max={10}
						step={1}
						marks
						valueLabelDisplay="on"
					/>
				</Box>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Have you experienced any side effects with the 29% THC product? (Select all that apply)
				</FormLabel>
				<FormGroup sx={{ mt: 2 }}>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsNone}
								onChange={(e) => handleCheckboxChange('sideEffectsNone', e.target.checked)}
							/>
						}
						label="None"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsMild}
								onChange={(e) => handleCheckboxChange('sideEffectsMild', e.target.checked)}
							/>
						}
						label="Mild (e.g., dry mouth, mild sedation, tiredness)"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsModerate}
								onChange={(e) => handleCheckboxChange('sideEffectsModerate', e.target.checked)}
							/>
						}
						label="Moderate (e.g., dizziness, nausea, appetite changes)"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsStrong}
								onChange={(e) => handleCheckboxChange('sideEffectsStrong', e.target.checked)}
							/>
						}
						label="Strong (e.g., anxiety, mood changes, racing heart, confusion)"
						sx={{ 
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
				</FormGroup>
				{(formData.sideEffectsModerate || formData.sideEffectsStrong) && (
					<TextField
						fullWidth
						multiline
						rows={3}
						value={formData.sideEffectsDescription}
						onChange={(e) => handleTextChange('sideEffectsDescription', e.target.value)}
						placeholder="Please describe briefly..."
						sx={{ marginTop: "15px" }}
					/>
				)}
			</FormControl>
		</Stack>
	);

	// Step 3: Health changes
	const stepThree = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Have there been any changes in your health, medications, or lifestyle since your last consultation?
				</FormLabel>
				<Typography sx={{ marginBottom: "15px", fontSize: "14px", color: "#666", mt: 1 }}>
					(e.g., new conditions, medications, substance use, mental health updates)
				</Typography>
				<RadioGroup
					value={formData.healthChanges}
					onChange={(e) => handleRadioChange('healthChanges', e.target.value)}
				>
					<FormControlLabel 
						value="no-changes" 
						control={<Radio />} 
						label="No changes" 
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel 
						value="yes" 
						control={<Radio />} 
						label="Yes — please describe:" 
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
				</RadioGroup>
				{formData.healthChanges === 'yes' && (
					<TextField
						fullWidth
						multiline
						rows={3}
						value={formData.healthChangesDescription}
						onChange={(e) => handleTextChange('healthChangesDescription', e.target.value)}
						placeholder="Please describe the changes..."
						sx={{ marginTop: "15px" }}
					/>
				)}
				<Typography sx={{ marginTop: "20px", fontSize: "12px", color: "#888", fontStyle: "italic" }}>
					Note: If changes involve blood pressure, heart rate, or new medications, we may ask for a quick pharmacy reading or health app screenshot for safety.
				</Typography>
			</FormControl>
		</Stack>
	);

	// Step 4: Expectations and preferences
	const stepFour = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					What do you hope to achieve by adding a 22% THC product? (e.g., reduced side effects, better daily functioning)
				</FormLabel>
				<TextField
					fullWidth
					multiline
					rows={3}
					value={formData.expectations}
					onChange={(e) => handleTextChange('expectations', e.target.value)}
					placeholder="Please describe your expectations..."
					sx={{ marginTop: "10px" }}
				/>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Do you have any concerns about adding this option? (e.g., potential interactions or reduced effectiveness)
				</FormLabel>
				<TextField
					fullWidth
					multiline
					rows={3}
					value={formData.concerns}
					onChange={(e) => handleTextChange('concerns', e.target.value)}
					placeholder="Please describe any concerns..."
					sx={{ marginTop: "10px" }}
				/>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					How do you plan to use the 22% THC product alongside your 29%?
				</FormLabel>
				<RadioGroup
					value={formData.usagePlan}
					onChange={(e) => handleRadioChange('usagePlan', e.target.value)}
					sx={{ mt: 1 }}
				>
					<FormControlLabel
						value="alternative-situations"
						control={<Radio />}
						label="As an alternative for specific times/situations"
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						value="rotation-time-symptoms"
						control={<Radio />}
						label="In rotation with 29% depending on time of day or symptoms"
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						value="unsure-advice"
						control={<Radio />}
						label="Unsure – would like advice from my doctor"
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						value="other"
						control={<Radio />}
						label="Other (please describe):"
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 5: Consent
	const stepFive = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Do you consent to your doctor reviewing this information, accessing your MyHealth Record if needed, and updating your treatment plan if appropriate?
				</FormLabel>
				<RadioGroup
					value={formData.consent}
					onChange={(e) => handleRadioChange('consent', e.target.value)}
					sx={{ mt: 2 }}
				>
					<FormControlLabel 
						value="yes" 
						control={<Radio />} 
						label="Yes" 
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel 
						value="no" 
						control={<Radio />} 
						label="No" 
						sx={{ 
							alignItems: 'center',
							marginBottom: 1,
							'.MuiFormControlLabel-label': { 
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Render current step
	const renderStep = () => {
		switch (step) {
			case 1:
				return stepOne();
			case 2:
				return stepTwo();
			case 3:
				return stepThree();
			case 4:
				return stepFour();
			case 5:
				return stepFive();
			default:
				return <Typography>Step {step} content goes here.</Typography>;
		}
	};

	// Handle step navigation
	const handleStepChange = async () => {
		if (!canProceed) {
			enqueueSnackbar("Please answer all questions to continue", {
				variant: "warning",
			});
			return;
		}

		if (step >= 1 && step < 5) {
			setStep(step + 1);
		} else if (step === 5) {
			// Submit questionnaire
			await submitQuestionnaire();
		}
	};

	return (
		<ThemeProvider theme={zenithTheme}>
			<Box
				sx={{
					minHeight: "100vh",
					backgroundColor: "#f5f5f5",
					display: "flex",
					flexDirection: "column",
				}}
			>
				{/* Custom Header */}
				<Box sx={{ width: "100%" }}>
					<Banner />

					{/* Nav Bar */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
							padding: "0 20px",
							backgroundColor: "white",
							paddingBottom: "10px",
						}}
					>
						<IconButton aria-label="menu" sx={{ padding: "8px" }} onClick={toggleDrawer}>
							<MenuIcon />
						</IconButton>

						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{
								height: "19.14px",
							}}
						/>
					</Box>
				</Box>

				{/* Navigation Drawer */}
				<Drawer
					anchor="top"
					open={drawerOpen}
					onClose={toggleDrawer}
					keepMounted={true}
					sx={{
						"& .MuiDrawer-paper": {
							width: "100%",
							maxWidth: "100%",
							boxSizing: "border-box",
							backgroundColor: "white",
							color: "black",
							padding: "20px 0",
							top: { xs: "40px", sm: "48px" },
							zIndex: 1299,
						},
					}}
				>
					{/* Drawer Header */}
					<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{ height: '36px' }}
						/>
						<IconButton onClick={toggleDrawer}>
							<CloseIcon />
						</IconButton>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Navigation Links */}
					<Box sx={{ padding: '0 20px 20px' }}>
						<Button
							onClick={() => {
								toggleDrawer();
								navigate({ to: "/patient/home" });
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Back to Home
						</Button>
						<Button
							onClick={() => {
								toggleDrawer();
								navigateToShop();
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Visit Private Shop
						</Button>
						<Button
							onClick={handleLogout}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none'
							}}
						>
							Logout
						</Button>
					</Box>
				</Drawer>

				{/* Main Content */}
				<Box sx={{ flex: 1, padding: "20px" }}>
					{/* Back Button */}
					<Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
						<Button
							variant="outlined"
							startIcon={<ArrowBack />}
							onClick={() => navigate({ to: "/patient/home" })}
							sx={{
								color: "#217F00",
								borderColor: "#217F00",
								fontSize: "16px",
								fontWeight: "bold",
								textTransform: "none",
								padding: "8px 16px",
								"&:hover": {
									backgroundColor: "rgba(33, 127, 0, 0.04)",
									borderColor: "#217F00",
								},
							}}
						>
							Back to Home
						</Button>
					</Box>

					<Grid
						container
						direction="column"
						alignItems="center"
						justifyContent="flex-start"
						spacing={2}
					>
						<Grid size={12}>
							<Stack direction="row" alignItems="center" justifyContent="center">
								<Box
									sx={{
										width: "32px",
										borderRadius: "15px 0 0 15px",
										height: "11px",
										backgroundColor: "green",
									}}
								/>
								{[...Array(3)].map((_, index) => (
									<Box
										key={index}
										sx={{
											width: "32px",
											height: "11px",
											backgroundColor: step > index + 1 ? "green" : "#EAEAEA",
											border: "0.5px solid rgba(89, 89, 89, 0.61)",
										}}
									/>
								))}
								<Box
									sx={{
										width: "32px",
										borderRadius: "0 15px 15px 0",
										height: "11px",
										backgroundColor: step === 5 ? "green" : "#EAEAEA",
										border: "0.5px solid rgba(89, 89, 89, 0.61)",
									}}
								/>
							</Stack>
						</Grid>
						<Grid size={12}>
							<Stack>
								<Typography variant="h4" fontWeight={800}>
									Questionnaire:
								</Typography>
								<Typography variant="h5" fontWeight={700} color="green">
									Add 22% THC Option
								</Typography>
							</Stack>
						</Grid>
						<Grid size={12}>{renderStep()}</Grid>
						<Grid size={9}>
							<Button
								fullWidth
								sx={{
									textTransform: "capitalize",
									borderRadius: "20px",
									opacity: (canProceed && !isLoading) ? 1 : 0.6
								}}
								variant="contained"
								color="success"
								disableElevation
								size="large"
								onClick={handleStepChange}
								disabled={!canProceed || isLoading}
							>
								{isLoading ? "Submitting..." : (step < 5 ? "Continue" : "Submit")}
							</Button>
							{!canProceed && !isLoading && (
								<Typography
									variant="body2"
									color="error"
									sx={{ mt: 1, textAlign: 'center' }}
								>
									Please answer all questions to continue
								</Typography>
							)}
						</Grid>
					</Grid>
				</Box>
			</Box>
		</ThemeProvider>
	);
};

export default Add22Thc;
