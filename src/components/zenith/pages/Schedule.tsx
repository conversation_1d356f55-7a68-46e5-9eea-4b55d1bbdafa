import React, { useEffect, useState } from "react";
import { styled, ThemeProvider } from "@mui/material/styles";
import {
  Typography,
  Box,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { useNavigate } from "@tanstack/react-location";
import axios from "axios";
import LoadingScreen from "../../../utils/loading-screen";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import { useFlow } from "../../../hooks/flow-controller";

// Add interface for user details
interface UserDetails {
  email?: string;
  zohoId?: string;
  [key: string]: any;
}

function Schedule() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useFlow();
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);

  // Check authentication and fetch user details
  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      // First check authentication
      const isAuthenticated = localStorage.getItem('zenith_authenticated') === 'true';

      if (!isAuthenticated) {
        // Hardcode the path like FormConsent and FormDischarge do
        // Determine which schedule path we're on
        const isPatientSchedule = window.location.pathname === '/patient/schedule' ||
                                 localStorage.getItem('initialPath') === '/patient/schedule';
                                 
        const currentPath = isPatientSchedule ? '/patient/schedule' : '/schedule';
        const loginUrl = (import.meta.env.VITE_ZENITH_LOGIN_URL || '/patient/login') +
                        `?return_to=${encodeURIComponent(currentPath)}`;
        window.location.href = loginUrl;
        return;
      }

      // Fetch user details if authenticated
      try {
        setIsLoading(true);
        const email = user?.email;

        if (!email) {
          enqueueSnackbar("Could not determine your email. Please try logging in again.", {
            variant: "error",
          });
          return;
        }

        // Fetch lead details from Zoho to get the zohoId
        const leadResponse = await axios.get(
          `${import.meta.env.VITE_API_URL}/zoho/v1.0/leads/by-email?email=${encodeURIComponent(email)}`,
          { withCredentials: true }
        );

        if (leadResponse.data?.success && leadResponse.data?.id) {
          const zohoId = leadResponse.data.id;
          setUserDetails({ email, zohoId });

          // Now redirect to the schedule with encrypted token
          await redirectToSchedule();
        } else {
          enqueueSnackbar("Could not find your account details. Please contact support.", {
            variant: "error",
          });
        }
      } catch (error) {
        console.error('Error fetching user details:', error);
        enqueueSnackbar("An error occurred while accessing your account. Please try again.", {
          variant: "error",
        });
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthAndRedirect();
  }, [user, enqueueSnackbar]);

  const redirectToSchedule = async () => {
    try {
      setIsLoading(true);

      // Get encrypted leadID from the backend
      const response = await axios.post(
        `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`,
        { user },
        { withCredentials: true }
      );

      if (response.data?.data?.leadID) {
        const encryptedLeadID = response.data.data.leadID;
        // Redirect to doctor.clinic/schedule with the encrypted token
        const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
        const url = `${baseUrl}?token=${encryptedLeadID}`;
        window.location.href = url;
      } else {
        enqueueSnackbar("Could not generate your scheduling link. Please contact support.", {
          variant: "error",
        });
      }
    } catch (error) {
      console.error('Error generating schedule link:', error);
      enqueueSnackbar("An error occurred while generating your scheduling link. Please try again.", {
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {isLoading && <LoadingScreen />}
      <ThemeProvider theme={zenithTheme}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '50vh',
            padding: 3,
          }}
        >
          <Grid container direction="column" alignItems="center" spacing={3}>
            <Grid>
              <Typography
                variant="h4"
                sx={{
                  fontSize: "32px",
                  fontWeight: "bold",
                  color: "green",
                  textAlign: "center",
                }}
              >
                Redirecting to Schedule
              </Typography>
            </Grid>
            <Grid>
              <Typography
                variant="body1"
                sx={{
                  fontSize: "16px",
                  textAlign: "center",
                  color: "#666",
                }}
              >
                Please wait while we prepare your scheduling link...
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </ThemeProvider>
    </>
  );
}

export default Schedule;
