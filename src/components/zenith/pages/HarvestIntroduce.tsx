import { <PERSON>, Button, Stack, <PERSON><PERSON><PERSON>ider, Typography } from "@mui/material";
import styles from "../../../css/harvest.module.css";
import zenithTheme from "../../../styles/zenith/theme";

function HarvestIntroduce() {
  function handleButtonToCommunity() {
    window.location.href =
      import.meta.env.VITE_HARVEST_FORUM || "https://harvest.delivery";
  }
  return (
    <>
      <ThemeProvider theme={zenithTheme}>
        <Stack gap={0} sx={{ width: "100%", maxWidth: "100vw" }}>
          <Box
            sx={{
              padding: 0,
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            <img
              width={350}
              height={170}
              src="/harvest/doctors-group.jpg"
              className={styles.imageDoctorsGroup}
            />
          </Box>
          <Typography
            sx={{
              fontSize: "40px",
              fontWeight: "700",
              lineHeight: "40px",
              padding: "10px 15px",
              color: "#4C4C4C",
            }}
          >
            Your <span style={{ color: "#007F00" }}>Natural</span> Health Allies
          </Typography>
          <Box
            sx={{
              paddingTop: 2,
              paddingBottom: 2,
              gap: 2,
              display: "flex",
              justifyContent: "center",
              alignContent: "center",
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <img width={95} height={23} src="/zenith/zenith-logo.png" />
            <span>X</span>
            <img width={95} height={23} src="/harvest/harvest_logo.svg" />
          </Box>
          <Typography
            sx={{
              textAlign: "start",
              mb: 1,
              padding: 2,
              fontSize: "16px",
              lineHeight: "18px",
              color: "#4C4C4C",
              fontWeight: 400,
            }}
          >
            Thank you for taking the first step with Zenith Clinics.{" "}
            <span style={{ color: "black", fontWeight: "bold" }}>
              Your pre-consult is booked
            </span>
            — and that alone shows courage, curiosity, and care for your own
            health.
          </Typography>
          <Typography
            sx={{
              textAlign: "start",
              mb: 3,
              paddingX: 2,
              paddingTop: 0,
              fontSize: "16px",
              lineHeight: "18px",
              color: "#4C4C4C",
              fontWeight: 400,
            }}
          >
            You’re not doing this alone.{" "}
            <span style={{ color: "black", fontWeight: "bold" }}>
              Through our partnership with Harvest
            </span>
            , we’re here to help you feel supported every step of the way.
          </Typography>
          <Box
            sx={{
              height: "245px",
            }}
          >
            <Typography
              sx={{
                height: "245px",
                padding: "10px",
                margin: "0px 10px",
                backgroundColor: "#EBEBEB",
                borderRadius: "18px",
                textAlign: "center",
                fontSize: "25px",
                lineHeight: "25px",
                color: "#FD6440",
                fontWeight: 800,
              }}
            >
              Who Is Harvest ?
            </Typography>
            <Box
              sx={{
                position: "relative",
                top: -200,
                boxShadow: "1px 1px 2px 1px rgba(0,0,0,0.15)",
                height: 175,
                backgroundColor: "white",
                borderRadius: "12px",
              }}
            >
              <Typography
                sx={{
                  textAlign: "start",
                  mb: 2,
                  height: 135,
                  padding: 2,
                  paddingBottom: 0,
                  fontSize: "16px",
                  lineHeight: "18px",
                  color: "#4C4C4C",
                  fontWeight: 400,
                }}
              >
                <span style={{ color: "black", fontWeight: "bold" }}>
                  Harvest stands for natural health, empowerment, and honesty.
                </span>
                We’re here for people who are tired of one-size-fits-all
                answers. We believe in breaking away from outdated systems — and
                making space for education, growth, and standing against Big
                Pharma’s synthetic solutions.
              </Typography>
              <Box sx={{ textAlign: "right", paddingRight: "10px" }}>
                <img width={63} height={16} src="/harvest/harvest_logo.svg" />
              </Box>
            </Box>
          </Box>
          <Typography
            sx={{
              textAlign: "center",
              mt: 2,
              padding: 2,
              fontSize: "25px",
              lineHeight: "18px",
              color: "#007F00",
              fontWeight: 700,
            }}
          >
            Harvest Is a Space To
          </Typography>
          <Box
            sx={{
              display: "flex",
              flexDirection: "row",
              alignItems: "center",
              padding: "20px 5px 5px 5px",
            }}
          >
            <Box
              sx={{
                width: "40%",
                display: "flex",
                alignItems: "end",
                justifyContent: "center",
                position: "relative",
                padding: "10px",
                height: "215px",
                borderRadius: "10px",
                backgroundColor: "#007F00",
              }}
            >
              <img
                height={285}
                style={{ top: 20, position: "relative" }}
                src="/zenith/smarth-phone.png"
              />
            </Box>
            <Box
              sx={{
                width: "60%",
                padding: "5px",
              }}
            >
              <Typography
                sx={{
                  textAlign: "start",
                  padding: 1,
                  fontSize: "17px",
                  lineHeight: "21px",
                  color: "#black",
                  fontWeight: 500,
                }}
              >
                <span style={{ color: "#007F00", fontWeight: "bold" }}>
                  Connect
                </span>{" "}
                with others passionate about natural living.
              </Typography>
              <Typography
                sx={{
                  textAlign: "start",
                  padding: 1,
                  fontSize: "17px",
                  lineHeight: "21px",
                  color: "#black",
                  fontWeight: 500,
                }}
              >
                <span style={{ color: "#007F00", fontWeight: "bold" }}>
                  Explore
                </span>{" "}
                topics like alternative care and cultivation.
              </Typography>
              <Typography
                sx={{
                  textAlign: "start",
                  padding: 1,
                  fontSize: "17px",
                  lineHeight: "21px",
                  color: "#black",
                  fontWeight: 500,
                }}
              >
                <span style={{ color: "#007F00", fontWeight: "bold" }}>
                  Share
                </span>{" "}
                your story and defy the pharmaceutical norm.
              </Typography>
            </Box>
          </Box>
          <Typography
            sx={{
              textAlign: "center",
              mt: 2,
              padding: 2,
              fontSize: "25px",
              lineHeight: "18px",
              color: "#007F00",
              fontWeight: 700,
            }}
          >
            Why It Matters
          </Typography>
          <Typography
            sx={{
              textAlign: "start",
              paddingX: 2,
              fontSize: "16px",
              lineHeight: "18px",
              color: "#4C4C4C",
              fontWeight: 400,
            }}
          >
            We know how it feels to be overlooked, rushed, or left with more
            questions than answers.
          </Typography>
          <Typography
            sx={{
              textAlign: "start",
              mb: 1,
              padding: 2,
              fontSize: "16px",
              lineHeight: "18px",
              color: "#4C4C4C",
              fontWeight: 400,
            }}
          >
            <span style={{ color: "black", fontWeight: "bold" }}>
              Harvest is about reclaiming your voice,
            </span>{" "}
            finding your people, and being part of something grounded, safe, and
            real.
          </Typography>
          <Box
            sx={{
              boxShadow: "1px 1px 2px 1px rgba(0,0,0,0.15)",
              marginX: "30px",
              display: "flex",
              height: "78px",
              padding: "8px",
              flexDirection: "column",
              borderRadius: "10px",
            }}
          >
            <Box
              sx={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "space-between",
              }}
            >
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  alignItems: "start",
                }}
              >
                <Typography sx={{ fontSize: 9, fontWeight: "bold" }}>
                  Alex T.
                </Typography>
                <Typography sx={{ fontSize: 8 }}>
                  <span style={{ fontWeight: "bold", color: "#007F00" }}>
                    Zenith
                  </span>
                  <span style={{ color: "#007F00" }}>Clinics</span>{" "}
                  <span>Patient</span>
                </Typography>
              </Box>

              <Box
                sx={{
                  display: "flex",
                  gap: "2px",
                  flexDirection: "row",
                  alignContent: "center",
                  alignItems: "center",
                }}
              >
                <img height={18} width={18} src="/zenith/ok.png" />
                <span
                  style={{
                    fontWeight: "bold",
                    color: "black",
                    fontSize: "12px",
                  }}
                >
                  Testimonial
                </span>
              </Box>
            </Box>
            <Typography
              sx={{
                textAlign: "start",
                mb: 2,
                height: 135,
                marginY: 2,
                fontSize: "10px",
                lineHeight: "100%",
                color: "#4C4C4C",
                fontWeight: 400,
              }}
            >
              “Harvest opened my eyes to natural options I never knew existed.”
            </Typography>
          </Box>
          <Typography
            sx={{
              textAlign: "center",
              mt: 2,
              padding: 2,
              fontSize: "25px",
              lineHeight: "18px",
              color: "#007F00",
              fontWeight: 700,
            }}
          >
            What Comes Next
          </Typography>
          <Typography
            sx={{
              textAlign: "start",
              mb: 1,
              paddingX: 2,
              fontSize: "14px",
              lineHeight: "18px",
              color: "#4C4C4C",
              fontWeight: 400,
            }}
          >
            After your consult, your doctor will determine whether alternative
            care is clinically appropriate. If suitable, they may guide you to
            the next steps. <br />
            In the meantime, you’re welcome to explore our
            partner community — a private forum for open discussion about
            natural alternatives, lifestyle, and real experiences.
          </Typography>
          <Box sx={{ position: "relative", height: "350px",mt:2 }}>
            <img
              style={{
                height: 290,
                width: 300,
                borderRadius: "8px",
              }}
              src="/zenith/phones.jpg"
            />
            <Box
              sx={{
                borderRadius: "10px",
                backgroundColor: "#007F00",
                position: "relative",
                top: "-100px",

                height: "175px",
                width: "210px",
              }}
            >
              <Typography
                sx={{
                  textAlign: "start",
                  padding: 2,
                  fontSize: "14px",
                  lineHeight: "18px",
                  color: "white",
                  fontWeight: 400,
                }}
              >
                Harvest is for education and conversation only. We don’t promote
                products or provide medical advice.{" "}
                <span style={{ fontWeight: "bold" }}>
                  Your healthcare journey is guided by your Zenith doctor
                </span>{" "}
              </Typography>
            </Box>
          </Box>
          <Typography
            sx={{
              textAlign: "center",
              mt: 4,
              padding: 2,
              fontSize: "25px",
              lineHeight: "18px",
              color: "#007F00",
              fontWeight: "bold",
            }}
          >
            You're Ready
          </Typography>
          <Typography
            sx={{
              textAlign: "center",
              fontSize: "14px",
              lineHeight: "18px",
              color: "#4C4C4C",
              fontWeight: 400,
            }}
          >
            <p>
              Click below to enter the Harvest Forum and begin exploring the
              community that’s here for you.
              <br />
              <span style={{ fontWeight: "bold" }}>
                Your people are waiting. Your path is unfolding
              </span>{" "}
            </p>
          </Typography>

          <Box
            sx={{
              height: "60px",
              zIndex: 99,
              width: "100%",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
              position: "sticky",
              bottom: 0,
            }}
          >
            <Button
              onClick={handleButtonToCommunity}
              sx={{
                color: "white",
                borderRadius: "30px",
                width: "80%",
                maxWidth: "270px",
                padding: "8px 16px",
                fontSize: "16px",
                fontWeight: "500",
                textTransform: "none",
                backgroundColor: "#FD6440",
              }}
            >
              Join The Community
            </Button>
          </Box>
        </Stack>
      </ThemeProvider>
    </>
  );
}

export default HarvestIntroduce;
