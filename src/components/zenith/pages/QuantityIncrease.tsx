import { useState, useEffect } from "react";
import {
	<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	ThemeProvider,
	Button,
	FormControl,
	FormLabel,
	FormGroup,
	FormControlLabel,
	Checkbox,
	RadioGroup,
	Radio,
	TextField,
	Slider,
	Select,
	MenuItem,
	Alert,
	<PERSON>rid2 as <PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Divider
} from "@mui/material";
import ArrowBack from "@mui/icons-material/ArrowBack";
import CloseIcon from "@mui/icons-material/Close";
import zenithTheme from "../../../styles/zenith/theme";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import { useFlow } from "../../../hooks/flow-controller";
import LoadingScreen from "../../../utils/loading-screen";
import axiosInstance from "../../../services/axios";
import Banner from "../layouts/Banner";
import { 
	QuantityIncreaseFormData, 
	QuantityIncreaseScoringState,
	USAGE_CONSISTENCY_OPTIONS,
	INTENDED_USAGE_OPTIONS,
	HEALTH_CHANGES_OPTIONS
} from "../../../types/quantityIncrease";
import {
	getAvailableStrengthOptions,
	calculateQuantityIncreaseScore,
	validateMultipleQuantityIncreaseRequests
} from "../../../utils/quantityIncreaseValidation";
import { TreatmentPlan } from "../../../types";

function QuantityIncrease() {
	const [step, setStep] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [canProceed, setCanProceed] = useState(false);
	const [treatmentPlan, setTreatmentPlan] = useState<TreatmentPlan | null>(null);
	const [availableOptions, setAvailableOptions] = useState<Array<{value: string, label: string, current: number, availableLevels: number[]}>>([]);
	const [drawerOpen, setDrawerOpen] = useState(false);

	// Form data state
	const [formData, setFormData] = useState<QuantityIncreaseFormData>({
		// Step 1: Reason for requesting quantity increase
		reasonNotLasting: false,
		reasonHigherDoses: false,
		reasonTolerance: false,
		reasonIncreasedSymptoms: false,
		reasonOther: false,
		reasonOtherText: "",

		// Step 2: Response to current treatment
		currentEffectiveness: "1",
		sideEffectsNone: false,
		sideEffectsMild: false,
		sideEffectsModerate: false,
		sideEffectsStrong: false,
		sideEffectsDescription: "",
		usageConsistency: "",
		usageConsistencyOther: "",

		// Step 3: Health changes
		healthChanges: "",
		healthChangesDescription: "",

		// Step 4: Expectations and preferences
		expectations: "",
		concerns: "",
		intendedUsage: "",
		intendedUsageOther: "",

		// Step 5: Consent
		consent: "",

		// Dynamic fields
		selectedStrengths: [],

		// 22% THC specific fields
		thc22Selected: false,
		thc22CurrentQuantity: 0,
		thc22RequestedQuantity: 0,

		// 29% THC specific fields
		thc29Selected: false,
		thc29CurrentQuantity: 0,
		thc29RequestedQuantity: 0
	});

	// Scoring state
	const [scoring, setScoring] = useState<QuantityIncreaseScoringState>({
		totalScore: 0,
		maxScore: 50,
		isEligible: false,
		questionScores: {}
	});

	const { user } = useFlow();
	const navigate = useNavigate();
	const { enqueueSnackbar } = useSnackbar();

	// Fetch treatment plan data on component mount
	useEffect(() => {
		const fetchTreatmentPlan = async () => {
			if (!user?.email) {
				navigate({ to: "/patient/login" });
				return;
			}

			setIsLoading(true);
			try {
				const response = await axiosInstance.get(
					`/zoho/v1.0/treatment-plan/by-email?email=${user.email}`,
					{ withCredentials: true }
				);

				if (response.data?.success && response.data?.treatmentPlan) {
					setTreatmentPlan(response.data.treatmentPlan);
					
					// Get available strength options
					const options = getAvailableStrengthOptions(response.data.treatmentPlan);
					setAvailableOptions(options);

					// Auto-populate current quantities for available strengths
					const updatedFormData: Partial<QuantityIncreaseFormData> = {};

					options.forEach(option => {
						if (option.value === '22' && option.availableLevels.length > 0) {
							updatedFormData.thc22CurrentQuantity = option.current;
							updatedFormData.thc22RequestedQuantity = option.availableLevels[0]; // Default to first available level
						}
						if (option.value === '29' && option.availableLevels.length > 0) {
							updatedFormData.thc29CurrentQuantity = option.current;
							updatedFormData.thc29RequestedQuantity = option.availableLevels[0]; // Default to first available level
						}
					});

					setFormData(prev => ({
						...prev,
						...updatedFormData
					}));
				} else {
					enqueueSnackbar("Unable to load treatment plan data", { variant: "error" });
					navigate({ to: "/patient/home" });
				}
			} catch (error) {
				console.error("Error fetching treatment plan:", error);
				enqueueSnackbar("Error loading treatment plan", { variant: "error" });
				navigate({ to: "/patient/home" });
			} finally {
				setIsLoading(false);
			}
		};

		fetchTreatmentPlan();
	}, [user?.email, navigate, enqueueSnackbar]);

	// Update scoring when form data changes
	useEffect(() => {
		const newScoring = calculateQuantityIncreaseScore(formData);
		setScoring(newScoring);
	}, [formData]);

	// Handle checkbox changes
	const handleCheckboxChange = (field: keyof QuantityIncreaseFormData, value: boolean) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	// Handle text field changes
	const handleTextChange = (field: keyof QuantityIncreaseFormData, value: string) => {
		setFormData(prev => ({ ...prev, [field]: value }));
	};

	// Handle strength selection change (checkbox)
	const handleStrengthSelectionChange = (strength: '22' | '29', selected: boolean) => {
		setFormData(prev => {
			const updatedSelectedStrengths = selected
				? [...prev.selectedStrengths, strength]
				: prev.selectedStrengths.filter(s => s !== strength);

			return {
				...prev,
				selectedStrengths: updatedSelectedStrengths,
				[`thc${strength}Selected`]: selected
			};
		});
	};

	// Handle requested quantity change for specific strength
	const handleRequestedQuantityChange = (strength: '22' | '29', quantity: number) => {
		setFormData(prev => ({
			...prev,
			[`thc${strength}RequestedQuantity`]: quantity
		}));
	};

	// Mobile drawer handlers
	const handleDrawerToggle = () => {
		setDrawerOpen(!drawerOpen);
	};

	const handleLogout = () => {
		// Clear any stored authentication data
		localStorage.removeItem('authToken');
		sessionStorage.clear();

		// Navigate to login page
		navigate({ to: "/patient/login" });
	};

	// Validate current step
	const validateStep = (stepNumber: number): boolean => {
		switch (stepNumber) {
			case 1:
				// Must select at least one reason and at least one strength with valid quantity
				const hasReason = formData.reasonNotLasting || formData.reasonHigherDoses ||
								 formData.reasonTolerance || formData.reasonIncreasedSymptoms ||
								 formData.reasonOther;
				const hasStrength = formData.selectedStrengths.length > 0;
				const hasValidQuantities = formData.selectedStrengths.every(strength => {
					if (strength === '22') return formData.thc22Selected && formData.thc22RequestedQuantity > 0;
					if (strength === '29') return formData.thc29Selected && formData.thc29RequestedQuantity > 0;
					return false;
				});
				const hasOtherText = !formData.reasonOther || formData.reasonOtherText.trim() !== "";
				return hasReason && hasStrength && hasValidQuantities && hasOtherText;
			
			case 2:
				// Must answer effectiveness, side effects, and usage consistency
				const hasEffectiveness = formData.currentEffectiveness !== "";
				const hasSideEffects = formData.sideEffectsNone || formData.sideEffectsMild || 
									  formData.sideEffectsModerate || formData.sideEffectsStrong;
				const hasUsageConsistency = formData.usageConsistency !== "";
				const hasUsageOther = formData.usageConsistency !== 'other' || formData.usageConsistencyOther.trim() !== "";
				const hasSideEffectsDesc = (!formData.sideEffectsModerate && !formData.sideEffectsStrong) || 
										  formData.sideEffectsDescription.trim() !== "";
				return hasEffectiveness && hasSideEffects && hasUsageConsistency && hasUsageOther && hasSideEffectsDesc;
			
			case 3:
				// Must answer health changes
				const hasHealthChanges = formData.healthChanges !== "";
				const hasHealthDesc = formData.healthChanges !== 'yes' || formData.healthChangesDescription.trim() !== "";
				return hasHealthChanges && hasHealthDesc;
			
			case 4:
				// Must provide expectations, concerns, and intended usage
				const hasExpectations = formData.expectations.trim() !== "";
				const hasConcerns = formData.concerns.trim() !== "";
				const hasIntendedUsage = formData.intendedUsage !== "";
				const hasIntendedOther = formData.intendedUsage !== 'other' || formData.intendedUsageOther.trim() !== "";
				return hasExpectations && hasConcerns && hasIntendedUsage && hasIntendedOther;
			
			case 5:
				// Must consent
				return formData.consent === "yes";
			
			default:
				return false;
		}
	};

	// Update canProceed when step or form data changes
	useEffect(() => {
		setCanProceed(validateStep(step));
	}, [step, formData]);

	// Navigation functions
	const handleNext = () => {
		if (canProceed && step < 5) {
			setStep(step + 1);
		}
	};

	// Get effectiveness label
	const getEffectivenessLabel = (value: number): string => {
		if (value === 10) return "10 - Complete relief, but still requiring more quantity";
		if (value === 1) return "1 - No benefit";
		return `${value}`;
	};

	// Submit questionnaire
	const submitQuestionnaire = async () => {
		setIsLoading(true);
		try {
			// Prepare requests for validation
			const requests = formData.selectedStrengths.map(strength => ({
				strength: strength as '22' | '29',
				requestedQuantity: formData[`thc${strength}RequestedQuantity` as keyof QuantityIncreaseFormData] as number
			}));

			// Validate the requests
			const validation = validateMultipleQuantityIncreaseRequests(treatmentPlan, requests);

			if (!validation.isValid) {
				enqueueSnackbar(validation.errors.join('; ') || "Invalid request", { variant: "error" });
				setIsLoading(false);
				return;
			}

			// Create grouped questions and answers array for better doctor review
			const questionsAndAnswers = [];

			// Question 1: Reason for requesting quantity increase
			const reasonSelections = [];
			if (formData.reasonNotLasting) reasonSelections.push("The current quantity is not lasting the full month");
			if (formData.reasonHigherDoses) reasonSelections.push("I need more frequent or higher doses to manage symptoms");
			if (formData.reasonTolerance) reasonSelections.push("I've developed tolerance and need more to achieve the same benefit");
			if (formData.reasonIncreasedSymptoms) reasonSelections.push("My symptoms have increased (e.g. flare-ups, stress, pain)");
			if (formData.reasonOther && formData.reasonOtherText) reasonSelections.push(`Other: ${formData.reasonOtherText}`);

			questionsAndAnswers.push({
				questionKey: "reasonForRequest",
				questionText: "Why are you requesting an increase in the amount of medicinal cannabis approved under your current treatment plan?",
				answerValue: reasonSelections,
				answerText: reasonSelections.join(", "),
				score: scoring.questionScores.reasonForRequest || 0
			});

			// Question 2: Current effectiveness
			questionsAndAnswers.push({
				questionKey: "currentEffectiveness",
				questionText: "On a scale of 1 to 10, how well has your current quantity helped manage your symptoms?",
				answerValue: formData.currentEffectiveness,
				answerText: getEffectivenessLabel(parseInt(formData.currentEffectiveness)),
				score: scoring.questionScores.currentEffectiveness || 0
			});

			// Question 3: Side effects
			const sideEffectSelections = [];
			if (formData.sideEffectsNone) sideEffectSelections.push("None");
			if (formData.sideEffectsMild) sideEffectSelections.push("Mild (e.g. dry mouth, light drowsiness)");
			if (formData.sideEffectsModerate) sideEffectSelections.push("Moderate (e.g. nausea, dizziness, memory issues)");
			if (formData.sideEffectsStrong) sideEffectSelections.push("Strong (e.g. anxiety, confusion, rapid heart rate)");

			questionsAndAnswers.push({
				questionKey: "sideEffects",
				questionText: "Have you experienced any side effects with your current dose?",
				answerValue: sideEffectSelections,
				answerText: sideEffectSelections.join(", "),
				score: scoring.questionScores.sideEffects || 0
			});

			// Question 4: Side effects description (if provided)
			if (formData.sideEffectsDescription && formData.sideEffectsDescription.trim()) {
				questionsAndAnswers.push({
					questionKey: "sideEffectsDescription",
					questionText: "Please describe your side effects:",
					answerValue: formData.sideEffectsDescription,
					answerText: formData.sideEffectsDescription,
					score: 0
				});
			}

			// Question 5: Usage consistency
			const usageConsistencyText = USAGE_CONSISTENCY_OPTIONS.find(opt => opt.value === formData.usageConsistency)?.label || formData.usageConsistency;
			const finalUsageText = formData.usageConsistency === 'other' && formData.usageConsistencyOther ?
								  `Other: ${formData.usageConsistencyOther}` : usageConsistencyText;

			questionsAndAnswers.push({
				questionKey: "usageConsistency",
				questionText: "Have you been using your current prescribed amount consistently?",
				answerValue: formData.usageConsistency,
				answerText: finalUsageText,
				score: scoring.questionScores.usageConsistency || 0
			});

			// Question 6: Health changes
			const healthChangesText = HEALTH_CHANGES_OPTIONS.find(opt => opt.value === formData.healthChanges)?.label || formData.healthChanges;
			questionsAndAnswers.push({
				questionKey: "healthChanges",
				questionText: "Have there been any changes in your health, medications, or lifestyle since your last doctor review?",
				answerValue: formData.healthChanges,
				answerText: healthChangesText,
				score: scoring.questionScores.healthChanges || 0
			});

			// Question 7: Health changes description (if provided)
			if (formData.healthChangesDescription && formData.healthChangesDescription.trim()) {
				questionsAndAnswers.push({
					questionKey: "healthChangesDescription",
					questionText: "Please describe the changes:",
					answerValue: formData.healthChangesDescription,
					answerText: formData.healthChangesDescription,
					score: 0
				});
			}

			// Question 8: Expectations
			questionsAndAnswers.push({
				questionKey: "expectations",
				questionText: "What do you hope to achieve by increasing the quantity of your medicinal cannabis?",
				answerValue: formData.expectations,
				answerText: formData.expectations,
				score: scoring.questionScores.expectations || 0
			});

			// Question 9: Concerns
			questionsAndAnswers.push({
				questionKey: "concerns",
				questionText: "Do you have any concerns about increasing the quantity?",
				answerValue: formData.concerns,
				answerText: formData.concerns,
				score: scoring.questionScores.concerns || 0
			});

			// Question 10: Intended usage
			const intendedUsageText = INTENDED_USAGE_OPTIONS.find(opt => opt.value === formData.intendedUsage)?.label || formData.intendedUsage;
			const finalIntendedText = formData.intendedUsage === 'other' && formData.intendedUsageOther ?
									 `Other: ${formData.intendedUsageOther}` : intendedUsageText;

			questionsAndAnswers.push({
				questionKey: "intendedUsage",
				questionText: "How do you intend to use the increased quantity?",
				answerValue: formData.intendedUsage,
				answerText: finalIntendedText,
				score: scoring.questionScores.intendedUsage || 0
			});

			// Question 11: Consent
			questionsAndAnswers.push({
				questionKey: "consent",
				questionText: "Do you consent to your doctor reviewing this information and accessing your My Health Record (if needed) to determine whether a quantity increase is clinically appropriate?",
				answerValue: formData.consent,
				answerText: formData.consent === 'yes' ? 'Yes' : 'No',
				score: scoring.questionScores.consent || 0
			});

			// Prepare strength requests
			const strengthRequests = formData.selectedStrengths.map(strength => {
				const currentQuantity = formData[`thc${strength}CurrentQuantity` as keyof QuantityIncreaseFormData] as number;
				const requestedQuantity = formData[`thc${strength}RequestedQuantity` as keyof QuantityIncreaseFormData] as number;
				return {
					strength,
					currentQuantity,
					requestedQuantity,
					increaseAmount: requestedQuantity - currentQuantity
				};
			});

			// Prepare submission data
			const submissionData = {
				questionsAndAnswers,
				selectedStrengths: formData.selectedStrengths,
				strengthRequests,
				totalScore: scoring.totalScore,
				maxScore: scoring.maxScore,
				isEligible: scoring.isEligible,
				submittedAt: new Date().toISOString(),
			};

			const result = await axiosInstance.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/quantity-increase-questionnaire`,
				submissionData,
				{ withCredentials: true }
			);

			if (result.data) {
				// Show results based on eligibility
				const strengthsText = formData.selectedStrengths.length === 1
					? `${formData.selectedStrengths[0]}% THC`
					: `${formData.selectedStrengths.join('% and ')}% THC`;

				if (scoring.isEligible) {
					enqueueSnackbar(`Your quantity increase request for ${strengthsText} is under review.`, {
						variant: "success",
					});
				} else {
					enqueueSnackbar(`Your quantity increase request for ${strengthsText} is under review.`, {
						variant: "success",
					});
				}

				// Navigate back to home page to show approval/rejection status
				navigate({ to: "/patient/home" });
			}
		} catch (error) {
			console.error('Error submitting quantity increase questionnaire:', error);
			enqueueSnackbar("Failed to submit questionnaire. Please try again.", { variant: "error" });
		} finally {
			setIsLoading(false);
		}
	};

	// Step 1: Reason for requesting quantity increase + Strength selection
	const stepOne = () => (
		<Stack sx={{ flexGrow: 1 }}>
			{/* Strength Selection */}
			<Box sx={{ mb: 4 }}>
				<FormControl fullWidth>
					<FormLabel sx={{ color: "black", fontWeight: "bold", mb: 2 }}>
						Which strength(s) would you like to increase? (Select all that apply)
					</FormLabel>
					<FormGroup>
						{availableOptions.map((option) => (
							<Box key={option.value} sx={{ mb: 3, p: 2, border: "1px solid #ddd", borderRadius: 2 }}>
								<FormControlLabel
									control={
										<Checkbox
											checked={formData[`thc${option.value}Selected` as keyof QuantityIncreaseFormData] as boolean}
											onChange={(e) => handleStrengthSelectionChange(option.value as '22' | '29', e.target.checked)}
										/>
									}
									label={
										<Box>
											<Typography variant="body1" sx={{ fontWeight: "bold" }}>
												{option.value}% THC
											</Typography>
											<Typography variant="body2" sx={{ color: "#666" }}>
												Current: {option.current}g per month → Available up to {Math.max(...option.availableLevels)}g
											</Typography>
										</Box>
									}
									sx={{
										alignItems: 'flex-start',
										'.MuiFormControlLabel-label': {
											mt: 0.5,
											textAlign: 'left',
											display: 'block'
										}
									}}
								/>

								{/* Quantity Selection for this strength */}
								{formData[`thc${option.value}Selected` as keyof QuantityIncreaseFormData] && (
									<Box sx={{ mt: 2, ml: 4 }}>
										<FormLabel sx={{ color: "black", fontWeight: "bold", mb: 1, display: "block" }}>
											Select new quantity for {option.value}% THC:
										</FormLabel>
										<Select
											value={formData[`thc${option.value}RequestedQuantity` as keyof QuantityIncreaseFormData] as number}
											onChange={(e) => handleRequestedQuantityChange(option.value as '22' | '29', Number(e.target.value))}
											fullWidth
											size="small"
										>
											{option.availableLevels.map((level) => (
												<MenuItem key={level} value={level}>
													{level}g per month (+{level - option.current}g increase)
												</MenuItem>
											))}
										</Select>
									</Box>
								)}
							</Box>
						))}
					</FormGroup>
				</FormControl>
			</Box>

			{/* Show selected increase details */}
			{formData.selectedStrengths.length > 0 && (
				<Alert severity="info" sx={{ mb: 3 }}>
					<Typography variant="body2" sx={{ fontWeight: "bold", mb: 1 }}>
						Requesting increases:
					</Typography>
					{formData.selectedStrengths.map(strength => {
						const currentQty = formData[`thc${strength}CurrentQuantity` as keyof QuantityIncreaseFormData] as number;
						const requestedQty = formData[`thc${strength}RequestedQuantity` as keyof QuantityIncreaseFormData] as number;
						return (
							<Typography key={strength} variant="body2" sx={{ ml: 1 }}>
								• {strength}% THC: {currentQty}g → {requestedQty}g (+{requestedQty - currentQty}g)
							</Typography>
						);
					})}
				</Alert>
			)}

			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Why are you requesting an increase in the amount of medicinal cannabis approved under your current treatment plan? (Select all that apply)
				</FormLabel>
				<FormGroup sx={{ mt: 2 }}>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonNotLasting}
								onChange={(e) => handleCheckboxChange('reasonNotLasting', e.target.checked)}
							/>
						}
						label="The current quantity is not lasting the full month"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonHigherDoses}
								onChange={(e) => handleCheckboxChange('reasonHigherDoses', e.target.checked)}
							/>
						}
						label="I need more frequent or higher doses to manage symptoms"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonTolerance}
								onChange={(e) => handleCheckboxChange('reasonTolerance', e.target.checked)}
							/>
						}
						label="I've developed tolerance and need more to achieve the same benefit"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonIncreasedSymptoms}
								onChange={(e) => handleCheckboxChange('reasonIncreasedSymptoms', e.target.checked)}
							/>
						}
						label="My symptoms have increased (e.g. flare-ups, stress, pain)"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.reasonOther}
								onChange={(e) => handleCheckboxChange('reasonOther', e.target.checked)}
							/>
						}
						label="Other (please describe):"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					{formData.reasonOther && (
						<TextField
							fullWidth
							multiline
							rows={2}
							value={formData.reasonOtherText}
							onChange={(e) => handleTextChange('reasonOtherText', e.target.value)}
							placeholder="Please describe your other reason..."
							sx={{ mt: 1, mb: 2 }}
						/>
					)}
				</FormGroup>
			</FormControl>
		</Stack>
	);

	// Step 2: Response to current treatment
	const stepTwo = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					On a scale of 1 to 10, how well has your current quantity helped manage your symptoms?
				</FormLabel>
				<Typography sx={{ marginBottom: "10px", mt: 1 }}>
					Rate your symptom improvement (1 = no benefit, 10 = complete relief, but still requiring more quantity):
				</Typography>
				<Box sx={{ padding: "0 20px", marginBottom: "20px" }}>
					<Slider
						value={parseInt(formData.currentEffectiveness)}
						onChange={(_, value) => handleTextChange('currentEffectiveness', value.toString())}
						min={1}
						max={10}
						step={1}
						marks
						valueLabelDisplay="on"
					/>
				</Box>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Have you experienced any side effects with your current dose? (Select all that apply)
				</FormLabel>
				<FormGroup sx={{ mt: 2 }}>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsNone}
								onChange={(e) => handleCheckboxChange('sideEffectsNone', e.target.checked)}
							/>
						}
						label="None"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsMild}
								onChange={(e) => handleCheckboxChange('sideEffectsMild', e.target.checked)}
							/>
						}
						label="Mild (e.g. dry mouth, light drowsiness)"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsModerate}
								onChange={(e) => handleCheckboxChange('sideEffectsModerate', e.target.checked)}
							/>
						}
						label="Moderate (e.g. nausea, dizziness, memory issues)"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						control={
							<Checkbox
								checked={formData.sideEffectsStrong}
								onChange={(e) => handleCheckboxChange('sideEffectsStrong', e.target.checked)}
							/>
						}
						label="Strong (e.g. anxiety, confusion, rapid heart rate)"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
				</FormGroup>
				{(formData.sideEffectsModerate || formData.sideEffectsStrong) && (
					<TextField
						fullWidth
						multiline
						rows={3}
						value={formData.sideEffectsDescription}
						onChange={(e) => handleTextChange('sideEffectsDescription', e.target.value)}
						placeholder="Please describe your side effects briefly..."
						sx={{ marginTop: "10px", marginLeft: "32px" }}
					/>
				)}
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Have you been using your current prescribed amount consistently?
				</FormLabel>
				<RadioGroup
					value={formData.usageConsistency}
					onChange={(e) => handleTextChange('usageConsistency', e.target.value)}
					sx={{ mt: 2 }}
				>
					{USAGE_CONSISTENCY_OPTIONS.map((option) => (
						<FormControlLabel
							key={option.value}
							value={option.value}
							control={<Radio />}
							label={option.label}
							sx={{
								alignItems: 'flex-start',
								marginBottom: 1,
								'.MuiFormControlLabel-label': {
									mt: 0.5,
									textAlign: 'left',
									display: 'block'
								}
							}}
						/>
					))}
				</RadioGroup>
				{formData.usageConsistency === 'other' && (
					<TextField
						fullWidth
						multiline
						rows={2}
						value={formData.usageConsistencyOther}
						onChange={(e) => handleTextChange('usageConsistencyOther', e.target.value)}
						placeholder="Please explain your usage pattern..."
						sx={{ marginTop: "10px", marginLeft: "32px" }}
					/>
				)}
			</FormControl>
		</Stack>
	);

	// Step 3: Health changes
	const stepThree = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Have there been any changes in your health, medications, or lifestyle since your last doctor review?
				</FormLabel>
				<RadioGroup
					value={formData.healthChanges}
					onChange={(e) => handleTextChange('healthChanges', e.target.value)}
					sx={{ mt: 2 }}
				>
					{HEALTH_CHANGES_OPTIONS.map((option) => (
						<FormControlLabel
							key={option.value}
							value={option.value}
							control={<Radio />}
							label={option.label}
							sx={{
								alignItems: 'flex-start',
								marginBottom: 1,
								'.MuiFormControlLabel-label': {
									mt: 0.5,
									textAlign: 'left',
									display: 'block'
								}
							}}
						/>
					))}
				</RadioGroup>
				{formData.healthChanges === 'yes' && (
					<TextField
						fullWidth
						multiline
						rows={4}
						value={formData.healthChangesDescription}
						onChange={(e) => handleTextChange('healthChangesDescription', e.target.value)}
						placeholder="Please describe the changes in your health, medications, or lifestyle..."
						sx={{ marginTop: "10px", marginLeft: "32px" }}
					/>
				)}
			</FormControl>
		</Stack>
	);

	// Step 4: Expectations and preferences
	const stepFour = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					What do you hope to achieve by increasing the quantity of your medicinal cannabis?
				</FormLabel>
				<TextField
					fullWidth
					multiline
					rows={3}
					value={formData.expectations}
					onChange={(e) => handleTextChange('expectations', e.target.value)}
					placeholder="Describe your expectations and goals..."
					sx={{ marginTop: "10px" }}
				/>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Do you have any concerns about increasing the quantity?
				</FormLabel>
				<TextField
					fullWidth
					multiline
					rows={3}
					value={formData.concerns}
					onChange={(e) => handleTextChange('concerns', e.target.value)}
					placeholder="e.g. potential for dependency, reduced effect over time, cost..."
					sx={{ marginTop: "10px" }}
				/>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					How do you intend to use the increased quantity?
				</FormLabel>
				<RadioGroup
					value={formData.intendedUsage}
					onChange={(e) => handleTextChange('intendedUsage', e.target.value)}
					sx={{ mt: 2 }}
				>
					{INTENDED_USAGE_OPTIONS.map((option) => (
						<FormControlLabel
							key={option.value}
							value={option.value}
							control={<Radio />}
							label={option.label}
							sx={{
								alignItems: 'flex-start',
								marginBottom: 1,
								'.MuiFormControlLabel-label': {
									mt: 0.5,
									textAlign: 'left',
									display: 'block'
								}
							}}
						/>
					))}
				</RadioGroup>
				{formData.intendedUsage === 'other' && (
					<TextField
						fullWidth
						multiline
						rows={2}
						value={formData.intendedUsageOther}
						onChange={(e) => handleTextChange('intendedUsageOther', e.target.value)}
						placeholder="Please describe how you intend to use the increased quantity..."
						sx={{ marginTop: "10px", marginLeft: "32px" }}
					/>
				)}
			</FormControl>
		</Stack>
	);

	// Step 5: Consent
	const stepFive = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel sx={{ color: "black", fontWeight: "bold" }}>
					Do you consent to your doctor reviewing this information and accessing your My Health Record (if needed) to determine whether a quantity increase is clinically appropriate?
				</FormLabel>
				<RadioGroup
					value={formData.consent}
					onChange={(e) => handleTextChange('consent', e.target.value)}
					sx={{ mt: 2 }}
				>
					<FormControlLabel
						value="yes"
						control={<Radio />}
						label="Yes"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
					<FormControlLabel
						value="no"
						control={<Radio />}
						label="No"
						sx={{
							alignItems: 'flex-start',
							marginBottom: 1,
							'.MuiFormControlLabel-label': {
								mt: 0.5,
								textAlign: 'left',
								display: 'block'
							}
						}}
					/>
				</RadioGroup>
				{formData.consent === 'no' && (
					<Typography sx={{ marginTop: "10px", marginLeft: "32px", color: "#d32f2f" }}>
						If you do not consent, we cannot process your request. Please contact our support team for guidance.
					</Typography>
				)}
				{formData.consent === 'yes' && (
					<Typography sx={{ marginTop: "10px", marginLeft: "32px", color: "#666" }}>
						If approved, your treatment plan will be updated, and you'll be notified within approximately 48 hours. We recommend continuing to monitor your usage and symptoms using a tracking tool like OnTracka.
					</Typography>
				)}
			</FormControl>
		</Stack>
	);

	// Render current step content
	const renderStep = () => {
		switch (step) {
			case 1: return stepOne();
			case 2: return stepTwo();
			case 3: return stepThree();
			case 4: return stepFour();
			case 5: return stepFive();
			default: return stepOne();
		}
	};

	if (isLoading) {
		return <LoadingScreen />;
	}

	return (
		<ThemeProvider theme={zenithTheme}>
			<Box sx={{ minHeight: "100vh", backgroundColor: "#f5f5f5", display: "flex", flexDirection: "column" }}>
				<Banner />

				{/* Mobile Drawer */}
				<Drawer
					anchor="left"
					open={drawerOpen}
					onClose={handleDrawerToggle}
					sx={{
						'& .MuiDrawer-paper': {
							width: 250,
							backgroundColor: '#f8f9fa',
							padding: '20px'
						}
					}}
				>
					<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
						<Typography variant="h6" sx={{ fontWeight: 'bold', color: '#333' }}>
							Menu
						</Typography>
						<IconButton onClick={handleDrawerToggle}>
							<CloseIcon />
						</IconButton>
					</Box>
					<Box sx={{ display: 'flex', flexDirection: 'column' }}>
						<Button
							onClick={() => {
								navigate({ to: "/patient/home" });
								setDrawerOpen(false);
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Home
						</Button>
						<Button
							onClick={() => {
								window.open(import.meta.env.VITE_ZENITH_PRIVATE_SHOP_URL, '_blank');
								setDrawerOpen(false);
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Visit Private Shop
						</Button>
						<Button
							onClick={handleLogout}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none'
							}}
						>
							Logout
						</Button>
					</Box>
				</Drawer>

				{/* Main Content */}
				<Box sx={{ flex: 1, padding: "20px" }}>
					{/* Back Button */}
					<Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
						<Button
							variant="outlined"
							startIcon={<ArrowBack />}
							onClick={() => navigate({ to: "/patient/home" })}
							sx={{
								color: "#217F00",
								borderColor: "#217F00",
								fontSize: "16px",
								fontWeight: "bold",
								textTransform: "none",
								padding: "8px 16px",
								"&:hover": {
									backgroundColor: "rgba(33, 127, 0, 0.04)",
									borderColor: "#217F00",
								},
							}}
						>
							Back to Home
						</Button>
					</Box>

					<Grid
						container
						direction="column"
						alignItems="center"
						justifyContent="flex-start"
						spacing={2}
					>
						<Grid size={12}>
							<Stack direction="row" alignItems="center" justifyContent="center">
								<Box
									sx={{
										width: "32px",
										borderRadius: "15px 0 0 15px",
										height: "11px",
										backgroundColor: "green",
									}}
								/>
								{[...Array(3)].map((_, index) => (
									<Box
										key={index}
										sx={{
											width: "32px",
											height: "11px",
											backgroundColor: step > index + 1 ? "green" : "#EAEAEA",
											border: "0.5px solid rgba(89, 89, 89, 0.61)",
										}}
									/>
								))}
								<Box
									sx={{
										width: "32px",
										borderRadius: "0 15px 15px 0",
										height: "11px",
										backgroundColor: step === 5 ? "green" : "#EAEAEA",
										border: "0.5px solid rgba(89, 89, 89, 0.61)",
									}}
								/>
							</Stack>
						</Grid>
						<Grid size={12}>
							<Stack>
								<Typography variant="h4" fontWeight={800}>
									Questionnaire:
								</Typography>
								<Typography variant="h5" fontWeight={700} color="green">
									Request Quantity Increase
								</Typography>
							</Stack>
						</Grid>
						<Grid size={12}>{renderStep()}</Grid>
						<Grid size={9}>
							<Button
								fullWidth
								sx={{
									textTransform: "capitalize",
									borderRadius: "20px",
									opacity: (canProceed && !isLoading) ? 1 : 0.6
								}}
								variant="contained"
								color="success"
								disableElevation
								size="large"
								onClick={step < 5 ? handleNext : submitQuestionnaire}
								disabled={!canProceed || isLoading}
							>
								{isLoading ? "Submitting..." : (step < 5 ? "Continue" : "Submit")}
							</Button>
							{!canProceed && !isLoading && (
								<Typography
									variant="body2"
									color="error"
									sx={{ mt: 1, textAlign: 'center' }}
								>
									Please answer all questions to continue
								</Typography>
							)}
						</Grid>
					</Grid>
				</Box>
			</Box>
		</ThemeProvider>
	);
}

export default QuantityIncrease;
