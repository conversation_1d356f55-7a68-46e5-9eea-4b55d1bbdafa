import { Button, Typography } from "@mui/material";
import Grid from "@mui/material/Grid2";

const NotApproved = () => {
	return (
		<Grid
			container
			direction="column"
			sx={{ width: "100%" }}
			justifyContent="center"
			alignItems="center"
			textAlign="center"
			spacing={2}
		>
			<Typography
				sx={{
					fontSize: "24px",
					fontWeight: "bold",
					lineHeight: "1em",
				}}
			>
				You Have Not Been Approved
			</Typography>
			<img src="/zenith/cancel.png" alt="Not Approved" style={{ width: "200px", height: "auto" }} />
			<Grid
				size={12}
				sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
				textAlign="start"
				container
				direction="column"
				alignItems="center"
			>
				<Typography
					sx={{
						fontSize: "16px",
						lineHeight: "1em",
						paddingBottom: "10px",
					}}
				>
					Based on your responses, you do not currently meet the eligibility criteria for a free doctor
					consultation. <br /> <br />
					We appreciate your time and interest in our clinic, and we wish you the very best on your health
					journey.
				</Typography>
				{/* <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={() => {
                        // Handle button click, e.g., close dialog or navigate away
                    }}
                >
                    Contact Us
                </Button> */}
			</Grid>
		</Grid>
	);
};

export default NotApproved;
