import { useEffect, useState } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  ThemeProvider,
  useMediaQuery,
  Button,
  IconButton,
  <PERSON><PERSON>,
} from "@mui/material";

import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate, useSearch } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import { useFlow } from "../../../hooks/flow-controller";
import Banner from "../layouts/Banner";
import axiosInstance from "../../../services/axios";
import CachedImage from "../common/CachedImage";

function Chat() {
  const [isLoading, setIsLoading] = useState(true);
  const [chatUrl, setChatUrl] = useState<string>("");
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [contactId, setContactId] = useState<string | null>(null);
  const { user } = useFlow();
  const navigate = useNavigate();
  const search = useSearch();
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    const initializeChat = async () => {
      // Prevent multiple initializations
      if (chatUrl) return;

      if (!user?.email) {
        enqueueSnackbar("User information not available. Please log in again.", {
          variant: "error",
        });
        // Use setTimeout to prevent immediate navigation loop
        setTimeout(() => navigate({ to: "/patient/login" }), 100);
        return;
      }

      // Get the token from URL parameters
      const token = search.token as string;

      if (!token) {
        enqueueSnackbar("Chat token not provided. Please try again from your profile page.", {
          variant: "error",
        });
        // Use setTimeout to prevent immediate navigation loop
        setTimeout(() => navigate({ to: "/patient/profile" }), 100);
        return;
      }

      try {
        setIsLoading(true);

        // Determine the base URL based on environment
        const isDevelopment = import.meta.env.DEV;
        const baseUrl = isDevelopment
          ? "https://zenith.ad/patient-chat"
          : `${import.meta.env.VITE_DRUI_URL}/patient-chat`;

        // Construct the chat URL with the token
        const fullChatUrl = `${baseUrl}?token=${encodeURIComponent(token)}`;
        setChatUrl(fullChatUrl);

      } catch (error) {
        console.error("Error initializing chat:", error);
        enqueueSnackbar("Failed to initialize chat. Please try again later.", {
          variant: "error",
        });
        // Use setTimeout to prevent immediate navigation loop
        setTimeout(() => navigate({ to: "/patient/profile" }), 100);
      } finally {
        setIsLoading(false);
      }
    };

    initializeChat();
  }, [user?.email, search.token]); // Simplified dependencies

  // Fetch contactId for navigation actions
  useEffect(() => {
    const fetchContactId = async () => {
      if (!user?.email || contactId) return;

      try {
        const response = await axiosInstance.get(
          `/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`
        );
        if (response.data?.contactId) {
          setContactId(response.data.contactId);
        }
      } catch (error) {
        console.error("Error fetching contact ID:", error);
      }
    };

    fetchContactId();
  }, [user?.email, contactId]);

  // Redirect to login if user is not authenticated or missing token
  useEffect(() => {
    // Only run on client
    if (typeof window === "undefined") return;

    // Only redirect if we're actually on the chat page
    if (!window.location.pathname.startsWith('/patient/chat')) return;

    const token = search.token as string;

    // Check if user is not authenticated or token is missing
    if (!user?.email || !token) {
      // Use hardcoded path to prevent redirect loops (similar to FormConsent.tsx)
      const currentPath = '/patient/chat';
      const loginUrl = (import.meta.env.VITE_ZENITH_LOGIN_URL || '/patient/login') +
                      `?return_to=${encodeURIComponent(currentPath)}`;
      window.location.href = loginUrl;
    }
  }, [user?.email, search.token]);

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('zenith_auth_user');
    localStorage.removeItem('zenith_authenticated');

    // Close drawer
    toggleDrawer();

    // Redirect to login page
    navigate({ to: "/patient/login" });
  };

  const navigateToShop = () => {
    if (user?.email && contactId) {
      const safeContactId = contactId.startsWith('p') ? contactId : `p${contactId}`;
      const shopUrl = `https://letsroll.harvest.delivery/members-shop?email=${encodeURIComponent(user.email)}&contact=${encodeURIComponent(safeContactId)}`;
      window.location.href = shopUrl;
    } else {
      enqueueSnackbar("Unable to access shop. Please contact support.", {
        variant: "error",
      });
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ThemeProvider theme={zenithTheme}>
      <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh", backgroundColor: "white" }}>
        {/* Custom Header */}
        <Box sx={{ width: "100%" }}>
          <Banner />

          {/* Nav Bar */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: "0 20px",
              backgroundColor: "white",
              paddingTop: { xs: '50px', sm: '58px' }, // Account for banner height + some spacing
              paddingBottom: "10px"
            }}
          >
            <IconButton
              aria-label="menu"
              sx={{ padding: "8px" }}
              onClick={toggleDrawer}
            >
              <MenuIcon />
            </IconButton>

            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{
                height: "19.14px",
              }}
            />
          </Box>
        </Box>

        {/* Navigation Drawer */}
        <Drawer
          anchor="top"
          open={drawerOpen}
          onClose={toggleDrawer}
          keepMounted={true}
          sx={{
            '& .MuiDrawer-paper': {
              width: '100%',
              maxWidth: '100%',
              boxSizing: 'border-box',
              padding: '20px 0 0 0',
              height: 'auto',
              maxHeight: '500px',
              overflowY: 'auto',
              borderBottom: '1px solid #e0e0e0',
              top: { xs: '40px', sm: '48px' }, // Push down by banner height
              zIndex: 1299, // Just below the banner
            },
          }}
        >
          {/* Drawer Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{ height: '36px' }}
            />
            <IconButton onClick={toggleDrawer}>
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Divider */}
          <Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

          {/* Navigation Links */}
          <Box sx={{ padding: '0 20px 20px' }}>
            <Button
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
              onClick={() => {
                toggleDrawer();
                navigate({ to: "/patient/home" });
              }}
            >
              Back to Home
            </Button>
            <Button
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
              onClick={() => {
                toggleDrawer();
                navigate({ to: "/patient/profile" });
              }}
            >
              View Treatment Plan
            </Button>
            <Button
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
              onClick={() => {
                toggleDrawer();
                navigateToShop();
              }}
            >
              Visit Private Shop
            </Button>
            <Button
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none'
              }}
              onClick={handleLogout}
            >
              Logout
            </Button>
          </Box>
        </Drawer>

        {/* Main Content */}
        <Box
          sx={{
            flexGrow: 1,
            display: "flex",
            flexDirection: "column",
            padding: "20px",
            backgroundColor: "white",
          }}
        >
        {/* Back Button */}
        <Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start" }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate({ to: "/patient/home" })}
            sx={{
              color: "#217F00",
              borderColor: "#217F00",
              fontSize: "16px",
              fontWeight: "bold",
              textTransform: "none",
              padding: "8px 16px",
              "&:hover": {
                backgroundColor: "rgba(33, 127, 0, 0.04)",
                borderColor: "#217F00",
              },
            }}
          >
            Back to Home
          </Button>
        </Box>

        {/* Header */}
        <Box
          sx={{
            padding: "20px 0",
            marginBottom: 2,
            backgroundColor: "#f5f5f5",
            borderRadius: "10px",
          }}
        >
          <Typography
            variant="h4"
            sx={{
              fontSize: isDesktopOrMobile ? "28px" : "20px",
              fontWeight: "bold",
              lineHeight: "1.2",
              textAlign: "center",
              color: "#333",
              marginBottom: 0,
            }}
          >
            {isDesktopOrMobile
              ? "Need Help With Your Treatment? Talk to Your Doctor Now"
              : "Talk to Your Doctor"
            }
          </Typography>
        </Box>

        {/* Chat iframe */}
        {chatUrl ? (
          <Box
            sx={{
              flex: 1,
              width: "100%",
              height: "calc(100vh - 280px)", // Account for banner (48px) + nav (80px) + back button (50px) + header (100px) + padding
              minHeight: "400px", // Minimum height for the iframe
              border: "1px solid #ddd",
              borderRadius: "8px",
              overflow: "hidden",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <iframe
              src={chatUrl}
              style={{
                width: "100%",
                height: "100%",
                border: "none",
                flex: 1,
              }}
              title="Doctor Chat"
              allow="camera; microphone; fullscreen"
            />
          </Box>
        ) : (
          <Box sx={{ textAlign: "center", padding: "40px" }}>
            <Typography variant="h6" sx={{ marginBottom: 2 }}>
              Unable to Load Chat
            </Typography>
            <Typography sx={{ marginBottom: 3 }}>
              There was an issue loading the chat interface. Please try again later.
            </Typography>
          </Box>
        )}
        </Box>
      </Box>
    </ThemeProvider>
  );
}

export default Chat;
