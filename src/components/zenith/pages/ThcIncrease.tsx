import { useState, useEffect } from "react";
import {
	Box,
	Button,
	Typography,
	TextField,
	Stack,
	FormControl,
	FormLabel,
	RadioGroup,
	FormControlLabel,
	Radio,
	Divider,
	ThemeProvider,
	IconButton,
	Drawer,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { ArrowBack } from "@mui/icons-material";
import InstagramIcon from "@mui/icons-material/Instagram";
import axios from "axios";
import { useSnackbar } from "notistack";
import { useNavigate } from "@tanstack/react-location";
import { useFlow } from "../../../hooks/flow-controller";
import axiosInstance from "../../../services/axios";
import zenithTheme from "../../../styles/zenith/theme";
import Banner from "../layouts/Banner";
import CachedImage from "../common/CachedImage";

// TypeScript interface for form data
interface ThcIncreaseFormData {
	// Step 1
	consistency: string;
	dosage: string;
	frequency: string;
	// Step 2
	condition: string;
	conditionOther: string;
	effectiveness: string;
	// Step 3
	symptomChanges: string;
	sideEffect: string;
	sideEffectsOther: string;
	// Step 4
	sideEffectManageability: string;
	concerns: string;
	// Step 5
	treatmentEffectiveness: string;
	weaknessAssessment: string;
	// Step 6
	insufficientRelief: string;
	satisfactionWithForm: string;
	// Step 7
	openToHigherPotency: string;
	quickReliefImportance: string;
	// Step 8
	continueTreatment: string;
	overallSatisfaction: string;
}

// Scoring state interface
interface ScoringState {
	totalScore: number;
	maxScore: number;
	isEligible: boolean;
	questionScores: Record<string, number>;
}

const ThcIncrease = () => {
	const [step, setStep] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [drawerOpen, setDrawerOpen] = useState(false);
	const [contactId, setContactId] = useState<string | null>(null);
	const { enqueueSnackbar } = useSnackbar();
	const navigate = useNavigate();
	const { user } = useFlow();

	// Form data state
	const [formData, setFormData] = useState<ThcIncreaseFormData>({
		// Step 1
		consistency: "",
		dosage: "",
		frequency: "",
		// Step 2
		condition: "",
		conditionOther: "",
		effectiveness: "",
		// Step 3
		symptomChanges: "",
		sideEffect: "",
		sideEffectsOther: "",
		// Step 4
		sideEffectManageability: "",
		concerns: "",
		// Step 5
		treatmentEffectiveness: "",
		weaknessAssessment: "",
		// Step 6
		insufficientRelief: "",
		satisfactionWithForm: "",
		// Step 7
		openToHigherPotency: "",
		quickReliefImportance: "",
		// Step 8
		continueTreatment: "",
		overallSatisfaction: "",
	});

	// Scoring state
	const [scoring, setScoring] = useState<ScoringState>({
		totalScore: 0,
		maxScore: 61,
		isEligible: false,
		questionScores: {},
	});

	// Form validation state
	const [canProceed, setCanProceed] = useState(false);

	// Navigation functions
	const toggleDrawer = () => {
		setDrawerOpen(!drawerOpen);
	};

	const handleLogout = () => {
		// Clear authentication data
		localStorage.removeItem("zenith_auth_user");
		localStorage.removeItem("zenith_authenticated");

		// Close drawer
		toggleDrawer();

		// Redirect to login page
		navigate({ to: "/patient/login" });
	};

	const navigateToShop = () => {
		if (user?.email && contactId) {
			const safeContactId = contactId.startsWith("p") ? contactId : `p${contactId}`;
			const shopUrl = `https://letsroll.harvest.delivery/members-shop?email=${encodeURIComponent(
				user.email
			)}&contact=${encodeURIComponent(safeContactId)}`;
			window.location.href = shopUrl;
		} else {
			enqueueSnackbar("Unable to access shop. Please contact support.", {
				variant: "error",
			});
		}
	};

	// Handle radio button changes (single select)
	const handleRadioChange = (questionKey: keyof ThcIncreaseFormData, value: string) => {
		setFormData(prev => {
			const newData = {
				...prev,
				[questionKey]: value
			};
			return newData;
		});
	};



	// Handle text field changes
	const handleTextChange = (questionKey: keyof ThcIncreaseFormData, value: string) => {
		setFormData(prev => ({
			...prev,
			[questionKey]: value
		}));
	};

	// Scoring mapping object - maps answer values to points
	const scoringMap: Record<string, Record<string, number>> = {
		// Step 1 Questions
		consistency: {
			'every-day': 3,
			'most-days': 2,
			'occasionally': 1,
			'rarely': 0
		},
		dosage: {
			'less-than-0-5g': 1,
			'0-5g-1g': 2,
			'1g-2g': 3,
			'more-than-2g': 4
		},
		frequency: {
			'once-a-day': 1,
			'twice-a-day': 2,
			'three-times-a-day': 3,
			'as-needed': 1
		},
		// Step 2 Questions
		effectiveness: {
			'1-2': 0,
			'3-4': 1,
			'5-6': 2,
			'7-8': 3,
			'9-10': 4
		},
		// Step 3 Questions
		symptomChanges: {
			'significant-improvement': 4,
			'some-improvement': 2,
			'no-change': 1,
			'worsening-symptoms': 0
		},
		// Step 4 Questions
		sideEffectManageability: {
			'1-2': 0,
			'3-4': 1,
			'5-6': 2,
			'7-8': 3,
			'9-10': 4
		},
		concerns: {
			'yes': 0,
			'no': 3
		},
		// Step 5 Questions
		treatmentEffectiveness: {
			'very-effective': 4,
			'effective': 3,
			'somewhat-effective': 2,
			'not-effective': 0
		},
		weaknessAssessment: {
			'yes-definitely': 4,
			'yes-somewhat': 3,
			'no-adequate': 1,
			'no-too-strong': 0
		},
		// Step 6 Questions
		insufficientRelief: {
			'yes-definitely': 4,
			'yes-somewhat': 3,
			'no-adequate': 1,
			'no-complete-relief': 0
		},
		satisfactionWithForm: {
			'very-satisfied': 4,
			'satisfied': 3,
			'neutral': 2,
			'unsatisfied': 1,
			'very-unsatisfied': 0
		},
		// Step 7 Questions
		openToHigherPotency: {
			'yes': 3,
			'no': 0,
			'maybe': 1
		},
		quickReliefImportance: {
			'very-important': 4,
			'important': 3,
			'neutral': 2,
			'not-important': 1
		},
		// Step 8 Questions
		continueTreatment: {
			'very-likely': 3,
			'likely': 2,
			'neutral': 1,
			'unlikely': 0,
			'very-unlikely': 0
		},
		overallSatisfaction: {
			'very-satisfied': 4,
			'satisfied': 3,
			'neutral': 2,
			'unsatisfied': 1,
			'very-unsatisfied': 0
		}
	};

	// Calculate score for individual question
	const calculateQuestionScore = (questionKey: keyof ThcIncreaseFormData, answer: string): number => {
		// Special scoring for condition question
		if (questionKey === 'condition') {
			return answer && answer !== 'other' ? 2 : 0;
		}

		// Special scoring for side effect question
		if (questionKey === 'sideEffect') {
			if (answer === 'none') {
				return 4;
			} else if (answer && answer !== 'other') {
				return 1;
			}
			return 0;
		}

		// Handle all other single-select questions
		const questionScoring = scoringMap[questionKey];
		return questionScoring ? (questionScoring[answer] || 0) : 0;
	};

	// Calculate total score from all answers
	const calculateTotalScore = (data: ThcIncreaseFormData): { totalScore: number; questionScores: Record<string, number> } => {
		let totalScore = 0;
		const questionScores: Record<string, number> = {};

		// Calculate score for each question
		Object.entries(data).forEach(([key, value]) => {
			if (key !== 'conditionOther' && key !== 'sideEffectsOther') {
				const questionKey = key as keyof ThcIncreaseFormData;
				const score = calculateQuestionScore(questionKey, value);
				questionScores[key] = score;
				totalScore += score;
			}
		});

		return { totalScore, questionScores };
	};

	// Check eligibility based on score
	const checkEligibility = (score: number): boolean => {
		return score >= 45; // 20% of 61 points (was 45 = 73.8%)
	};

	// Update scoring when form data changes
	useEffect(() => {
		const { totalScore, questionScores } = calculateTotalScore(formData);
		const isEligible = checkEligibility(totalScore);

		setScoring(prev => ({
			...prev,
			totalScore,
			isEligible,
			questionScores
		}));
	}, [formData]);

	// Validate current step
	const validateCurrentStep = (): boolean => {
		switch (step) {
			case 1:
				return !!(formData.consistency && formData.dosage && formData.frequency);
			case 2:
				return !!(formData.condition && formData.effectiveness);
			case 3:
				return !!(formData.symptomChanges && formData.sideEffect);
			case 4:
				return !!(formData.sideEffectManageability && formData.concerns);
			case 5:
				return !!(formData.treatmentEffectiveness && formData.weaknessAssessment);
			case 6:
				return !!(formData.insufficientRelief && formData.satisfactionWithForm);
			case 7:
				return !!(formData.openToHigherPotency && formData.quickReliefImportance);
			case 8:
				return !!(formData.continueTreatment && formData.overallSatisfaction);
			default:
				return false;
		}
	};

	// Update canProceed when step or formData changes
	useEffect(() => {
		setCanProceed(validateCurrentStep());
	}, [step, formData]);

	// Fetch contact ID on component mount
	useEffect(() => {
		const fetchContactId = async () => {
			try {
				const token = localStorage.getItem("zenith_auth_token");
				if (token) {
					setContactId(token);
				}
			} catch (error) {
				console.error("Error fetching contact ID:", error);
			}
		};

		fetchContactId();
	}, []);

	// Question and answer text mapping for display purposes
	const getQuestionText = (questionKey: string): string => {
		const questionMap: Record<string, string> = {
			consistency: "How often did you use 22% THC flower?",
			dosage: "What was your typical dosage per session?",
			frequency: "How many times per day did you typically use it?",
			condition: "What condition are you using THC flower to treat?",
			effectiveness: "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?",
			symptomChanges: "Have you noticed any changes in your symptoms since starting 22% THC flower?",
			sideEffect: "Have you experienced any side effects from 22% THC flower?",
			sideEffectManageability: "On a scale of 1 to 10, how manageable are any side effects you've experienced?",
			concerns: "Do you have any concerns about your current 22% THC flower treatment?",
			treatmentEffectiveness: "How would you rate the overall effectiveness of your current treatment?",
			weaknessAssessment: "Do you feel your current 22% THC flower treatment has any weaknesses or limitations?",
			insufficientRelief: "Do you feel you need stronger medication for better symptom relief?",
			satisfactionWithForm: "How satisfied are you with the flower form of your current medication?",
			openToHigherPotency: "Would you be open to trying a higher potency (29%) THC treatment?",
			quickReliefImportance: "How important is quick symptom relief to you?",
			continueTreatment: "How likely are you to continue with cannabis treatment long-term?",
			overallSatisfaction: "What is your overall satisfaction with your current 22% THC flower treatment?"
		};
		return questionMap[questionKey] || questionKey;
	};

	const getAnswerText = (questionKey: string, answerValue: string): string => {
		const answerMap: Record<string, Record<string, string>> = {
			consistency: {
				"every-day": "Every day",
				"most-days": "Most days (5-6 days per week)",
				"few-times-week": "A few times a week (2-4 days)",
				"once-week": "Once a week or less"
			},
			dosage: {
				"less-than-0.5g": "Less than 0.5g",
				"0.5g-1g": "0.5g - 1g",
				"1g-2g": "1g - 2g",
				"more-than-2g": "More than 2g"
			},
			frequency: {
				"once-a-day": "Once a day",
				"twice-a-day": "Twice a day",
				"three-times-a-day": "Three times a day",
				"more-than-three": "More than three times a day"
			},
			condition: {
				"chronic-pain": "Chronic pain",
				"anxiety": "Anxiety",
				"insomnia": "Insomnia",
				"depression": "Depression",
				"ptsd": "PTSD",
				"epilepsy": "Epilepsy",
				"cancer": "Cancer-related symptoms",
				"other": formData.conditionOther || "Other"
			},
			effectiveness: {
				"1-2": "1-2 (Not effective)",
				"3-4": "3-4 (Slightly effective)",
				"5-6": "5-6 (Moderately effective)",
				"7-8": "7-8 (Very effective)",
				"9-10": "9-10 (Extremely effective)"
			},
			symptomChanges: {
				"significant-improvement": "Significant improvement",
				"moderate-improvement": "Moderate improvement",
				"slight-improvement": "Slight improvement",
				"no-change": "No change",
				"worsened": "Symptoms have worsened"
			},
			sideEffect: {
				"none": "None",
				"mild": "Mild side effects",
				"moderate": "Moderate side effects",
				"severe": "Severe side effects"
			},
			sideEffectManageability: {
				"1-2": "1-2 (Very difficult to manage)",
				"3-4": "3-4 (Difficult to manage)",
				"5-6": "5-6 (Somewhat manageable)",
				"7-8": "7-8 (Manageable)",
				"9-10": "9-10 (Very manageable)"
			},
			concerns: {
				"no": "No concerns",
				"mild": "Mild concerns",
				"moderate": "Moderate concerns",
				"significant": "Significant concerns"
			},
			treatmentEffectiveness: {
				"very-effective": "Very effective",
				"effective": "Effective",
				"somewhat-effective": "Somewhat effective",
				"not-effective": "Not effective"
			},
			weaknessAssessment: {
				"yes-definitely": "Yes, definitely",
				"yes-somewhat": "Yes, somewhat",
				"no-satisfied": "No, I'm satisfied",
				"unsure": "I'm unsure"
			},
			insufficientRelief: {
				"yes-definitely": "Yes, definitely",
				"yes-sometimes": "Yes, sometimes",
				"no-sufficient": "No, current strength is sufficient",
				"unsure": "I'm unsure"
			},
			satisfactionWithForm: {
				"very-satisfied": "Very satisfied",
				"satisfied": "Satisfied",
				"neutral": "Neutral",
				"dissatisfied": "Dissatisfied",
				"very-dissatisfied": "Very dissatisfied"
			},
			openToHigherPotency: {
				"yes": "Yes",
				"maybe": "Maybe",
				"no": "No",
				"need-more-info": "I need more information"
			},
			quickReliefImportance: {
				"very-important": "Very important",
				"important": "Important",
				"somewhat-important": "Somewhat important",
				"not-important": "Not important"
			},
			continueTreatment: {
				"very-likely": "Very likely",
				"likely": "Likely",
				"unsure": "Unsure",
				"unlikely": "Unlikely",
				"very-unlikely": "Very unlikely"
			},
			overallSatisfaction: {
				"very-satisfied": "Very satisfied",
				"satisfied": "Satisfied",
				"neutral": "Neutral",
				"dissatisfied": "Dissatisfied",
				"very-dissatisfied": "Very dissatisfied"
			}
		};

		return answerMap[questionKey]?.[answerValue] || answerValue;
	};

	// Submit questionnaire to API
	const submitQuestionnaire = async () => {
		setIsLoading(true);

		try {
			// Create questions and answers array for easy display
			const questionsAndAnswers = Object.entries(formData)
				.filter(([key, value]) => value && key !== 'conditionOther' && key !== 'sideEffectsOther')
				.map(([questionKey, answerValue]) => ({
					questionKey,
					questionText: getQuestionText(questionKey),
					answerValue,
					answerText: getAnswerText(questionKey, answerValue),
					score: calculateQuestionScore(questionKey as keyof ThcIncreaseFormData, answerValue)
				}));

			// Prepare submission data
			const submissionData = {
				questionsAndAnswers,
				totalScore: scoring.totalScore,
				maxScore: scoring.maxScore,
				isEligible: scoring.isEligible,
				submittedAt: new Date().toISOString(),
			};

			const result = await axios.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/thc-increase-questionnaire`,
				submissionData,
				{ withCredentials: true }
			);

			if (result.data) {
				// Show results based on eligibility
				if (scoring.isEligible) {
					enqueueSnackbar("Congratulations! Your 29% THC treatment request is under review.", {
						variant: "success",
					});
				} else {
					enqueueSnackbar("Congratulations! Your 29% THC treatment request is under review.", {
						variant: "success",
					});
				}

				// Navigate back to home page to show approval/rejection status
				navigate({ to: "/patient/home" });
			}
		} catch (error) {
			console.error('Error submitting THC increase questionnaire:', error);
			enqueueSnackbar("Failed to submit questionnaire. Please try again.", {
				variant: "error",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const stepOne = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="consistency-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					How consistent were you in using 22% THC flower during the two-week trial?
				</FormLabel>
				<RadioGroup
					aria-labelledby="consistency-radio-buttons-group"
					name="consistency-radio-buttons-group"
					value={formData.consistency}
					onChange={(e) => handleRadioChange('consistency', e.target.value)}
				>
					<FormControlLabel value="every-day" control={<Radio />} label="Every day as prescribed" />
					<FormControlLabel value="most-days" control={<Radio />} label="Most days" />
					<FormControlLabel value="occasionally" control={<Radio />} label="Occasionally" />
					<FormControlLabel value="rarely" control={<Radio />} label="Rarely" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="dosage-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					What dosage of 22% THC flower were you taking during the trial?
				</FormLabel>
				<RadioGroup
					aria-labelledby="dosage-radio-buttons-group"
					name="dosage-radio-buttons-group"
					value={formData.dosage}
					onChange={(e) => handleRadioChange('dosage', e.target.value)}
				>
					<FormControlLabel value="less-than-0-5g" control={<Radio />} label="Less than 0.5g/day" />
					<FormControlLabel value="0-5g-1g" control={<Radio />} label="0.5g - 1g/day" />
					<FormControlLabel value="1g-2g" control={<Radio />} label="1g - 2g/day" />
					<FormControlLabel value="more-than-2g" control={<Radio />} label="More than 2g/day" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="often-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					How often did you use 22% THC flower?
				</FormLabel>
				<RadioGroup
					aria-labelledby="often-radio-buttons-group"
					name="often-radio-buttons-group"
					value={formData.frequency}
					onChange={(e) => handleRadioChange('frequency', e.target.value)}
				>
					<FormControlLabel value="once-a-day" control={<Radio />} label="Once a day" />
					<FormControlLabel value="twice-a-day" control={<Radio />} label="Twice a day" />
					<FormControlLabel value="three-times-a-day" control={<Radio />} label="Three times a day" />
					<FormControlLabel value="as-needed" control={<Radio />} label="As needed" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepTwo = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="condition-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					What condition are you using THC flower to treat?
				</FormLabel>
				<RadioGroup
					aria-labelledby="condition-radio-buttons-group"
					name="condition-radio-buttons-group"
					value={formData.condition}
					onChange={(e) => handleRadioChange('condition', e.target.value)}
				>
					<FormControlLabel value="chronic-pain" control={<Radio />} label="Chronic pain" />
					<FormControlLabel value="anxiety" control={<Radio />} label="Anxiety" />
					<FormControlLabel value="insomnia" control={<Radio />} label="Insomnia" />
					<FormControlLabel value="inflammation" control={<Radio />} label="Inflammation" />
					<FormControlLabel value="other" control={<Radio />} label="Other (Please specify):" />
					<TextField
						variant="outlined"
						size="small"
						placeholder="Type your condition here"
						sx={{ mt: 1 }}
						value={formData.conditionOther}
						onChange={(e) => handleTextChange('conditionOther', e.target.value)}
						disabled={formData.condition !== 'other'}
					/>
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="effective-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?
				</FormLabel>
				<RadioGroup
					aria-labelledby="effective-radio-buttons-group"
					name="effective-radio-buttons-group"
					value={formData.effectiveness}
					onChange={(e) => handleRadioChange('effectiveness', e.target.value)}
				>
					<FormControlLabel value="1-2" control={<Radio />} label="1-2" />
					<FormControlLabel value="3-4" control={<Radio />} label="3-4" />
					<FormControlLabel value="5-6" control={<Radio />} label="5-6" />
					<FormControlLabel value="7-8" control={<Radio />} label="7-8" />
					<FormControlLabel value="9-10" control={<Radio />} label="9-10" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepThree = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="symptoms-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					Have you noticed any changes in your symptoms since starting 22% THC flower?
				</FormLabel>
				<RadioGroup
					aria-labelledby="symptoms-radio-buttons-group"
					name="symptoms-radio-buttons-group"
					value={formData.symptomChanges}
					onChange={(e) => handleRadioChange('symptomChanges', e.target.value)}
				>
					<FormControlLabel value="significant-improvement" control={<Radio />} label="Significant improvement" />
					<FormControlLabel value="some-improvement" control={<Radio />} label="Some improvement" />
					<FormControlLabel value="no-change" control={<Radio />} label="No change" />
					<FormControlLabel value="worsening-symptoms" control={<Radio />} label="Worsening of symptoms" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="sideeffects-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					Have you experienced any side effects from using 22% THC flower during the trial?
				</FormLabel>
				<RadioGroup
					aria-labelledby="sideeffects-radio-buttons-group"
					name="sideeffects-radio-buttons-group"
					value={formData.sideEffect}
					onChange={(e) => handleRadioChange('sideEffect', e.target.value)}
				>
					<FormControlLabel value="drowsiness" control={<Radio />} label="Drowsiness" />
					<FormControlLabel value="dry-mouth" control={<Radio />} label="Dry mouth" />
					<FormControlLabel value="dizziness" control={<Radio />} label="Dizziness" />
					<FormControlLabel value="increased-heart-rate" control={<Radio />} label="Increased heart rate" />
					<FormControlLabel value="anxiety" control={<Radio />} label="Anxiety" />
					<FormControlLabel value="none" control={<Radio />} label="None" />
					<FormControlLabel value="other" control={<Radio />} label="Other (Please specify):" />
					<TextField
						variant="outlined"
						size="small"
						placeholder="Other (Please specify)"
						sx={{ mt: 1 }}
						value={formData.sideEffectsOther}
						onChange={(e) => handleTextChange('sideEffectsOther', e.target.value)}
						disabled={formData.sideEffect !== 'other'}
					/>
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepFour = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="manageable-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					On a scale of 1 to 10, how manageable were these side effects?
				</FormLabel>
				<RadioGroup
					aria-labelledby="manageable-radio-buttons-group"
					name="manageable-radio-buttons-group"
					value={formData.sideEffectManageability}
					onChange={(e) => handleRadioChange('sideEffectManageability', e.target.value)}
				>
					<FormControlLabel value="1-2" control={<Radio />} label="1-2" />
					<FormControlLabel value="3-4" control={<Radio />} label="3-4" />
					<FormControlLabel value="5-6" control={<Radio />} label="5-6" />
					<FormControlLabel value="7-8" control={<Radio />} label="7-8" />
					<FormControlLabel value="9-10" control={<Radio />} label="9-10" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="issues-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					Did you have any concerns or issues with your THC flower treatment during the trial?
				</FormLabel>
				<RadioGroup
					aria-labelledby="issues-radio-buttons-group"
					name="issues-radio-buttons-group"
					value={formData.concerns}
					onChange={(e) => handleRadioChange('concerns', e.target.value)}
				>
					<FormControlLabel value="yes" control={<Radio />} label="Yes" />
					<FormControlLabel value="no" control={<Radio />} label="No" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepFive = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="effectiveness-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					Was the 22% THC flower effective in treating your condition?
				</FormLabel>
				<RadioGroup
					aria-labelledby="effectiveness-radio-buttons-group"
					name="effectiveness-radio-buttons-group"
					value={formData.treatmentEffectiveness}
					onChange={(e) => handleRadioChange('treatmentEffectiveness', e.target.value)}
				>
					<FormControlLabel value="very-effective" control={<Radio />} label="Very effective" />
					<FormControlLabel value="effective" control={<Radio />} label="Effective" />
					<FormControlLabel value="somewhat-effective" control={<Radio />} label="Somewhat effective" />
					<FormControlLabel value="not-effective" control={<Radio />} label="Not effective" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="weakness-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					Do you feel the 22% THC flower was too weak in managing your symptoms?
				</FormLabel>
				<RadioGroup
					aria-labelledby="weakness-radio-buttons-group"
					name="weakness-radio-buttons-group"
					value={formData.weaknessAssessment}
					onChange={(e) => handleRadioChange('weaknessAssessment', e.target.value)}
				>
					<FormControlLabel value="yes-definitely" control={<Radio />} label="Yes, definitely" />
					<FormControlLabel value="yes-somewhat" control={<Radio />} label="Yes, somewhat" />
					<FormControlLabel value="no-adequate" control={<Radio />} label="No, it was adequate" />
					<FormControlLabel value="no-too-strong" control={<Radio />} label="No, it was too strong" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepSix = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="relief-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					During these breakout pain or acute episodes, did you find the 22% THC flower insufficient in
					providing relief?
				</FormLabel>
				<RadioGroup
					aria-labelledby="relief-radio-buttons-group"
					name="relief-radio-buttons-group"
					value={formData.insufficientRelief}
					onChange={(e) => handleRadioChange('insufficientRelief', e.target.value)}
				>
					<FormControlLabel value="yes-definitely" control={<Radio />} label="Yes, definitely" />
					<FormControlLabel value="yes-somewhat" control={<Radio />} label="Yes, somewhat" />
					<FormControlLabel value="no-adequate" control={<Radio />} label="No, it was adequate" />
					<FormControlLabel value="no-complete-relief" control={<Radio />} label="No, it provided complete relief" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="satisfaction-form-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					How satisfied are you with the form of THC flower you used during the trial (e.g., vaporized,
					smoked)?
				</FormLabel>
				<RadioGroup
					aria-labelledby="satisfaction-form-radio-buttons-group"
					name="satisfaction-form-radio-buttons-group"
					value={formData.satisfactionWithForm}
					onChange={(e) => handleRadioChange('satisfactionWithForm', e.target.value)}
				>
					<FormControlLabel value="very-satisfied" control={<Radio />} label="Very satisfied" />
					<FormControlLabel value="satisfied" control={<Radio />} label="Satisfied" />
					<FormControlLabel value="neutral" control={<Radio />} label="Neutral" />
					<FormControlLabel value="unsatisfied" control={<Radio />} label="Unsatisfied" />
					<FormControlLabel value="very-unsatisfied" control={<Radio />} label="Very unsatisfied" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepSeven = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="higher-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					Would you be open to trying a higher potency of THC flower (29%)?
				</FormLabel>
				<RadioGroup
					aria-labelledby="higher-radio-buttons-group"
					name="higher-radio-buttons-group"
					value={formData.openToHigherPotency}
					onChange={(e) => handleRadioChange('openToHigherPotency', e.target.value)}
				>
					<FormControlLabel value="yes" control={<Radio />} label="Yes" />
					<FormControlLabel value="no" control={<Radio />} label="No" />
					<FormControlLabel value="maybe" control={<Radio />} label="Maybe" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="quick-relief-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					How important is it for you to have quick relief from your symptoms?
				</FormLabel>
				<RadioGroup
					aria-labelledby="quick-relief-radio-buttons-group"
					name="quick-relief-radio-buttons-group"
					value={formData.quickReliefImportance}
					onChange={(e) => handleRadioChange('quickReliefImportance', e.target.value)}
				>
					<FormControlLabel value="very-important" control={<Radio />} label="Very important" />
					<FormControlLabel value="important" control={<Radio />} label="Important" />
					<FormControlLabel value="neutral" control={<Radio />} label="Neutral" />
					<FormControlLabel value="not-important" control={<Radio />} label="Not important" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const stepEight = () => (
		<Stack sx={{ flexGrow: 1 }}>
			<FormControl>
				<FormLabel id="continue-treatment-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					How likely are you to continue using cannabinoid treatments as part of your treatment plan?
				</FormLabel>
				<RadioGroup
					aria-labelledby="continue-treatment-radio-buttons-group"
					name="continue-treatment-radio-buttons-group"
					value={formData.continueTreatment}
					onChange={(e) => handleRadioChange('continueTreatment', e.target.value)}
				>
					<FormControlLabel value="very-likely" control={<Radio />} label="Very likely" />
					<FormControlLabel value="likely" control={<Radio />} label="Likely" />
					<FormControlLabel value="neutral" control={<Radio />} label="Neutral" />
					<FormControlLabel value="unlikely" control={<Radio />} label="Unlikely" />
					<FormControlLabel value="very-unlikely" control={<Radio />} label="Very unlikely" />
				</RadioGroup>
			</FormControl>
			<Divider sx={{ my: 2, borderColor: "black" }} />
			<FormControl>
				<FormLabel id="overall-satisfaction-radio-buttons-group" sx={{ color: "black", fontWeight: "bold" }}>
					How satisfied are you with the overall experience of using 22% THC flower during the trial?
				</FormLabel>
				<RadioGroup
					aria-labelledby="overall-satisfaction-radio-buttons-group"
					name="overall-satisfaction-radio-buttons-group"
					value={formData.overallSatisfaction}
					onChange={(e) => handleRadioChange('overallSatisfaction', e.target.value)}
				>
					<FormControlLabel value="very-satisfied" control={<Radio />} label="Very satisfied" />
					<FormControlLabel value="satisfied" control={<Radio />} label="Satisfied" />
					<FormControlLabel value="neutral" control={<Radio />} label="Neutral" />
					<FormControlLabel value="unsatisfied" control={<Radio />} label="Unsatisfied" />
					<FormControlLabel value="very-unsatisfied" control={<Radio />} label="Very unsatisfied" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	const renderStep = () => {
		switch (step) {
			case 1:
				return stepOne();
			case 2:
				return stepTwo();
			case 3:
				return stepThree();
			case 4:
				return stepFour();
			case 5:
				return stepFive();
			case 6:
				return stepSix();
			case 7:
				return stepSeven();
			case 8:
				return stepEight();
			default:
				return <Typography>Step {step} content goes here.</Typography>;
		}
	};

	const handleStepChange = async () => {
		if (!canProceed) {
			// Show validation error
			enqueueSnackbar("Please answer all questions to continue", {
				variant: "warning",
			});
			return;
		}

		if (step >= 1 && step < 8) {
			setStep(step + 1);
		} else if (step === 8) {
			// Submit questionnaire
			await submitQuestionnaire();
		}
	};

	return (
		<ThemeProvider theme={zenithTheme}>
			<Box
				sx={{
					minHeight: "100vh",
					backgroundColor: "#f5f5f5",
					display: "flex",
					flexDirection: "column",
				}}
			>
				{/* Custom Header */}
				<Box sx={{ width: "100%" }}>
					<Banner />

					{/* Nav Bar */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
							padding: "0 20px",
							backgroundColor: "white",
							// paddingTop: { xs: "50px", sm: "58px" }, // Account for banner height + some spacing
							paddingBottom: "10px",
						}}
					>
						<IconButton aria-label="menu" sx={{ padding: "8px" }} onClick={toggleDrawer}>
							<MenuIcon />
						</IconButton>

						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{
								height: "19.14px",
							}}
						/>
					</Box>
				</Box>

				{/* Navigation Drawer */}
				<Drawer
					anchor="top"
					open={drawerOpen}
					onClose={toggleDrawer}
					keepMounted={true}
					sx={{
						"& .MuiDrawer-paper": {
							width: "100%",
							maxWidth: "100%",
							boxSizing: "border-box",
							padding: "20px 0 0 0",
							height: "auto",
							maxHeight: "500px",
							overflowY: "auto",
							borderBottom: "1px solid #e0e0e0",
							top: { xs: "40px", sm: "48px" }, // Push down by banner height
							zIndex: 1299, // Just below the banner
						},
					}}
				>
					{/* Drawer Header */}
					<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{ height: '36px' }}
						/>
						<IconButton onClick={toggleDrawer}>
							<CloseIcon />
						</IconButton>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Navigation Links */}
					<Box sx={{ padding: '0 20px 20px' }}>
						<Button
							onClick={() => {
								toggleDrawer();
								navigate({ to: "/patient/home" });
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Back to Home
						</Button>
						<Button
							onClick={() => {
								toggleDrawer();
								navigateToShop();
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Visit Private Shop
						</Button>
						<Button
							onClick={handleLogout}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none'
							}}
						>
							Logout
						</Button>
					</Box>
				</Drawer>

				{/* Main Content */}
				<Box sx={{ flex: 1, padding: "20px" }}>
					{/* Back Button */}
					<Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
						<Button
							variant="outlined"
							startIcon={<ArrowBack />}
							onClick={() => navigate({ to: "/patient/home" })}
							sx={{
								color: "#217F00",
								borderColor: "#217F00",
								fontSize: "16px",
								fontWeight: "bold",
								textTransform: "none",
								padding: "8px 16px",
								"&:hover": {
									backgroundColor: "rgba(33, 127, 0, 0.04)",
									borderColor: "#217F00",
								},
							}}
						>
							Back to Home
						</Button>
					</Box>

					<Grid
						container
						direction="column"
						alignItems="center"
						justifyContent="flex-start"
						spacing={2}
					>
			<Grid size={12}>
				<Stack direction="row" alignItems="center" justifyContent="center">
					<Box
						sx={{
							width: "32px",
							borderRadius: "15px 0 0 15px",
							height: "11px",
							backgroundColor: "green",
						}}
					/>
					{[...Array(6)].map((_, index) => (
						<Box
							key={index}
							sx={{
								width: "32px",
								height: "11px",
								backgroundColor: step > index + 1 ? "green" : "#EAEAEA",
								border: "0.5px solid rgba(89, 89, 89, 0.61)",
							}}
						/>
					))}
					<Box
						sx={{
							width: "32px",
							borderRadius: "0 15px 15px 0",
							height: "11px",
							backgroundColor: step === 8 ? "green" : "#EAEAEA",
							border: "0.5px solid rgba(89, 89, 89, 0.61)",
						}}
					/>
				</Stack>
			</Grid>
			<Grid size={12}>
				<Stack>
					<Typography variant="h4" fontWeight={800}>
						Questionnaire:
					</Typography>
					<Typography variant="h5" fontWeight={700} color="green">
						Increase to a 29% THC Plan
					</Typography>
					
				</Stack>
			</Grid>
			<Grid size={12}>{renderStep()}</Grid>
			<Grid size={9}>
				<Button
					fullWidth
					sx={{
						textTransform: "capitalize",
						borderRadius: "20px",
						opacity: (canProceed && !isLoading) ? 1 : 0.6
					}}
					variant="contained"
					color="success"
					disableElevation
					size="large"
					onClick={handleStepChange}
					disabled={!canProceed || isLoading}
				>
					{isLoading ? "Submitting..." : (step < 8 ? "Continue" : "Submit")}
				</Button>
				{!canProceed && !isLoading && (
					<Typography
						variant="body2"
						color="error"
						sx={{ mt: 1, textAlign: 'center' }}
					>
						Please answer all questions to continue
					</Typography>
				)}
				{isLoading && (
					<Typography
						variant="body2"
						color="primary"
						sx={{ mt: 1, textAlign: 'center' }}
					>
						Submitting your questionnaire...
					</Typography>
				)}
			</Grid>
				</Grid>
				</Box>
			</Box>
		</ThemeProvider>
	);
};

export default ThcIncrease;