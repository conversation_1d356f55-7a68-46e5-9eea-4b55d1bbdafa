import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typography,
  ThemeProvider,
  useMediaQuery,
  Box,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { AuthUser } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import { useFlow } from "../../../hooks/flow-controller";
import DontOfferDialog from "../dialogs/DontOfferDialog";

const inputStyle = {
  backgroundColor: "white",
  borderRadius: "13px",
  "& .MuiOutlinedInput-root": {
    borderRadius: "13px",
    "&:hover": {
      borderColor: "black",
    },
    "&.Mui-focused fieldset": {
      borderColor: "black",
      borderRadius: "13px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "white",
  },
};

function Login() {
  const [canSubmit, setCanSubmit] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { user, setUser, flowController } = useFlow();
  const navigate = useNavigate();
  const urlParams = new URLSearchParams(location.search); // Utilisez ceci si vous utilisez React Router
  const resetpassword = urlParams.get("resetpassword");
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));
  const { enqueueSnackbar } = useSnackbar();
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });

  // Get the return_to parameter from URL
  const getReturnPath = () => {
    const params = new URLSearchParams(window.location.search);
    return params.get("return_to");
  };

  // Simplified initial effect to prevent loops
  useEffect(() => {
    setIsLoading(false);
  }, []);
  useEffect(() => {
    if (resetpassword == "true") {
      enqueueSnackbar(
        "Your password has been changed successfully, you can login with your new access",
        {
          variant: "success",
        }
      );
    }
  }, [resetpassword]);
  useEffect(() => {
    setCanSubmit(credentials.email != "" && credentials.password != "");
  }, [credentials]);
  async function handleSubmit() {
    if (canSubmit) {
      setIsLoading(true);
      try {
        // First, let's try to clear any existing cookies that might be interfering
        document.cookie.split(";").forEach((cookie) => {
          const [name] = cookie.trim().split("=");
          if (name && !["__stripe_mid", "__stripe_sid"].includes(name)) {
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/;`;
          }
        });

        // Now perform the login
        const result = await axiosInstance.post(
          `${
            import.meta.env.VITE_API_URL
          }/funnel/v1.0/patient/login/email-password`,
          credentials,
          {
            withCredentials: true,
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          }
        );

        if (result.data && result.data.authenticated) {
          // Store authentication in localStorage as a backup
          localStorage.setItem(
            "zenith_auth_user",
            JSON.stringify(result.data.user)
          );
          localStorage.setItem("zenith_authenticated", "true");

          enqueueSnackbar("You have successfully logged in.", {
            variant: "success",
          });
          setUser(result.data?.user as AuthUser);
          // Get return path from URL parameters
          const returnPath = getReturnPath();

          // Special handling for consent form
          if (returnPath === "/patient/consent") {
            try {
              // Get user email from context or result
              const email = (result.data?.user?.email || user?.email) as
                | string
                | undefined;
              if (!email) {
                enqueueSnackbar(
                  "Could not determine your email for consent form redirection.",
                  { variant: "error" }
                );
                return;
              }
              // Fetch contactId from Zoho by email
              const zohoUrl = `/zoho/v1.0/contacts/by-email?email=${encodeURIComponent(
                email
              )}`;
              const contactResp = await axiosInstance.get(zohoUrl);
              if (
                contactResp.data &&
                contactResp.data.success &&
                contactResp.data.id
              ) {
                const contactId = contactResp.data.id;
                // Clear redirect key
                localStorage.removeItem("redirect_after_login");
                // Redirect to consent form with contactId
                const consentUrl = `/patient/consent/${contactId}`;
                window.location.href = consentUrl;
                return;
              } else {
                enqueueSnackbar(
                  "Could not find your consent form link. Please contact support.",
                  { variant: "error" }
                );
                return;
              }
            } catch (err) {
              enqueueSnackbar(
                "Could not look up your consent form. Please contact support.",
                { variant: "error" }
              );
              return;
            }
          }
          // Special handling for chat routes
          else if (returnPath && returnPath.startsWith('/patient/chat')) {
            try {
              // Get user email from context or result
              const email = (result.data?.user?.email || user?.email) as
                | string
                | undefined;
              if (!email) {
                enqueueSnackbar(
                  "Could not determine your email for chat redirection.",
                  { variant: "error" }
                );
                return;
              }
              // Fetch contactId from Zoho by email (same as Home.tsx logic)
              const treatmentResponse = await axiosInstance.get(
                `/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(email)}`
              );
              if (treatmentResponse.data?.success && treatmentResponse.data?.contactId) {
                const contactId = treatmentResponse.data.contactId;
                // Redirect to chat with the correct contactId token
                const chatUrl = `/patient/chat?token=${encodeURIComponent(contactId)}`;
                window.location.href = chatUrl;
                return;
              } else {
                enqueueSnackbar(
                  "Could not find your chat information. Please contact support.",
                  { variant: "error" }
                );
                return;
              }
            } catch (err) {
              enqueueSnackbar(
                "Could not look up your chat information. Please contact support.",
                { variant: "error" }
              );
              return;
            }
          }
          // Special handling for schedule routes
          else if (
            returnPath === "/patient/schedule" ||
            returnPath === "/schedule"
          ) {
            window.location.href = returnPath;
            return;
          }
          // Handle other return paths
          else if (returnPath) {
            window.location.href = returnPath;
            return;
          }

          // If no return path, use flow controller
          flowController(result.data?.user as AuthUser);
        } else {
          enqueueSnackbar("Authentication failed. Try again", {
            variant: "error",
          });
        }
      } catch (e: any) {
        console.log("Login - Error:", e);
        enqueueSnackbar("Authentication failed. Try again", {
          variant: "error",
        });
      } finally {
        setIsLoading(false);
      }
    } else {
      enqueueSnackbar("Please, fill the form", {
        variant: "error",
      });
    }
  }
  return (
    <>
      {isLoading && <LoadingScreen />}
      <ThemeProvider theme={zenithTheme}>
        <Stack
          gap={1}
          marginBottom={"300px"}
          sx={{ marginBottom: "250px", width: "100%" }}
        >
          <Grid
            container
            direction={"column"}
            padding={"20px 0"}
            sx={{ borderRadius: "10px" }}
            boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
          >
            <Grid>
              <Typography
                sx={{
                  fontSize: "38px",
                  fontWeight: "bold",
                  lineHeight: "1em",
                  color: "green",
                }}
              >
                Login
              </Typography>
            </Grid>
          </Grid>
          <Grid textAlign="center" marginTop={"50px"} marginBottom={"30px"}>
            <Typography
              sx={{
                fontSize: "20px",
                lineHeight: "1em",

                color: "black",
              }}
            >
              Welcome, please enter your account information to log in again.
            </Typography>
          </Grid>
          <Grid
            width={"100%"}
            borderRadius={"10px"}
            padding={!isDesktopOrMobile ? "20px 20px" : "50px"}
            boxShadow={"0px 2px 2px 0px rgba(0,0,0,0.2)"}
          >
            <Grid sx={{ width: "100%" }} width={"70%"} textAlign={"center"}>
              <Grid container direction={"column"} alignItems={"start"}>
                <Grid>
                  <Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
                    Email or phone number
                  </Typography>
                </Grid>
                <TextField
                  sx={{ m: 0, ...inputStyle }}
                  type="email"
                  name="email"
                  placeholder="Enter your email or phone number"
                  size="small"
                  onChange={(e) =>
                    setCredentials({ ...credentials, email: e.target.value })
                  }
                  margin="normal"
                  fullWidth
                  value={credentials.email}
                  required={true}
                  onKeyPress={(event) => {
                    if (event.key === " ") {
                      event.preventDefault();
                    }
                  }}
                />
              </Grid>
              <Grid
                container
                direction={"column"}
                alignItems={"start"}
                paddingTop={"10px"}
              >
                <Grid>
                  <Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
                    Password
                  </Typography>
                </Grid>
                <TextField
                  sx={{ m: 0, ...inputStyle }}
                  type="password"
                  name="password"
                  placeholder="Enter your password"
                  size="small"
                  onChange={(e) =>
                    setCredentials({ ...credentials, password: e.target.value })
                  }
                  margin="normal"
                  fullWidth
                  value={credentials.password}
                  required={true}
                />
              </Grid>
              <Grid container sx={{ mt: 2 }}>
                <Button
                  sx={{
                    textTransform: "none",
                    color: "#0000EE",
                    fontSize: "12px",
                  }}
                  onClick={() => navigate({ to: "/patient/forgot-password" })}
                >
                  Forgot password ?
                </Button>
              </Grid>
              <Grid container>
                <Button
                  sx={{
                    textTransform: "none",
                    color: "#0000EE",
                    fontSize: "12px",
                  }}
                  onClick={() => navigate({ to: "/patient/register" })}
                >
                  Are you new here? Register
                </Button>
              </Grid>
              <Grid>
                <Button
                  type="submit"
                  fullWidth
                  disabled={!canSubmit}
                  variant="contained"
                  sx={{ mt: 1 }}
                  onClick={handleSubmit}
                >
                  Login
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Stack>
      </ThemeProvider>
    </>
  );
}

export default Login;