import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typography,
  ThemeProvider,
  useMediaQuery,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import axios from "axios";
import { AuthUser } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import UserSteps from "../../../types/enum";
import { FlowController, useFlow } from "../../../hooks/flow-controller";

const resendButtonStyle = {
  backgroundColor: "white",
  color: zenithTheme.palette.primary.main,
  borderColor: zenithTheme.palette.primary.main,
};

interface MyComponentParams {
  email: string;
}

const spanClickStyle = {
  cursor: "pointer",
};
const inputStyle = {
  backgroundColor: "white",
  borderRadius: "13px",
  "& .MuiOutlinedInput-root": {
    borderRadius: "13px",
    "&:hover": {
      borderColor: "black",
    },
    "&.Mui-focused fieldset": {
      borderColor: "black",
      borderRadius: "13px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "white",
  },
};

function ResetPassword() {
  const [isLoading, setIsLoading] = useState(false);
  const [otp, setOtp] = useState("");
  const [password, setPassword] = useState("");
  const [canSubmit, setCanSubmit] = useState(false);
  const [passwordConfirm, setPasswordConfirmation] = useState("");
  const [phoneNumberResend, setPhoneNumberResend] = useState("");
  const [email, setEmail] = useState("");
  const urlParams = new URLSearchParams(location.search); // Utilisez ceci si vous utilisez React Router
  const phoneValue = urlParams.get("phone");
  const emailValue = urlParams.get("email");
  const navigate = useNavigate();
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));

  const { enqueueSnackbar } = useSnackbar();
  const [formErrorsVerify, setformErrorsVerify] = useState({
    otp: false,
    password: false,
    passwordConfirm: false,
  });
  useEffect(() => {
    if (phoneValue) {
      setPhoneNumberResend(phoneValue.substring(2));
    } else {
      navigate({ to: "/patient/forgot-password" });
    }
  }, [phoneValue]);
  useEffect(() => {
    function init() {
      setformErrorsVerify({
        otp: email == "",
        password: password == "",
        passwordConfirm: passwordConfirm == "",
      });
    }
    init();
  }, [otp,password,passwordConfirm]);
  useEffect(() => {
    if (emailValue) {
      setEmail(emailValue);
    } else {
      navigate({ to: "/patient/forgot-password" });
    }
  }, [emailValue]);
  useEffect(() => {
    const errorCheck: boolean[] = Object.values(formErrorsVerify);
    setCanSubmit(errorCheck.every((v) => v === false));
  }, [formErrorsVerify]);
  async function handleSubmit() {
    if (canSubmit) {
      if (password == passwordConfirm) {
        setIsLoading(true);
        try {
          const result = await axiosInstance.post(
            `${
              import.meta.env.VITE_API_URL
            }/funnel/v1.0/patient/otp-reset-password`,
            {
              otp: otp,
              email: email,
              phone: phoneNumberResend,
              password: password,
            }
          );
          if (result.data && result.data.success) {
            window.location.href =
              import.meta.env.VITE_ZENITH_URL +
              "/patient/login?resetpassword=true";
          }
        } catch (e: any) {
          console.log(e);
          enqueueSnackbar("Phone verification failed", {
            variant: "error",
          });
          setIsLoading(false);
        }
      }else{
        enqueueSnackbar("Passwords don't match", {
          variant: "error",
        });
      }
    }
  }

  async function handleSubmitResend() {
    setIsLoading(true);
    try {
      const result = await axiosInstance.post(
        `${
          import.meta.env.VITE_API_URL
        }/funnel/v1.0/patient/resend-otp-password`,
        {
          phone: phoneNumberResend,
          email: email,
        }
      );
      if (result.data.success) {
        enqueueSnackbar(
          "A new OTP has been sent your phone number : " + phoneNumberResend,
          {
            variant: "success",
          }
        );
      }
    } catch (e: any) {
      console.log(e);
      enqueueSnackbar("Resend otp failed", {
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  }
  return (
    <>
      {isLoading && <LoadingScreen />}
      <ThemeProvider theme={zenithTheme}>
        <Stack
          gap={1}
          marginBottom={"300px"}
          sx={{ marginBottom: "250px", width: "100%" }}
        >
          <Grid
            container
            direction={"column"}
            padding={"20px 0"}
            sx={{ borderRadius: "10px" }}
            boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
          >
            <Grid>
              <Typography
                sx={{
                  fontSize: "30px",
                  fontWeight: "bold",
                  lineHeight: "1em",
                  color: "green",
                }}
              >
                Reset password - phone verification
              </Typography>
            </Grid>
          </Grid>
          <Grid marginTop={"50px"} marginBottom={"10px"}>
            <Typography
              sx={{
                fontSize: "20px",
                lineHeight: "1em",
                color: "black",
                textAlign: "justify",
              }}
            >
              Please check your SMS at the following phone number :{" "}
              {phoneNumberResend}. If you didn't receive any SMS, click the
              button "Resend code".
            </Typography>
          </Grid>
          <Grid marginTop={"10px"} marginBottom={"30px"}>
            <Typography
              sx={{
                fontSize: "20px",
                lineHeight: "1em",
                color: "black",
                textAlign: "justify",
              }}
            >
              OTP not received ?{" "}
              <a
                style={{
                  textDecoration: "underline",
                  fontWeight: "bold",
                  color: "#007F00",
                  cursor : "pointer"
                }}
                onClick={handleSubmitResend}
              >
                Click here to resend
              </a>
            </Typography>
          </Grid>
          <Grid
            width={"100%"}
            borderRadius={"10px"}
            padding={!isDesktopOrMobile ? "20px 20px" : "50px"}
            boxShadow={"0px 2px 2px 0px rgba(0,0,0,0.2)"}
          >
            <Grid sx={{ width: "100%" }} width={"70%"} textAlign={"center"}>
              <Grid container direction={"column"} alignItems={"start"}>
                <Grid>
                  <Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
                    OTP code *
                  </Typography>
                </Grid>
                <TextField
                  sx={{ m: 0, mb: 1, ...inputStyle }}
                  type="text"
                  name="otp"
                  placeholder="Enter your OTP code"
                  size="small"
                  onChange={(e) => setOtp(e.target.value)}
                  margin="normal"
                  fullWidth
                  value={otp}
                  required={true}
                  error={formErrorsVerify.otp}
                  helperText={
                    formErrorsVerify.otp ? "Please enter the OTP code" : ""
                  }
                />
              </Grid>
              <Grid container direction={"column"} alignItems={"start"}>
                <Grid>
                  <Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
                    New password *
                  </Typography>
                </Grid>
                <TextField
                  sx={{ m: 0, mb: 1, ...inputStyle }}
                  type="password"
                  name="password"
                  placeholder="Enter your new password"
                  size="small"
                  onChange={(e) => setPassword(e.target.value)}
                  margin="normal"
                  fullWidth
                  value={password}
                  required={true}
                  error={formErrorsVerify.password}
                  helperText={
                    formErrorsVerify.password
                      ? "Please enter your new password"
                      : ""
                  }
                />
              </Grid>
              <Grid container direction={"column"} alignItems={"start"}>
                <Grid>
                  <Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
                    Confirm your password *
                  </Typography>
                </Grid>
                <TextField
                  sx={{ m: 0, mb: 1, ...inputStyle }}
                  type="password"
                  name="passwordConfirm"
                  placeholder="Confirm your password"
                  size="small"
                  onChange={(e) => setPasswordConfirmation(e.target.value)}
                  margin="normal"
                  fullWidth
                  value={passwordConfirm}
                  required={true}
                  error={formErrorsVerify.passwordConfirm}
                  helperText={
                    formErrorsVerify.passwordConfirm
                      ? "Please confirm your password"
                      : ""
                  }
                />
              </Grid>
              <Grid>
                <Button
                  type="submit"
                  fullWidth
                  disabled={!canSubmit}
                  variant="contained"
                  sx={{ mt: 2 }}
                  onClick={handleSubmit}
                >
                  Reset password
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Stack>
      </ThemeProvider>
    </>
  );
}

export default ResetPassword;
