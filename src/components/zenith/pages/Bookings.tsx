import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Card, CardContent, <PERSON>ton, <PERSON>alog, DialogTitle, DialogContent, DialogActions, Drawer, useMediaQuery, Container } from "@mui/material";
import { ArrowBack } from "@mui/icons-material";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import AccessTimeIcon from "@mui/icons-material/AccessTime";
import PersonIcon from "@mui/icons-material/Person";
import zenithTheme from "../../../styles/zenith/theme";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import { useFlow } from "../../../hooks/flow-controller";
import LoadingScreen from "../../../utils/loading-screen";
import axiosInstance from "../../../services/axios";
import Banner from "../layouts/Banner";
import CachedImage from "../common/CachedImage";
import { convertSydneyToClientTimezone, detectClientTimezone } from "../../../utils/date-formatter";

interface Booking {
  bookingId: string;
  patientId: string;
  date: string;
  day: string;
  timeSlot: string;
  queueType: string;
  bookingType: string;
  assignedDoctor: {
    name: string;
    email: string;
  };
  bookedBy: {
    name?: string;
    email?: string;
    type: 'admin' | 'patient';
  };
  createdAt: string;
  updatedAt: string;
  canCancel: boolean;
}

interface BookingsResponse {
  success: boolean;
  email: string;
  leadId: string; // Zoho ID
  bookings: Booking[];
  totalBookings: number;
}

function Bookings() {
  const [isLoading, setIsLoading] = useState(false);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [leadId, setLeadId] = useState<string>("");
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [bookingToCancel, setBookingToCancel] = useState<Booking | null>(null);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [clientTimezone, setClientTimezone] = useState<string>('Australia/Sydney');

  const { user } = useFlow();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const isDesktop = useMediaQuery(zenithTheme.breakpoints.up("md"));

  // Detect client timezone on component mount
  useEffect(() => {
    const timezone = detectClientTimezone();
    setClientTimezone(timezone);
  }, []);

  useEffect(() => {
    const fetchBookings = async () => {
      if (!user?.email) {
        return;
      }

      try {
        setIsLoading(true);

        // Fetch bookings using the user's email directly
        const bookingsResponse = await axiosInstance.get(
          `/zoho/v1.0/patient/${encodeURIComponent(user.email)}/bookings`
        );

        if (bookingsResponse.data?.success) {
          setBookings(bookingsResponse.data.bookings);
          setLeadId(bookingsResponse.data.leadId);
        } else {
          enqueueSnackbar("Could not fetch your bookings. Please try again.", {
            variant: "error",
          });
        }
      } catch (error) {
        console.error("Error fetching bookings:", error);
        enqueueSnackbar("An error occurred while fetching your bookings. Please try again.", {
          variant: "error",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [user?.email, enqueueSnackbar]);

  const formatBookingDateTime = (booking: Booking) => {
    try {
      console.log('Converting booking:', {
        date: booking.date,
        timeSlot: booking.timeSlot,
        clientTimezone
      });

      const converted = convertSydneyToClientTimezone(booking.date, booking.timeSlot, clientTimezone);

      console.log('Conversion result:', converted);

      return {
        dateString: converted.clientDateString,
        timeSlot: converted.clientTimeSlot,
        timezoneLabel: converted.timezoneLabel,
        isSameTimezone: converted.isSameTimezone
      };
    } catch (error) {
      console.warn('Error formatting booking date/time:', error);
      // Fallback to original formatting
      const date = new Date(booking.date);
      return {
        dateString: date.toLocaleDateString('en-AU', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        timeSlot: booking.timeSlot,
        timezoneLabel: 'AEDT/AEST',
        isSameTimezone: true
      };
    }
  };

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleLogout = () => {
    // Clear authentication data
    localStorage.removeItem('zenith_auth_user');
    localStorage.removeItem('zenith_authenticated');

    // Close drawer
    toggleDrawer();

    // Redirect to login page
    navigate({ to: "/patient/login" });
  };

  const handleCancelBooking = (booking: Booking) => {
    setBookingToCancel(booking);
    setCancelDialogOpen(true);
  };

  const confirmCancelBooking = async () => {
    if (!bookingToCancel || !leadId) {
      enqueueSnackbar("Missing booking information. Please try again.", {
        variant: "error",
      });
      return;
    }

    try {
      setIsLoading(true);

      const cancelData = {
        patientID: bookingToCancel.patientId,
        leadID: leadId,
        date: bookingToCancel.date
      };

      await axiosInstance.post('/doc/v1.0/complete/consultation', cancelData);

      enqueueSnackbar("Booking cancelled successfully.", {
        variant: "success",
      });

      // Remove the cancelled booking from the list
      setBookings(bookings.filter(b => b.bookingId !== bookingToCancel.bookingId));

      // Close dialog
      setCancelDialogOpen(false);
      setBookingToCancel(null);

    } catch (error) {
      console.error('Error cancelling booking:', error);
      enqueueSnackbar("An error occurred while cancelling your booking. Please try again.", {
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const redirectToSchedule = async () => {
    try {
      setIsLoading(true);
      const response = await axiosInstance.post(
        `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/encrypt-id`,
        { user },
        { withCredentials: true }
      );

      if (response.data?.data?.leadID) {
        const encryptedLeadID = response.data.data.leadID;
        const baseUrl = `${import.meta.env.VITE_DRUI_URL}/schedule`;
        const url = `${baseUrl}?token=${encryptedLeadID}`;
        window.location.href = url;
      } else {
        enqueueSnackbar("Could not generate your booking link. Please contact support.", {
          variant: "error",
        });
      }
    } catch (error) {
      console.error('Error generating booking link:', error);
      enqueueSnackbar("An error occurred while accessing your booking. Please try again.", {
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ThemeProvider theme={zenithTheme}>
      <Box sx={{ display: "flex", flexDirection: "column", minHeight: "100vh", backgroundColor: "white" }}>
        {/* Custom Header */}
        <Box sx={{ width: "100%" }}>
         <Banner />

          {/* Nav Bar */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              padding: isDesktop ? "0 40px" : "0 20px",
              backgroundColor: "white",
              paddingTop: { xs: '0px', sm: '0px' }, // Account for banner height + some spacing
              paddingBottom: "10px",
              maxWidth: isDesktop ? "1200px" : "100%",
              margin: isDesktop ? "0 auto" : "0",
              width: "100%"
            }}
          >
            {/* Desktop Navigation */}
            {isDesktop ? (
              <>
                <CachedImage
                  src="/zenith/zenith-logo.png"
                  alt="Zenith Clinics"
                  sx={{
                    height: "24px",
                  }}
                />
                <Box sx={{ display: "flex", gap: "30px", alignItems: "center" }}>
                  <Button
                    sx={{
                      color: '#217F00',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      textTransform: 'none',
                      '&:hover': { backgroundColor: 'rgba(33, 127, 0, 0.1)' }
                    }}
                    onClick={() => navigate({ to: "/patient/home" })}
                  >
                    Back to Home
                  </Button>
                  <Button
                    sx={{
                      color: '#217F00',
                      fontSize: '16px',
                      fontWeight: 'bold',
                      textTransform: 'none',
                      '&:hover': { backgroundColor: 'rgba(33, 127, 0, 0.1)' }
                    }}
                    onClick={handleLogout}
                  >
                    Logout
                  </Button>
                </Box>
              </>
            ) : (
              /* Mobile Navigation */
              <>
                <IconButton
                  aria-label="menu"
                  sx={{ padding: "8px" }}
                  onClick={toggleDrawer}
                >
                  <MenuIcon />
                </IconButton>

                <CachedImage
                  src="/zenith/zenith-logo.png"
                  alt="Zenith Clinics"
                  sx={{
                    height: "19.14px",
                  }}
                />
              </>
            )}
          </Box>
        </Box>

        {/* Navigation Drawer */}
        <Drawer
          anchor="top"
          open={drawerOpen}
          onClose={toggleDrawer}
          keepMounted={true}
          sx={{
            '& .MuiDrawer-paper': {
              width: '100%',
              maxWidth: '100%',
              boxSizing: 'border-box',
              padding: '20px 0 0 0',
              height: 'auto',
              maxHeight: '500px',
              overflowY: 'auto',
              borderBottom: '1px solid #e0e0e0',
              top: { xs: '40px', sm: '48px' }, // Push down by banner height
              zIndex: 1299, // Just below the banner
            },
          }}
        >
          {/* Drawer Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
            <CachedImage
              src="/zenith/zenith-logo.png"
              alt="Zenith Clinics"
              sx={{ height: '36px' }}
            />
            <IconButton onClick={toggleDrawer}>
              <CloseIcon />
            </IconButton>
          </Box>

          {/* Divider */}
          <Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

          {/* Navigation Links */}
          <Box sx={{ padding: '0 20px 20px' }}>
            <Button
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none',
                borderBottom: '1px solid #f0f0f0'
              }}
              onClick={() => {
                toggleDrawer();
                navigate({ to: "/patient/home" });
              }}
            >
              Back to Home
            </Button>
            <Button
              sx={{
                color: '#217F00',
                fontSize: '18px',
                fontWeight: 'bold',
                justifyContent: 'flex-start',
                padding: '15px 0',
                width: '100%',
                textTransform: 'none'
              }}
              onClick={handleLogout}
            >
              Logout
            </Button>
          </Box>
        </Drawer>

        {/* Main Content */}
        <Container
          maxWidth={isDesktop ? "lg" : false}
          sx={{
            flexGrow: 1,
            display: "flex",
            flexDirection: "column",
            padding: isDesktop ? "40px 20px" : "20px",
            backgroundColor: "white",
            width: "100%"
          }}
        >
          {/* Back Button - Only show on mobile since desktop has nav */}
          {!isDesktop && (
            <Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
              <Button
                variant="outlined"
                startIcon={<ArrowBack />}
                onClick={() => navigate({ to: "/patient/home" })}
                sx={{
                  color: "#217F00",
                  borderColor: "#217F00",
                  fontSize: "16px",
                  fontWeight: "bold",
                  textTransform: "none",
                  padding: "8px 16px",
                  "&:hover": {
                    backgroundColor: "rgba(33, 127, 0, 0.04)",
                    borderColor: "#217F00",
                  },
                }}
              >
                Back to Home
              </Button>
            </Box>
          )}

          {/* Page Title */}
          <Box sx={{
            maxWidth: "800px",
            margin: isDesktop ? "30px auto 30px auto" : "20px auto 20px auto",
            width: "100%"
          }}>
            <Typography
              sx={{
                fontSize: isDesktop ? "40px" : "32px",
                fontWeight: "bold",
                color: "#333",
                textAlign: "center"
              }}
            >
              My Bookings
            </Typography>
            {clientTimezone !== 'Australia/Sydney' && (
              <Typography
                sx={{
                  fontSize: "14px",
                  color: "#666",
                  textAlign: "center",
                  marginTop: "8px"
                }}
              >
                Times shown in your local timezone
              </Typography>
            )}
          </Box>

          {/* Bookings Content */}
          <Box sx={{ maxWidth: "800px", margin: "0 auto", width: "100%" }}>
          {bookings.length === 0 ? (
            <Box sx={{ textAlign: "center", marginTop: isDesktop ? "80px" : "50px" }}>
              <Typography sx={{ fontSize: isDesktop ? "20px" : "18px", marginBottom: "20px", color: "#666" }}>
                You don't have any upcoming bookings.
              </Typography>
              <Button
                variant="contained"
                sx={{
                  backgroundColor: "#217F00",
                  color: "white",
                  padding: isDesktop ? "15px 40px" : "12px 30px",
                  borderRadius: "25px",
                  textTransform: "none",
                  fontSize: isDesktop ? "18px" : "16px",
                  fontWeight: "bold",
                  '&:hover': {
                    backgroundColor: "#1a6600",
                    transform: "translateY(-2px)",
                    transition: "all 0.3s ease"
                  }
                }}
                onClick={redirectToSchedule}
              >
                Book a Consultation
              </Button>
            </Box>
          ) : (
            <Box>
              <Typography sx={{ fontSize: "16px", marginBottom: "20px", color: "#666" }}>
                You have {bookings.length} booking{bookings.length !== 1 ? 's' : ''}
              </Typography>
              
              {bookings.map((booking) => {
                const formattedDateTime = formatBookingDateTime(booking);

                return (
                <Card
                  key={booking.bookingId}
                  sx={{
                    marginBottom: "15px",
                    borderRadius: "12px",
                    boxShadow: isDesktop ? "0 4px 12px rgba(0,0,0,0.1)" : "0 2px 8px rgba(0,0,0,0.1)",
                    '&:hover': isDesktop ? {
                      boxShadow: "0 6px 20px rgba(0,0,0,0.15)",
                      transform: "translateY(-2px)",
                      transition: "all 0.3s ease"
                    } : {}
                  }}
                >
                  <CardContent sx={{ padding: "20px" }}>
                    {/* <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start", marginBottom: "15px" }}>
                      <Typography sx={{ fontSize: "18px", fontWeight: "bold", color: "#333" }}>
                        {booking.bookingType === 'consultation' ? 'Medical Consultation' : booking.bookingType}
                      </Typography>
                      <Chip
                        label={booking.queueType}
                        size="small"
                        sx={{
                          backgroundColor: "#217F00",
                          color: "white",
                          textTransform: "capitalize"
                        }}
                      />
                    </Box> */}

                    <Box sx={{ display: "flex", alignItems: "center", marginBottom: "10px" }}>
                      <CalendarTodayIcon sx={{ fontSize: "18px", color: "#666", marginRight: "8px" }} />
                      <Typography sx={{ fontSize: "16px", color: "#333" }}>
                        {formattedDateTime.dateString}
                      </Typography>
                    </Box>

                    <Box sx={{ display: "flex", alignItems: "center", marginBottom: "10px" }}>
                      <AccessTimeIcon sx={{ fontSize: "18px", color: "#666", marginRight: "8px" }} />
                      <Typography sx={{ fontSize: "16px", color: "#333" }}>
                        {formattedDateTime.timeSlot}
                        {!formattedDateTime.isSameTimezone && (
                          <Typography component="span" sx={{ fontSize: "14px", color: "#666", marginLeft: "8px" }}>
                            ({formattedDateTime.timezoneLabel})
                          </Typography>
                        )}
                      </Typography>
                    </Box>

                    {booking.assignedDoctor.name && (
                      <Box sx={{ display: "flex", alignItems: "center", marginBottom: "20px" }}>
                        <PersonIcon sx={{ fontSize: isDesktop ? "20px" : "18px", color: "#666", marginRight: "10px" }} />
                        <Typography sx={{ fontSize: isDesktop ? "18px" : "16px", color: "#333", fontWeight: "500" }}>
                          Dr. {booking.assignedDoctor.name}
                        </Typography>
                      </Box>
                    )}

                    <Box sx={{
                      display: "flex",
                      gap: isDesktop ? "15px" : "10px",
                      marginTop: "20px",
                      flexDirection: isDesktop ? "row" : "row",
                      justifyContent: isDesktop ? "flex-start" : "flex-start"
                    }}>
                      <Button
                        variant="outlined"
                        size={isDesktop ? "medium" : "small"}
                        sx={{
                          borderColor: "#217F00",
                          color: "#217F00",
                          textTransform: "none",
                          borderRadius: "20px",
                          padding: isDesktop ? "10px 20px" : "6px 16px",
                          fontSize: isDesktop ? "16px" : "14px",
                          '&:hover': {
                            backgroundColor: "rgba(33, 127, 0, 0.1)",
                            borderColor: "#217F00"
                          }
                        }}
                        onClick={redirectToSchedule}
                      >
                        Reschedule
                      </Button>
                      {booking.canCancel && (
                        <Button
                          variant="outlined"
                          size={isDesktop ? "medium" : "small"}
                          sx={{
                            borderColor: "#d32f2f",
                            color: "#d32f2f",
                            textTransform: "none",
                            borderRadius: "20px",
                            padding: isDesktop ? "10px 20px" : "6px 16px",
                            fontSize: isDesktop ? "16px" : "14px",
                            '&:hover': {
                              backgroundColor: "rgba(211, 47, 47, 0.1)",
                              borderColor: "#d32f2f"
                            }
                          }}
                          onClick={() => handleCancelBooking(booking)}
                        >
                          Cancel
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
                );
              })}
            </Box>
          )}
          </Box>
        </Container>

        {/* Cancel Confirmation Dialog */}
        <Dialog
          open={cancelDialogOpen}
          onClose={() => setCancelDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Cancel Booking
          </DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to cancel your booking for {bookingToCancel && (() => {
                const formatted = formatBookingDateTime(bookingToCancel);
                return `${formatted.dateString} at ${formatted.timeSlot}`;
              })()} ?
            </Typography>
            <Typography sx={{ marginTop: "10px", color: "#666", fontSize: "14px" }}>
              This action cannot be undone.
            </Typography>
          </DialogContent>
          <DialogActions sx={{ padding: "16px 24px" }}>
            <Button
              onClick={() => setCancelDialogOpen(false)}
              sx={{
                color: "#666",
                textTransform: "none"
              }}
            >
              Keep Booking
            </Button>
            <Button
              onClick={confirmCancelBooking}
              variant="contained"
              sx={{
                backgroundColor: "#d32f2f",
                color: "white",
                textTransform: "none",
                "&:hover": {
                  backgroundColor: "#b71c1c"
                }
              }}
            >
              Cancel Booking
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </ThemeProvider>
  );
}

export default Bookings;
