import React from "react";
import { Ty<PERSON><PERSON>, Button, Box, TextField, Stack } from "@mui/material";
import Grid from "@mui/material/Grid2";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import StarIcon from "@mui/icons-material/Star";

const ThankYouMessage = () => {
	const [rating, setRating] = React.useState<number>(0);

	return (
		<Grid
			container
			spacing={2}
			direction={"column"}
			alignItems="center"
			justifyContent="center"
			style={{ minHeight: "100vh", padding: "20px" }}
		>
			<Grid size={12}>
				<Typography variant="h4" align="center" gutterBottom fontWeight={700}>
					Share a Kind Word With <span style={{ color: "green" }}>Your Doctor</span>
				</Typography>
			</Grid>
			<Grid size={12}>
				<Typography variant="body2" align="center" gutterBottom>
					We&apos;re glad to be on this journey with you. As part of your ongoing treatment plan at Zenith
					Clinics, you&apos;re welcome to{" "}
					<span style={{ fontWeight: 700 }}>share a few kind words about your doctor.</span>
				</Typography>
			</Grid>
			<Grid size={12}>
				<Stack
					width="100%"
					maxWidth={600}
					spacing={2}
					direction={"column"}
					p={4}
					justifyContent={"center"}
					sx={{ backgroundColor: "#E8E8E8", borderRadius: "20px" }}
				>
					<Typography variant="body2" fontWeight={700}>
						How would you rate your Doctor experience?{" "}
						<span style={{ fontWeight: "normal" }}> (Choose from 1 to 5 stars)</span>
					</Typography>
					<Stack direction="row" spacing={1} width={"100%"} justifyContent="center">
						{[1, 2, 3, 4, 5].map((star) => (
							<span key={star} style={{ cursor: "pointer" }} onClick={() => setRating(star)}>
								{rating >= star ? (
									<StarIcon fontSize="large" color="success" />
								) : (
									<StarBorderIcon color="success" fontSize="large" />
								)}
							</span>
						))}
					</Stack>
				</Stack>
			</Grid>
			<Grid size={12}>
				<Stack
					direction="column"
					justifyContent={"center"}
					alignItems="center"
					spacing={2}
					p={4}
					sx={{ backgroundColor: "#E8E8E8", borderRadius: "20px" }}
				>
					<Typography variant="body2" align="center" fontWeight={700}>
						What did you appreciate about your Doctor’s appointment?
					</Typography>
					<TextField
						multiline
						rows={6}
						variant="outlined"
						placeholder="Type Your Message Here"
						fullWidth
						slotProps={{
							input: {
								style: {
									fontSize: "12px",
									padding: "10px",
									borderRadius: "10px",
								},
							},
						}}
					/>
				</Stack>
			</Grid>
			<Grid size={12}>
				<Button
					variant="contained"
					color="success"
					fullWidth
					size="large"
					style={{ borderRadius: "50px", padding: "10px 20px" }}
				>
					Submit
				</Button>
			</Grid>
			<Grid size={12}>
				<Typography variant="caption" align="center" color="textSecondary">
					<span style={{ fontWeight: 700 }}>Please note: </span>Your doctor won’t be able to reply to this
					message. If you need urgent help or are experiencing a medical emergency, please seek immediate
					assistance through the appropriate emergency services.
				</Typography>
			</Grid>
		</Grid>
	);
};
export default ThankYouMessage;
