import { useEffect, useState } from "react";
import {
  MobileStepper,
  Box,
  Button,
  Stack,
  TextField,
  Typography,
  Divider,
  Checkbox,
  FormControlLabel,
  ThemeProvider,
  useMediaQuery,
  Avatar,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import styles from "../../../css/confirmation.module.css";
import ConfirmationDialog from "../dialogs/ConfirmationDialog";
import CancelConfirmationDialog from "../dialogs/CancelConfirmationDialog";
import { AuthUser } from "../../../types";
import axios from "axios";
import { useFlow } from "../../../hooks/flow-controller";
import DontOfferDialog from "../dialogs/DontOfferDialog";

const resendButtonStyle = {
  backgroundColor: "white",
  color: zenithTheme.palette.primary.main,
  borderColor: zenithTheme.palette.primary.main,
};

const inputStyle = {
  backgroundColor: "white",
  borderRadius: "20px",
  "& .MuiOutlinedInput-root": {
    borderRadius: "20px",
    "&:hover": {
      borderColor: "black",
    },
    "&.Mui-focused fieldset": {
      borderColor: "black",
      borderRadius: "20px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "white",
  },
};

function ConfirmationOnCall() {
  const [isLoading, setIsLoading] = useState(true);
  const [messageDialog, setMessageDialog] = useState("");
  const [message, setMessage] = useState("");
  const [doctorName, setDoctorName] = useState("Dr. Hussain Anjum"); // Default fallback
  const [doctorImage, setDoctorImage] = useState("/zenith/dr.png"); // Default fallback image
  const [hasImage, setHasImage] = useState(false);
  const [token, setToken] = useState("");
  const { user, setUser } = useFlow();

  const [leadID, setLeadID] = useState("");
  const [openCancelConfirmationDialog, setOpenCancelConfirmationDialog] =
    useState(false);
  const [openConfirmationDialog, setOpenConfirmationDialog] = useState(false);
  const [openDontOfferDialog, setOpenDontOfferDialog] = useState(false);
  const searchParams = new URLSearchParams(window.location.search);
  const tokenParam = searchParams.get("token");
  
  // Helper function to get the first name initial from doctor name
  const getFirstNameInitial = (fullName: string): string => {
    // Extract the first name from the full name (assuming format "Dr. First Last")
    const nameWithoutTitle = fullName.replace(/^Dr\.\s+/, '');
    const firstNameInitial = nameWithoutTitle.charAt(0).toUpperCase();
    return firstNameInitial;
  };

  async function handleSubmitConfirm(e: any) {
    try {
      e.preventDefault();
      setIsLoading(true);
      const data = await getZohoLeadById(leadID);
      await updateZohoLead(leadID);
      const dataToUsed = data.data.at(0);
      setMessageDialog(
        "Thank you for booking your consultation  on " +
          new Date(dataToUsed["Consult_Date_Time"]).toLocaleDateString(
            "en-AU",
            {
              timeZone: "Australia/Sydney",
            }
          ) +
          " between " +
          new Date(dataToUsed["Consult_Range_Start"]).toLocaleTimeString(
            "en-AU",
            {
              timeZone: "Australia/Sydney",
              hour: "2-digit",
              minute: "2-digit",
            }
          ) +
          " and " +
          new Date(dataToUsed["Consult_Range_End"]).toLocaleTimeString(
            "en-AU",
            {
              timeZone: "Australia/Sydney",
              hour: "2-digit",
              minute: "2-digit",
            }
          ) +
          " (AEST)."
      );
      setIsLoading(false);
      setOpenConfirmationDialog(true);
    } catch (e: any) {
      enqueueSnackbar("An error occured", {
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  }
  const updateZohoLead = async (leadId: string) => {
    await axiosInstance.post(
      `${
        import.meta.env.VITE_API_URL
      }/funnel/v1.0/patient/updateZohoLeadBookingStatus`,
      {
        leadId: leadId,
      }
    );
  };
  const updateZohoLeadReachConfirmationPage = async (leadId: string) => {
    await axiosInstance.post(
      `${
        import.meta.env.VITE_API_URL
      }/funnel/v1.0/patient/updateZohoLeadReachConfirmationPage`,
      {
        leadId: leadId,
      }
    );
  };
  const getZohoLeadById = async (leadId: String) => {
    const result = await axiosInstance.get(
      `${
        import.meta.env.VITE_API_URL
      }/funnel/v1.0/patient/getZohoLeadById/${leadId}`
    );
    if (result.status == 200) {
      return result.data;
    } else {
      enqueueSnackbar("Confirmation process failed", {
        variant: "error",
      });
      throw new Error("An error occured");
    }
  };
  
  // Function to get consultation data from database
  const getConsultationFromDatabase = async (zohoId: string) => {
    try {
      const result = await axiosInstance.get(
        `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/consultation/${zohoId}`
      );
      if (result.status === 200 && result.data) {
        // Map database fields to the format expected by the confirmation message
        const consultation = result.data;
        
        // Check if username is available and set doctor name
        if (consultation.data && consultation.data.doctorUsername) {
          setDoctorName(consultation.data.doctorUsername);
        }
        
        // Check if doctor email is available and set doctor image
        if (consultation.data && consultation.data.doctorEmail) {
          setDoctorImage(`/zenith/${consultation.data.doctorEmail}.png`);
          setHasImage(true);
        } else {
          setHasImage(false);
        }

        // Return the database fields directly
        return consultation;
      }
      return null;
    } catch (error) {
      console.log("Error fetching consultation from database:", error);
      return null;
    }
  };

  useEffect(() => {
    async function init() {
      if (tokenParam && tokenParam.length > 0) {
        try {
          const resultDecrypt = await axiosInstance.post(
            `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/decrypt-id`,
            { leadID: tokenParam }
          );
          if (resultDecrypt.data.leadID) {
            const leadID = resultDecrypt.data.leadID;
            const result = await axiosInstance.get(
              `${
                import.meta.env.VITE_API_URL
              }/funnel/v1.0/patient/bylead/${leadID}`
            );
            if (result.data.user) {
              setUser(result.data.user as AuthUser);
              setLeadID(result.data.user.zohoID);
              
              // Check if username is available in user data
              if (result.data.user.username) {
                setDoctorName(result.data.user.username);
              }
              
              // Get consultation data to get doctor information
              const zohoID = result.data.user.zohoID;
              if (zohoID) {
                const dbConsultation = await getConsultationFromDatabase(zohoID);
                // Doctor name will be updated in the getConsultationFromDatabase function if available
              }
              
              await updateZohoLeadReachConfirmationPage(
                result.data.user.zohoID
              );
              setMessage("Dear " + result.data.user.fullName);
              setIsLoading(false);
            } else {
              enqueueSnackbar("Retreiving lead has failed", {
                variant: "error",
              });
            }
          }
        } catch (e: any) {
          console.log(e);
          enqueueSnackbar("An error occured", {
            variant: "error",
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        enqueueSnackbar("No token found", {
          variant: "error",
        });
      }
    }
    init();
  }, []);
  
  // Check if image exists when doctorImage changes
  useEffect(() => {
    const img = new Image();
    img.src = doctorImage;
    img.onload = () => {
      setHasImage(true);
    };
    img.onerror = () => {
      setHasImage(false);
    };
  }, [doctorImage]);
  
  const navigate = useNavigate();
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));

  const { enqueueSnackbar } = useSnackbar();
  return (
    <>
      {isLoading && <LoadingScreen />}
      <ThemeProvider theme={zenithTheme}>
        <Stack
          gap={1}
          marginBottom={"300px"}
          sx={{ marginBottom: "250px", width: "100%" }}
        >
          <Grid container direction={"column"} paddingTop={"20px"}>
            <Grid>
              <Typography
                sx={{
                  fontSize: "22px",
                  fontWeight: "bold",
                  lineHeight: "2em",
                }}
              >
                Message from Your Doctor Ahead of Your{" "}
                <span style={{ color: "green" }}>Consultation</span>
              </Typography>
            </Grid>
          </Grid>
          <Grid marginBottom={"20px"}>
            <div className={styles.imageContainer}>
              {hasImage ? (
                <img className={styles.drImage} src={doctorImage} onError={() => setHasImage(false)} />
              ) : (
                <Avatar 
                  sx={{ 
                    width: '100%', 
                    height: '100%',
                    bgcolor: zenithTheme.palette.primary.main,
                    fontSize: '2rem',
                    fontWeight: 'bold'
                  }}
                >
                  {getFirstNameInitial(doctorName)}
                </Avatar>
              )}
              <img className={styles.notifImage} src="/zenith/notif.png" />
            </div>
          </Grid>
          <Grid sx={{ width: "100%" }} textAlign={"start"}>
            <Grid container direction={"column"} alignItems={"start"}>
              <div>
                <span>{message}</span>
                <p>
                  I'm looking forward to meeting you for your scheduled
                  consultation. Ahead of our appointment, I'd like to provide
                  some key details about how Zenith Clinics operates to ensure
                  our services align with your treatment expectations:
                </p>
                <ul>
                  <li>
                    If eligible, I will create a treatment plan, giving you
                    access to purchase approved products through our partner
                    sites. Please note that we DO NOT write e-scripts.
                  </li>
                  <li>
                    We currently only provide dried flower. (prices range from
                    $8/g to $14/g)
                  </li>
                  <li>
                    Currently, oils, cartridges and edibles are not available
                    yet
                  </li>
                </ul>
                <p>
                  If this does not meet your needs, you can cancel or otherwise,
                  confirm your appointment below.
                </p>
              </div>
              <img className={styles.signImage} src="/zenith/sign.png" />
              <p style={{ marginTop: "40px" }}>
                <span>Best regards, {doctorName}</span>
              </p>
            </Grid>
          </Grid>
          {leadID.length > 0 && (
            <Grid
              sx={{ width: "100%" }}
              textAlign={"center"}
              container
              direction={"row"}
              alignItems={"center"}
              justifyContent={"space-evenly"}
            >
              <Grid
                sx={{ width: isDesktopOrMobile ? "40%" : "80%" }}
                textAlign={"center"}
                container
                direction={"row"}
                alignItems={"center"}
                justifyContent={"space-evenly"}
              >
                <Button
                  type="submit"
                  variant="contained"
                  sx={{ mt: 2 }}
                  onClick={(e) => setOpenDontOfferDialog(true)}
                >
                  Confirm
                </Button>
                <Button
                  type="submit"
                  variant="outlined"
                  sx={{ mt: 2 }}
                  onClick={() => setOpenCancelConfirmationDialog(true)}
                >
                  Cancel
                </Button>
              </Grid>
            </Grid>
          )}

          <Grid
            sx={{ width: "100%", fontSize: "14px", paddingTop: "50px" }}
            textAlign={"start"}
            container
            direction={"column"}
            alignItems={"start"}
          >
            <span>
              <i>
                Any information you provide today is confidential and compliant
                with the Medical Board of Australia Good Medical Practice code,
                RACGP Standards of General Practice and our Medical
                Confidentially Duty of Conduct for doctors in Australia, which
                means we protect your privacy and right to confidentiality
              </i>
            </span>
          </Grid>
          <ConfirmationDialog
            message={messageDialog}
            openDialog={openConfirmationDialog}
            setOpenDialog={setOpenConfirmationDialog}
          />
          <DontOfferDialog
            onClickAction={handleSubmitConfirm}
            openDialog={openDontOfferDialog}
            setOpenDialog={setOpenDontOfferDialog}
          ></DontOfferDialog>
          <CancelConfirmationDialog
            leadId={leadID}
            openDialog={openCancelConfirmationDialog}
            setOpenDialog={setOpenCancelConfirmationDialog}
          />
        </Stack>
      </ThemeProvider>
    </>
  );
}

export default ConfirmationOnCall;
