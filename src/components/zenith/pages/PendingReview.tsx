import { Typography, But<PERSON> } from "@mui/material";
import Grid from "@mui/material/Grid2";

const PendingReview = () => {
	return (
		<Grid
			container
			direction="column"
			sx={{ width: "100%" }}
			justifyContent="center"
			alignItems="center"
			textAlign="center"
			spacing={2}
		>
			<Typography
				sx={{
					fontSize: "24px",
					fontWeight: "bold",
					lineHeight: "1em",
				}}
			>
				Pending Review
			</Typography>
			<img src="/zenith/pending.png" alt="Pending Review" style={{ width: "200px", height: "auto" }} />
			<Grid
				size={12}
				sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
				textAlign="start"
				container
				direction="column"
				alignItems="center"
			>
				<Typography
					sx={{
						fontSize: "16px",
						lineHeight: "1em",
						paddingBottom: "10px",
					}}
				>
					Your application is currently under review. We will notify you by email once a decision has been
					made.
				</Typography>
				<Button
					type="submit"
					fullWidth
					variant="contained"
					sx={{ mt: 2, backgroundColor: "green", color: "#fff", maxWidth: "360px", width: "100%" }} // Custom styles for the button
					onClick={() => {
						window.location.href = "https://zenith.clinic/contact"; // Redirect to contact page
					}}
				>
					Contact Us
				</Button>
			</Grid>
		</Grid>
	);
};

export default PendingReview;
