import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  Typography,
  ThemeProvider,
  useMediaQuery,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import axios from "axios";
import { AuthUser } from "../../../types";
import LoadingScreen from "../../../utils/loading-screen";
import { useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import zenithTheme from "../../../styles/zenith/theme";
import axiosInstance from "../../../services/axios";
import UserSteps from "../../../types/enum";
import { FlowController, useFlow } from "../../../hooks/flow-controller";

const resendButtonStyle = {
  backgroundColor: "white",
  color: zenithTheme.palette.primary.main,
  borderColor: zenithTheme.palette.primary.main,
};

const inputStyle = {
  backgroundColor: "white",
  borderRadius: "13px",
  "& .MuiOutlinedInput-root": {
    borderRadius: "13px",
    "&:hover": {
      borderColor: "black",
    },
    "&.Mui-focused fieldset": {
      borderColor: "black",
      borderRadius: "13px",
    },
  },
  "& .MuiInputLabel-root": {
    color: "#3B3B3B",
  },
  "& .MuiInputLabel-root.Mui-focused": {
    color: "white",
  },
};

function ForgotPassword() {
  const [isLoading, setIsLoading] = useState(false);
  const [phone, setPhone] = useState("");
  const [canSubmit, setCanSubmit] = useState(false);
  const navigate = useNavigate();
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));

  const { enqueueSnackbar } = useSnackbar();
  useEffect(() => {
    setCanSubmit(phone.length>0);
  }, [phone]);
  async function handleSubmit() {
    if(canSubmit){
      setIsLoading(true);
      try {
        const result = await axiosInstance.post(
          `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/forgot-password`,
          {
            phone: phone,
          }
        );
        if (result.data && result.data.success && result.data.email) {
          enqueueSnackbar("Your phone number received an OTP.", {
            variant: "success",
          });
          const email = result.data.email;
          navigate({
            to: "/patient/reset-password?phone=p-" + phone + "&email=" + email,
          });
        } else {
          enqueueSnackbar("Forgot password failed", {
            variant: "error",
          });
        }
      } catch (e: any) {
        console.log(e);
        enqueueSnackbar("Forgot password failed", {
          variant: "error",
        });
      } finally {
        setIsLoading(false);
      }
    }
  }
  return (
    <>
      {isLoading && <LoadingScreen />}
      <ThemeProvider theme={zenithTheme}>
        <Stack
          gap={1}
          marginBottom={"300px"}
          sx={{ marginBottom: "250px", width: "100%" }}
        >
          <Grid
            container
            direction={"column"}
            padding={"20px 0"}
            sx={{ borderRadius: "10px" }}
            boxShadow={"0px 3px 3px 0px rgba(0,0,0,0.25)"}
          >
            <Grid>
              <Typography
                sx={{
                  fontSize: "30px",
                  fontWeight: "bold",
                  lineHeight: "1em",
                  color: "green",
                }}
              >
                Forgot password
              </Typography>
            </Grid>
          </Grid>
          <Grid marginTop={"50px"} marginBottom={"30px"}>
            <Typography
              sx={{
                fontSize: "20px",
                lineHeight: "1em",
                textAlign: "justify",
                color: "black",
              }}
            >
              Forgot your password? Please enter your phone number and you will
              receive an OTP.
            </Typography>
          </Grid>
          <Grid
            width={"100%"}
            borderRadius={"10px"}
            padding={!isDesktopOrMobile ? "20px 20px" : "50px"}
            boxShadow={"0px 2px 2px 0px rgba(0,0,0,0.2)"}
          >
            <Grid sx={{ width: "100%" }} width={"70%"} textAlign={"center"}>
              <Grid container direction={"column"} alignItems={"start"}>
                <Grid>
                  <Typography sx={{ fontSize: "12px", mb: 1 }} align="left">
                    Phone number *
                  </Typography>
                </Grid>
                <TextField
                  sx={{ m: 0, ...inputStyle }}
                  type="text"
                  name="phone"
                  placeholder="Enter your phone number"
                  size="small"
                  onChange={(e) => setPhone(e.target.value)}
                  margin="normal"
                  fullWidth
                  value={phone}
                  required={true}
                  
                />
              </Grid>
              <Grid>
                <Button
                  type="submit"
                  fullWidth
                  disabled={!canSubmit}
                  variant="contained"
                  sx={{ mt: 2 }}
                  onClick={handleSubmit}
                >
                  Reset password
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Stack>
      </ThemeProvider>
    </>
  );
}

export default ForgotPassword;
