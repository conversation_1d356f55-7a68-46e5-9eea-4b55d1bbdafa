import { useState, useEffect } from "react";
import {
	Box,
	Button,
	Typography,
	Stack,
	FormControl,
	FormLabel,
	RadioGroup,
	FormControlLabel,
	Radio,
	Divider,
	ThemeProvider,
	IconButton,
	Drawer,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import { ArrowBack } from "@mui/icons-material";
import InstagramIcon from "@mui/icons-material/Instagram";
import axios from "axios";
import { useSnackbar } from "notistack";
import { useNavigate } from "@tanstack/react-location";
import { useFlow } from "../../../hooks/flow-controller";
import axiosInstance from "../../../services/axios";
import zenithTheme from "../../../styles/zenith/theme";
import Banner from "../layouts/Banner";
import CachedImage from "../common/CachedImage";

// TypeScript interfaces
interface ExtendTPFormData {
	adherence: string;
	symptomImprovement: string;
	symptomFrequency: string;
	additionalRelief: string;
	functionalBenefit: string;
	sleepQuality: string;
	tolerance: string;
	sideEffectSeverity: string;
	sideEffectTolerability: string;
	overallSatisfaction: string;
	goalAchievement: string;
	treatmentIntent: string;
}

interface ScoringState {
	totalScore: number;
	maxScore: number;
	isEligible: boolean;
	questionScores: Record<string, number>;
}

const ExtendTP = () => {
	const [step, setStep] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [drawerOpen, setDrawerOpen] = useState(false);
	const [contactId, setContactId] = useState<string | null>(null);
	const { enqueueSnackbar } = useSnackbar();
	const navigate = useNavigate();
	const { user } = useFlow();

	// Navigation functions
	const toggleDrawer = () => {
		setDrawerOpen(!drawerOpen);
	};

	const handleLogout = () => {
		// Clear authentication data
		localStorage.removeItem("zenith_auth_user");
		localStorage.removeItem("zenith_authenticated");

		// Close drawer
		toggleDrawer();

		// Redirect to login page
		navigate({ to: "/patient/login" });
	};

	const navigateToShop = () => {
		if (user?.email && contactId) {
			const safeContactId = contactId.startsWith("p") ? contactId : `p${contactId}`;
			const shopUrl = `https://letsroll.harvest.delivery/members-shop?email=${encodeURIComponent(
				user.email
			)}&contact=${encodeURIComponent(safeContactId)}`;
			window.location.href = shopUrl;
		} else {
			enqueueSnackbar("Unable to access shop. Please contact support.", {
				variant: "error",
			});
		}
	};

	// Form data state
	const [formData, setFormData] = useState<ExtendTPFormData>({
		adherence: "",
		symptomImprovement: "",
		symptomFrequency: "",
		additionalRelief: "",
		functionalBenefit: "",
		sleepQuality: "",
		tolerance: "",
		sideEffectSeverity: "",
		sideEffectTolerability: "",
		overallSatisfaction: "",
		goalAchievement: "",
		treatmentIntent: "",
	});

	// Scoring state
	const [scoring, setScoring] = useState<ScoringState>({
		totalScore: 0,
		maxScore: 60,
		isEligible: false,
		questionScores: {},
	});

	const [canProceed, setCanProceed] = useState(false);

	// Reusable styled FormControlLabel component
	const StyledFormControlLabel = ({ value, label, ...props }: any) => (
		<FormControlLabel
			value={value}
			control={<Radio sx={{ alignSelf: 'flex-start', mt: '2px' }} />}
			label={label}
			sx={{
				alignItems: 'flex-start',
				margin: '8px 0',
				'& .MuiFormControlLabel-label': {
					fontSize: '16px',
					lineHeight: 1.5,
					color: '#333',
					marginTop: 0,
					textAlign: 'left'
				}
			}}
			{...props}
		/>
	);

	// Handle radio button changes
	const handleRadioChange = (questionKey: keyof ExtendTPFormData, value: string) => {
		setFormData(prev => ({
			...prev,
			[questionKey]: value
		}));
	};

	// Scoring mapping object
	const scoringMap: Record<string, Record<string, number>> = {
		adherence: {
			'always-followed': 5,
			'usually-followed': 4,
			'sometimes-followed': 2,
			'rarely-followed': 1,
			'never-followed': 0
		},
		symptomImprovement: {
			'significant-improvement': 5,
			'moderate-improvement': 4,
			'slight-improvement': 2,
			'no-improvement': 1,
			'symptoms-worsened': 0
		},
		symptomFrequency: {
			'much-less-often': 5,
			'somewhat-less-often': 4,
			'about-same': 2,
			'somewhat-more-often': 1,
			'much-more-often': 0
		},
		additionalRelief: {
			'no-none': 5,
			'rarely': 4,
			'sometimes': 2,
			'frequently': 0
		},
		functionalBenefit: {
			'significantly-improved': 5,
			'somewhat-improved': 4,
			'no-change': 2,
			'somewhat-worsened': 1,
			'significantly-worsened': 0
		},
		sleepQuality: {
			'much-improved': 5,
			'somewhat-improved': 4,
			'no-change': 2,
			'somewhat-worse': 1,
			'much-worse': 0
		},
		tolerance: {
			'no-increase-needed': 5,
			'slight-increase': 4,
			'significant-increase': 2,
			'effect-decreased': 0
		},
		sideEffectSeverity: {
			'none-mild': 5,
			'mild': 4,
			'moderate': 2,
			'severe': 0
		},
		sideEffectTolerability: {
			'not-at-all': 5,
			'a-little': 4,
			'moderately': 2,
			'severely': 0
		},
		overallSatisfaction: {
			'very-satisfied': 5,
			'somewhat-satisfied': 4,
			'neutral': 2,
			'somewhat-dissatisfied': 1,
			'very-dissatisfied': 0
		},
		goalAchievement: {
			'completely-met': 5,
			'mostly-met': 4,
			'partially-met': 2,
			'not-met': 0
		},
		treatmentIntent: {
			'continue-current': 5,
			'continue-adjustments': 4,
			'unsure': 2,
			'stop-treatment': 0
		}
	};

	// Calculate score for individual question
	const calculateQuestionScore = (questionKey: keyof ExtendTPFormData, answer: string): number => {
		const questionScoring = scoringMap[questionKey];
		return questionScoring ? (questionScoring[answer] || 0) : 0;
	};

	// Calculate total score
	const calculateTotalScore = (data: ExtendTPFormData): number => {
		let totalScore = 0;
		Object.entries(data).forEach(([key, value]) => {
			if (value) {
				const questionKey = key as keyof ExtendTPFormData;
				totalScore += calculateQuestionScore(questionKey, value);
			}
		});
		return totalScore;
	};

	// Check eligibility (42+ points for 6-month plan)
	const checkEligibility = (score: number): boolean => {
		return score >= 42;
	};

	// Update scoring when form data changes
	useEffect(() => {
		const totalScore = calculateTotalScore(formData);
		const isEligible = checkEligibility(totalScore);
		setScoring(prev => ({
			...prev,
			totalScore,
			isEligible
		}));
	}, [formData]);

	// Validate current step
	const validateCurrentStep = (): boolean => {
		switch (step) {
			case 1: return !!(formData.adherence);
			case 2: return !!(formData.symptomImprovement);
			case 3: return !!(formData.symptomFrequency);
			case 4: return !!(formData.additionalRelief);
			case 5: return !!(formData.functionalBenefit);
			case 6: return !!(formData.sleepQuality);
			case 7: return !!(formData.tolerance);
			case 8: return !!(formData.sideEffectSeverity);
			case 9: return !!(formData.sideEffectTolerability);
			case 10: return !!(formData.overallSatisfaction);
			case 11: return !!(formData.goalAchievement);
			case 12: return !!(formData.treatmentIntent);
			default: return false;
		}
	};

	// Update canProceed when step or formData changes
	useEffect(() => {
		setCanProceed(validateCurrentStep());
	}, [step, formData]);

	// Fetch contact ID on component mount
	useEffect(() => {
		const fetchContactId = async () => {
			try {
				const token = localStorage.getItem("zenith_auth_token");
				if (token) {
					setContactId(token);
				}
			} catch (error) {
				console.error("Error fetching contact ID:", error);
			}
		};

		fetchContactId();
	}, []);

	// Handle step navigation
	const handleStepChange = async () => {
		if (!canProceed) {
			enqueueSnackbar("Please answer the question to continue", { variant: "warning" });
			return;
		}
		if (step < 12) {
			setStep(step + 1);
		} else {
			await submitQuestionnaire();
		}
	};

	// Question and answer text mapping for display purposes
	const getQuestionText = (questionKey: string): string => {
		const questionMap: Record<string, string> = {
			adherence: "During the past 3 months, how often did you use your medicinal cannabis exactly as prescribed (correct dose and timing)?",
			symptomImprovement: "How much has your primary symptom (the main condition you are treating, e.g. pain, anxiety, insomnia) improved since starting medicinal cannabis?",
			symptomFrequency: "How has the frequency or occurrence of your symptoms changed with treatment? (For example, how often you experience pain flare-ups, anxiety attacks, or sleepless nights now versus before.)",
			additionalRelief: "Did you need to use any extra treatments besides the prescribed cannabis to manage your condition (such as additional medications or extra cannabis doses)?",
			functionalBenefit: "How has the treatment affected your daily functioning or quality of life (ability to perform work, household tasks, exercise, socialize, etc.)?",
			sleepQuality: "How has your sleep quality or pattern been affected by the treatment?",
			tolerance: "Did you find that you needed to increase your cannabis dose over time to get the same symptom relief?",
			sideEffectSeverity: "Which statement best describes the side effects you experienced from the medicinal cannabis?",
			sideEffectTolerability: "How did any side effects impact your willingness to continue treatment?",
			overallSatisfaction: "Overall, how satisfied are you with the results of your medicinal cannabis treatment so far?",
			goalAchievement: "To what extent has this treatment met the goals or expectations you had when you started?",
			treatmentIntent: "What would you like to do going forward after this 3-month trial?"
		};

		return questionMap[questionKey] || questionKey;
	};

	const getAnswerText = (questionKey: string, answerValue: string): string => {
		const answerMap: Record<string, Record<string, string>> = {
			adherence: {
				"always-followed": "Always followed the prescribed schedule",
				"usually-followed": "Usually followed the schedule, with only a few minor misses or adjustments",
				"sometimes-followed": "Sometimes followed the schedule, with occasional missed or extra doses",
				"rarely-followed": "Rarely followed the prescribed schedule",
				"never-followed": "Never followed the prescribed regimen"
			},
			symptomImprovement: {
				"significant-improvement": "Significant improvement: symptoms have greatly reduced",
				"moderate-improvement": "Moderate improvement: noticeable improvement, but symptoms are still present",
				"slight-improvement": "Slight improvement: only a small improvement in symptoms",
				"no-improvement": "No improvement: no change in the symptoms",
				"symptoms-worsened": "Symptoms worsened: symptoms have become worse than before"
			},
			symptomFrequency: {
				"much-less-often": "Much less often than before: symptoms rarely occur now",
				"somewhat-less-often": "Somewhat less often than before: symptoms occur less frequently",
				"about-same": "About the same as before: no real change in how often symptoms occur",
				"somewhat-more-often": "Somewhat more often than before: symptoms occur a bit more frequently",
				"much-more-often": "Much more often than before: symptoms occur far more frequently"
			},
			additionalRelief: {
				"no-none": "No, none: cannabis alone was sufficient to manage my symptoms",
				"rarely": "Rarely: only on a few occasions I needed something additional",
				"sometimes": "Sometimes: I sometimes took another medication or extra dose for better relief",
				"frequently": "Frequently: I often needed other medications or extra doses to control my symptoms"
			},
			functionalBenefit: {
				"significantly-improved": "Significantly improved: I'm much more able to carry out daily activities than before",
				"somewhat-improved": "Somewhat improved: I can do more daily activities than before, though still with some difficulty",
				"no-change": "No change: my ability to perform daily tasks is about the same as before",
				"somewhat-worsened": "Somewhat worsened: day-to-day activities have become a bit more difficult",
				"significantly-worsened": "Significantly worsened: I'm much less able to function in daily life than before"
			},
			sleepQuality: {
				"much-improved": "Much improved: I am sleeping significantly better than before",
				"somewhat-improved": "Somewhat improved: I'm sleeping a bit better than before",
				"no-change": "No change: my sleep is neither better nor worse than before",
				"somewhat-worse": "Somewhat worse: I have experienced slightly worse sleep or new sleep issues",
				"much-worse": "Much worse: my sleep has significantly worsened since starting treatment"
			},
			tolerance: {
				"no-increase-needed": "No increase needed: I did not need to raise the dose; the same amount worked throughout",
				"slight-increase": "Yes, slight increase: I needed a small increase in dose/frequency to maintain relief",
				"significant-increase": "Yes, significant increase: I had to greatly increase the dose or use much more often for the same effect",
				"effect-decreased": "Yes, and effect decreased: even with higher doses, the medication felt less effective than before"
			},
			sideEffectSeverity: {
				"none-mild": "None or very mild side effects: I had no noticeable side effects",
				"mild": "Mild side effects: e.g. slight dry mouth or mild drowsiness that did not bother me",
				"moderate": "Moderate side effects: e.g. dizziness, increased appetite, or some anxiety – noticeable but manageable",
				"severe": "Severe side effects: e.g. very strong unwanted effects (such as severe dizziness, confusion, vomiting, etc.) that were hard to tolerate"
			},
			sideEffectTolerability: {
				"not-at-all": "Not at all: side effects did not affect my willingness to continue using the medicine",
				"a-little": "A little: side effects were somewhat bothersome but I felt I could manage and continue",
				"moderately": "Moderately: side effects made me question whether to continue at times",
				"severely": "Severely: side effects were intolerable – I felt I needed to stop treatment because of them"
			},
			overallSatisfaction: {
				"very-satisfied": "Very satisfied: extremely pleased with the treatment's results",
				"somewhat-satisfied": "Somewhat satisfied: mostly happy, with a few minor concerns",
				"neutral": "Neutral: neither satisfied nor dissatisfied with the results",
				"somewhat-dissatisfied": "Somewhat dissatisfied: somewhat unhappy; results fell short of expectations",
				"very-dissatisfied": "Very dissatisfied: very unhappy; treatment did not help as hoped"
			},
			goalAchievement: {
				"completely-met": "Completely met: it fully met or even exceeded my treatment goals",
				"mostly-met": "Mostly met: it met most of my main goals, with only slight shortfalls",
				"partially-met": "Partially met: it met some of my goals but not others",
				"not-met": "Not met: it did not meet my expectations or goals at all"
			},
			treatmentIntent: {
				"continue-current": "Continue the current treatment into a 6-month plan (I wish to keep using medicinal cannabis as is) – eligible for telehealth follow-up",
				"continue-adjustments": "Continue with adjustments (I want to continue, but perhaps with some changes in dose/strain) – eligible for telehealth follow-up",
				"unsure": "Unsure (I'm not certain if I should continue and would like to discuss it more)",
				"stop-treatment": "Stop the treatment (I prefer not to continue with medicinal cannabis)"
			}
		};

		return answerMap[questionKey]?.[answerValue] || answerValue;
	};

	// Submit questionnaire
	const submitQuestionnaire = async () => {
		setIsLoading(true);
		try {
			// Create questions and answers array for easy display (matching THC increase format)
			const questionsAndAnswers = Object.entries(formData)
				.filter(([_, value]) => value)
				.map(([questionKey, answerValue]) => ({
					questionKey,
					questionText: getQuestionText(questionKey),
					answerValue,
					answerText: getAnswerText(questionKey, answerValue),
					score: calculateQuestionScore(questionKey as keyof ExtendTPFormData, answerValue)
				}));

			// Prepare submission data
			const submissionData = {
				questionsAndAnswers,
				totalScore: scoring.totalScore,
				maxScore: scoring.maxScore,
				isEligible: scoring.isEligible,
				submittedAt: new Date().toISOString(),
			};

			await axios.post(
				`${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/extend-tp-questionnaire`,
				submissionData,
				{ withCredentials: true }
			);

			// Show results based on eligibility
			if (scoring.isEligible) {
				enqueueSnackbar("Congratulations! Your treatment plan extension is under review.", { variant: "success" });
			} else {
				enqueueSnackbar("Congratulations! Your treatment plan extension is under review.", { variant: "success" });
			}

			navigate({ to: "/patient/home" });
		} catch (error) {
			console.error('Error submitting ExtendTP questionnaire:', error);
			enqueueSnackbar("Failed to submit questionnaire. Please try again.", { variant: "error" });
		} finally {
			setIsLoading(false);
		}
	};

	// Step 1: Adherence
	const stepOne = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					During the past 3 months, how often did you use your medicinal cannabis exactly as prescribed (correct dose and timing)?
				</FormLabel>
				<RadioGroup
					value={formData.adherence}
					onChange={(e) => handleRadioChange('adherence', e.target.value)}
				>
					<StyledFormControlLabel value="always-followed" label="Always followed the prescribed schedule" />
					<StyledFormControlLabel value="usually-followed" label="Usually followed the schedule, with only a few minor misses or adjustments" />
					<StyledFormControlLabel value="sometimes-followed" label="Sometimes followed the schedule, with occasional missed or extra doses" />
					<StyledFormControlLabel value="rarely-followed" label="Rarely followed the prescribed schedule" />
					<StyledFormControlLabel value="never-followed" label="Never followed the prescribed regimen" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 2: Symptom Improvement
	const stepTwo = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					How much has your primary symptom (the main condition you are treating, e.g. pain, anxiety, insomnia) improved since starting medicinal cannabis?
				</FormLabel>
				<RadioGroup
					value={formData.symptomImprovement}
					onChange={(e) => handleRadioChange('symptomImprovement', e.target.value)}
				>
					<StyledFormControlLabel value="significant-improvement" label="Significant improvement: symptoms have greatly reduced" />
					<StyledFormControlLabel value="moderate-improvement" label="Moderate improvement: noticeable improvement, but symptoms are still present" />
					<StyledFormControlLabel value="slight-improvement" label="Slight improvement: only a small improvement in symptoms" />
					<StyledFormControlLabel value="no-improvement" label="No improvement: no change in the symptoms" />
					<StyledFormControlLabel value="symptoms-worsened" label="Symptoms worsened: symptoms have become worse than before" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 3: Symptom Frequency
	const stepThree = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					How has the frequency or occurrence of your symptoms changed with treatment? (For example, how often you experience pain flare-ups, anxiety attacks, or sleepless nights now versus before.)
				</FormLabel>
				<RadioGroup
					value={formData.symptomFrequency}
					onChange={(e) => handleRadioChange('symptomFrequency', e.target.value)}
				>
					<StyledFormControlLabel value="much-less-often" label="Much less often than before: symptoms rarely occur now" />
					<StyledFormControlLabel value="somewhat-less-often" label="Somewhat less often than before: symptoms occur less frequently" />
					<StyledFormControlLabel value="about-same" label="About the same as before: no real change in how often symptoms occur" />
					<StyledFormControlLabel value="somewhat-more-often" label="Somewhat more often than before: symptoms occur a bit more frequently" />
					<StyledFormControlLabel value="much-more-often" label="Much more often than before: symptoms occur far more frequently" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 4: Additional Relief
	const stepFour = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					Did you need to use any extra treatments besides the prescribed cannabis to manage your condition (such as additional medications or extra cannabis doses)?
				</FormLabel>
				<RadioGroup
					value={formData.additionalRelief}
					onChange={(e) => handleRadioChange('additionalRelief', e.target.value)}
				>
					<StyledFormControlLabel value="no-none" label="No, none: cannabis alone was sufficient to manage my symptoms" />
					<StyledFormControlLabel value="rarely" label="Rarely: only on a few occasions I needed something additional" />
					<StyledFormControlLabel value="sometimes" label="Sometimes: I sometimes took another medication or extra dose for better relief" />
					<StyledFormControlLabel value="frequently" label="Frequently: I often needed other medications or extra doses to control my symptoms" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 5: Functional Benefit
	const stepFive = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					How has the treatment affected your daily functioning or quality of life (ability to perform work, household tasks, exercise, socialize, etc.)?
				</FormLabel>
				<RadioGroup
					value={formData.functionalBenefit}
					onChange={(e) => handleRadioChange('functionalBenefit', e.target.value)}
				>
					<StyledFormControlLabel value="significantly-improved" label="Significantly improved: I'm much more able to carry out daily activities than before" />
					<StyledFormControlLabel value="somewhat-improved" label="Somewhat improved: I can do more daily activities than before, though still with some difficulty" />
					<StyledFormControlLabel value="no-change" label="No change: my ability to perform daily tasks is about the same as before" />
					<StyledFormControlLabel value="somewhat-worsened" label="Somewhat worsened: day-to-day activities have become a bit more difficult" />
					<StyledFormControlLabel value="significantly-worsened" label="Significantly worsened: I'm much less able to function in daily life than before" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 6: Sleep Quality
	const stepSix = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					How has your sleep quality or pattern been affected by the treatment?
				</FormLabel>
				<RadioGroup
					value={formData.sleepQuality}
					onChange={(e) => handleRadioChange('sleepQuality', e.target.value)}
				>
					<StyledFormControlLabel value="much-improved" label="Much improved: I am sleeping significantly better than before" />
					<StyledFormControlLabel value="somewhat-improved" label="Somewhat improved: I'm sleeping a bit better than before" />
					<StyledFormControlLabel value="no-change" label="No change: my sleep is neither better nor worse than before" />
					<StyledFormControlLabel value="somewhat-worse" label="Somewhat worse: I have experienced slightly worse sleep or new sleep issues" />
					<StyledFormControlLabel value="much-worse" label="Much worse: my sleep has significantly worsened since starting treatment" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 7: Tolerance
	const stepSeven = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					Did you find that you needed to increase your cannabis dose over time to get the same symptom relief?
				</FormLabel>
				<RadioGroup
					value={formData.tolerance}
					onChange={(e) => handleRadioChange('tolerance', e.target.value)}
				>
					<StyledFormControlLabel value="no-increase-needed" label="No increase needed: I did not need to raise the dose; the same amount worked throughout" />
					<StyledFormControlLabel value="slight-increase" label="Yes, slight increase: I needed a small increase in dose/frequency to maintain relief" />
					<StyledFormControlLabel value="significant-increase" label="Yes, significant increase: I had to greatly increase the dose or use much more often for the same effect" />
					<StyledFormControlLabel value="effect-decreased" label="Yes, and effect decreased: even with higher doses, the medication felt less effective than before" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 8: Side Effect Severity
	const stepEight = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					Which statement best describes the side effects you experienced from the medicinal cannabis?
				</FormLabel>
				<RadioGroup
					value={formData.sideEffectSeverity}
					onChange={(e) => handleRadioChange('sideEffectSeverity', e.target.value)}
				>
					<StyledFormControlLabel value="none-mild" label="None or very mild side effects: I had no noticeable side effects" />
					<StyledFormControlLabel value="mild" label="Mild side effects: e.g. slight dry mouth or mild drowsiness that did not bother me" />
					<StyledFormControlLabel value="moderate" label="Moderate side effects: e.g. dizziness, increased appetite, or some anxiety – noticeable but manageable" />
					<StyledFormControlLabel value="severe" label="Severe side effects: e.g. very strong unwanted effects (such as severe dizziness, confusion, vomiting, etc.) that were hard to tolerate" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 9: Side Effect Tolerability
	const stepNine = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					How did any side effects impact your willingness to continue treatment?
				</FormLabel>
				<RadioGroup
					value={formData.sideEffectTolerability}
					onChange={(e) => handleRadioChange('sideEffectTolerability', e.target.value)}
				>
					<StyledFormControlLabel value="not-at-all" label="Not at all: side effects did not affect my willingness to continue using the medicine" />
					<StyledFormControlLabel value="a-little" label="A little: side effects were somewhat bothersome but I felt I could manage and continue" />
					<StyledFormControlLabel value="moderately" label="Moderately: side effects made me question whether to continue at times" />
					<StyledFormControlLabel value="severely" label="Severely: side effects were intolerable – I felt I needed to stop treatment because of them" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 10: Overall Satisfaction
	const stepTen = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					Overall, how satisfied are you with the results of your medicinal cannabis treatment so far?
				</FormLabel>
				<RadioGroup
					value={formData.overallSatisfaction}
					onChange={(e) => handleRadioChange('overallSatisfaction', e.target.value)}
				>
					<StyledFormControlLabel value="very-satisfied" label="Very satisfied: extremely pleased with the treatment's results" />
					<StyledFormControlLabel value="somewhat-satisfied" label="Somewhat satisfied: mostly happy, with a few minor concerns" />
					<StyledFormControlLabel value="neutral" label="Neutral: neither satisfied nor dissatisfied with the results" />
					<StyledFormControlLabel value="somewhat-dissatisfied" label="Somewhat dissatisfied: somewhat unhappy; results fell short of expectations" />
					<StyledFormControlLabel value="very-dissatisfied" label="Very dissatisfied: very unhappy; treatment did not help as hoped" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 11: Goal Achievement
	const stepEleven = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					To what extent has this treatment met the goals or expectations you had when you started?
				</FormLabel>
				<RadioGroup
					value={formData.goalAchievement}
					onChange={(e) => handleRadioChange('goalAchievement', e.target.value)}
				>
					<StyledFormControlLabel value="completely-met" label="Completely met: it fully met or even exceeded my treatment goals" />
					<StyledFormControlLabel value="mostly-met" label="Mostly met: it met most of my main goals, with only slight shortfalls" />
					<StyledFormControlLabel value="partially-met" label="Partially met: it met some of my goals but not others" />
					<StyledFormControlLabel value="not-met" label="Not met: it did not meet my expectations or goals at all" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Step 12: Treatment Intent
	const stepTwelve = () => (
		<Stack sx={{ flexGrow: 1, px: 3, py: 2 }}>
			<FormControl>
				<FormLabel sx={{
					color: "black",
					fontWeight: "bold",
					mb: 3,
					fontSize: "18px",
					lineHeight: 1.4,
					display: 'block'
				}}>
					What would you like to do going forward after this 3-month trial?
				</FormLabel>
				<RadioGroup
					value={formData.treatmentIntent}
					onChange={(e) => handleRadioChange('treatmentIntent', e.target.value)}
				>
					<StyledFormControlLabel value="continue-current" label="Continue the current treatment into a 6-month plan (I wish to keep using medicinal cannabis as is) – eligible for telehealth follow-up" />
					<StyledFormControlLabel value="continue-adjustments" label="Continue with adjustments (I want to continue, but perhaps with some changes in dose/strain) – eligible for telehealth follow-up" />
					<StyledFormControlLabel value="unsure" label="Unsure (I'm not certain if I should continue and would like to discuss it more)" />
					<StyledFormControlLabel value="stop-treatment" label="Stop the treatment (I prefer not to continue with medicinal cannabis)" />
				</RadioGroup>
			</FormControl>
		</Stack>
	);

	// Render current step
	const renderStep = () => {
		switch (step) {
			case 1: return stepOne();
			case 2: return stepTwo();
			case 3: return stepThree();
			case 4: return stepFour();
			case 5: return stepFive();
			case 6: return stepSix();
			case 7: return stepSeven();
			case 8: return stepEight();
			case 9: return stepNine();
			case 10: return stepTen();
			case 11: return stepEleven();
			case 12: return stepTwelve();
			default: return stepOne();
		}
	};

	return (
		<ThemeProvider theme={zenithTheme}>
			<Box
				sx={{
					minHeight: "100vh",
					backgroundColor: "#f5f5f5",
					display: "flex",
					flexDirection: "column",
				}}
			>
				{/* Custom Header */}
				<Box sx={{ width: "100%" }}>
					<Banner />

					{/* Nav Bar */}
					<Box
						sx={{
							display: "flex",
							justifyContent: "space-between",
							alignItems: "center",
							padding: "0 20px",
							backgroundColor: "white",
							//paddingTop: { xs: "50px", sm: "58px" }, // Account for banner height + some spacing
							paddingBottom: "10px",
						}}
					>
						<IconButton aria-label="menu" sx={{ padding: "8px" }} onClick={toggleDrawer}>
							<MenuIcon />
						</IconButton>

						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{
								height: "19.14px",
							}}
						/>
					</Box>
				</Box>

				{/* Navigation Drawer */}
				<Drawer
					anchor="top"
					open={drawerOpen}
					onClose={toggleDrawer}
					keepMounted={true}
					sx={{
						"& .MuiDrawer-paper": {
							width: "100%",
							maxWidth: "100%",
							boxSizing: "border-box",
							padding: "20px 0 0 0",
							height: "auto",
							maxHeight: "500px",
							overflowY: "auto",
							borderBottom: "1px solid #e0e0e0",
							top: { xs: "40px", sm: "48px" }, // Push down by banner height
							zIndex: 1299, // Just below the banner
						},
					}}
				>
					{/* Drawer Header */}
					<Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 20px', marginBottom: '10px' }}>
						<CachedImage
							src="/zenith/zenith-logo.png"
							alt="Zenith Clinics"
							sx={{ height: '36px' }}
						/>
						<IconButton onClick={toggleDrawer}>
							<CloseIcon />
						</IconButton>
					</Box>

					{/* Divider */}
					<Box sx={{ height: "1px", backgroundColor: "#e0e0e0", margin: "10px 0" }} />

					{/* Navigation Links */}
					<Box sx={{ padding: '0 20px 20px' }}>
						<Button
							onClick={() => {
								toggleDrawer();
								navigate({ to: "/patient/home" });
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Back to Home
						</Button>
						<Button
							onClick={() => {
								toggleDrawer();
								navigateToShop();
							}}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none',
								borderBottom: '1px solid #f0f0f0'
							}}
						>
							Visit Private Shop
						</Button>
						<Button
							onClick={handleLogout}
							sx={{
								color: '#217F00',
								fontSize: '18px',
								fontWeight: 'bold',
								justifyContent: 'flex-start',
								padding: '15px 0',
								width: '100%',
								textTransform: 'none'
							}}
						>
							Logout
						</Button>
					</Box>
				</Drawer>

				{/* Main Content */}
				<Box sx={{ flex: 1, padding: "20px" }}>
					{/* Back Button */}
					<Box sx={{ marginBottom: 2, display: "flex", justifyContent: "flex-start", maxWidth: "800px", margin: "0 auto 16px auto", width: "100%" }}>
						<Button
							variant="outlined"
							startIcon={<ArrowBack />}
							onClick={() => navigate({ to: "/patient/home" })}
							sx={{
								color: "#217F00",
								borderColor: "#217F00",
								fontSize: "16px",
								fontWeight: "bold",
								textTransform: "none",
								padding: "8px 16px",
								"&:hover": {
									backgroundColor: "rgba(33, 127, 0, 0.04)",
									borderColor: "#217F00",
								},
							}}
						>
							Back to Home
						</Button>
					</Box>

					<Grid
						container
						direction="column"
						alignItems="center"
						justifyContent="flex-start"
						spacing={2}
					>
			<Grid size={12}>
				<Stack direction="row" alignItems="center" justifyContent="center">
					<Box
						sx={{
							width: "32px",
							borderRadius: "15px 0 0 15px",
							height: "11px",
							backgroundColor: "green",
						}}
					/>
					{[...Array(10)].map((_, index) => (
						<Box
							key={index}
							sx={{
								width: "32px",
								height: "11px",
								backgroundColor: step > index + 1 ? "green" : "#EAEAEA",
								border: "0.5px solid rgba(89, 89, 89, 0.61)",
							}}
						/>
					))}
					<Box
						sx={{
							width: "32px",
							borderRadius: "0 15px 15px 0",
							height: "11px",
							backgroundColor: step === 12 ? "green" : "#EAEAEA",
							border: "0.5px solid rgba(89, 89, 89, 0.61)",
						}}
					/>
				</Stack>
			</Grid>
			<Grid size={12}>
				<Stack>
					<Typography variant="h4" fontWeight={800}>
						Questionnaire:
					</Typography>
					<Typography variant="h5" fontWeight={700} color="green">
						Extend Treatment Plan
					</Typography>
				</Stack>
			</Grid>
			<Grid size={12}>{renderStep()}</Grid>
			<Grid size={9}>
				<Button
					fullWidth
					sx={{
						textTransform: "capitalize",
						borderRadius: "20px",
						opacity: (canProceed && !isLoading) ? 1 : 0.6
					}}
					variant="contained"
					color="success"
					disableElevation
					size="large"
					onClick={handleStepChange}
					disabled={!canProceed || isLoading}
				>
					{isLoading ? "Submitting..." : (step < 12 ? "Continue" : "Submit")}
				</Button>
				{!canProceed && !isLoading && (
					<Typography
						variant="body2"
						color="error"
						sx={{ mt: 1, textAlign: 'center' }}
					>
						Please answer the question to continue
					</Typography>
				)}
				{isLoading && (
					<Typography
						variant="body2"
						color="primary"
						sx={{ mt: 1, textAlign: 'center' }}
					>
						Submitting your questionnaire...
					</Typography>
				)}
				</Grid>
			</Grid>
			</Box>
		</Box>
	</ThemeProvider>
);
};

export default ExtendTP;