import React, { useState, useEffect, useMemo } from 'react';
import { Box, BoxProps } from '@mui/material';

interface CachedImageProps extends Omit<BoxProps, 'component'> {
  src: string;
  alt: string;
  preload?: boolean;
}

// Global cache for blob URLs to prevent re-fetching
const blobCache = new Map<string, string>();
const loadingPromises = new Map<string, Promise<string>>();

// Preload critical images immediately when this module loads
const preloadCriticalImages = () => {
  const criticalImages = ['/zenith/zenith-logo.png'];

  criticalImages.forEach(src => {
    if (!blobCache.has(src) && !loadingPromises.has(src)) {
      const loadPromise = fetch(src)
        .then(response => response.blob())
        .then(blob => {
          const url = URL.createObjectURL(blob);
          blobCache.set(src, url);
          return url;
        })
        .catch(error => {
          console.warn(`Failed to preload critical image: ${src}`, error);
          throw error; // Re-throw to maintain Promise<string> type
        });

      loadingPromises.set(src, loadPromise);
      loadPromise.finally(() => loadingPromises.delete(src));
    }
  });
};

// Preload immediately when module loads
preloadCriticalImages();

const CachedImage: React.FC<CachedImageProps> = ({
  src,
  alt,
  preload = true,
  sx,
  ...boxProps
}) => {
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Create a stable blob URL for this image
  const cachedBlobUrl = useMemo(() => {
    if (blobCache.has(src)) {
      return blobCache.get(src)!;
    }
    return null;
  }, [src]);

  useEffect(() => {
    // If we already have a cached blob URL, use it immediately
    if (cachedBlobUrl) {
      setBlobUrl(cachedBlobUrl);
      setIsLoading(false);
      return;
    }

    // If there's already a loading promise for this image, wait for it
    if (loadingPromises.has(src)) {
      loadingPromises.get(src)!
        .then((url) => {
          setBlobUrl(url);
          setIsLoading(false);
        })
        .catch(() => {
          setHasError(true);
          setIsLoading(false);
        });
      return;
    }

    // Create a new loading promise
    const loadPromise = fetch(src)
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.status}`);
        }
        return response.blob();
      })
      .then(blob => {
        const url = URL.createObjectURL(blob);
        blobCache.set(src, url);
        return url;
      });

    loadingPromises.set(src, loadPromise);

    loadPromise
      .then((url) => {
        setBlobUrl(url);
        setIsLoading(false);
        // Clean up the loading promise
        loadingPromises.delete(src);
      })
      .catch((error) => {
        console.error(`Failed to load image: ${src}`, error);
        setHasError(true);
        setIsLoading(false);
        loadingPromises.delete(src);
      });

  }, [src, cachedBlobUrl]);

  if (hasError) {
    // Fallback to original src if blob caching fails
    return (
      <Box
        component="img"
        src={src}
        alt={alt}
        sx={{
          ...sx,
        }}
        {...boxProps}
      />
    );
  }

  if (isLoading || !blobUrl) {
    // Return a placeholder or invisible element while loading
    return (
      <Box
        sx={{
          ...sx,
          opacity: 0,
        }}
        {...boxProps}
      />
    );
  }

  return (
    <Box
      component="img"
      src={blobUrl}
      alt={alt}
      sx={{
        ...sx,
      }}
      {...boxProps}
    />
  );
};

export default CachedImage;
