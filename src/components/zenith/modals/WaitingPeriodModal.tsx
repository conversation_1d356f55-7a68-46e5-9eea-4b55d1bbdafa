import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  Typography,
  Box,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { calculateRemainingTime, formatRemainingTime } from '../../../utils/waitingPeriod';

interface WaitingPeriodModalProps {
  open: boolean;
  onClose: () => void;
  treatmentPlanStartDate: string;
}

interface CountdownTimerProps {
  remainingTime: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ remainingTime }) => {
  if (!remainingTime) {
    return null;
  }

  const { days, hours, minutes } = remainingTime;

  return (
    <Box
      sx={{
        backgroundColor: '#217F00',
        borderRadius: '16px',
        padding: '24px',
        margin: '24px 0 16px 0',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '16px',
      }}
    >
      {/* Countdown Numbers */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '32px',
          width: '100%',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'white' }}>
          <Typography
            sx={{
              fontSize: '48px',
              fontWeight: 'bold',
              lineHeight: 1,
              marginBottom: '4px',
            }}
          >
            {days}
          </Typography>
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.9,
            }}
          >
            {days === 1 ? 'Day' : 'Days'}
          </Typography>
        </Box>

        <Box sx={{ textAlign: 'center', color: 'white' }}>
          <Typography
            sx={{
              fontSize: '48px',
              fontWeight: 'bold',
              lineHeight: 1,
              marginBottom: '4px',
            }}
          >
            {hours.toString().padStart(2, '0')}
          </Typography>
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.9,
            }}
          >
            Hours
          </Typography>
        </Box>

        <Box sx={{ textAlign: 'center', color: 'white' }}>
          <Typography
            sx={{
              fontSize: '48px',
              fontWeight: 'bold',
              lineHeight: 1,
              marginBottom: '4px',
            }}
          >
            {minutes.toString().padStart(2, '0')}
          </Typography>
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.9,
            }}
          >
            Minutes
          </Typography>
        </Box>
      </Box>

      {/* Divider Line */}
      <Box
        sx={{
          width: '100%',
          height: '1px',
          backgroundColor: 'rgba(255, 255, 255, 0.3)',
          margin: '8px 0',
        }}
      />

      {/* Time Remaining Label */}
      <Typography
        sx={{
          fontSize: '16px',
          fontWeight: 'bold',
          color: 'white',
          textAlign: 'center',
        }}
      >
        Time Remaining
      </Typography>
    </Box>
  );
};

const WaitingPeriodModal: React.FC<WaitingPeriodModalProps> = ({
  open,
  onClose,
  treatmentPlanStartDate,
}) => {
  const [remainingTime, setRemainingTime] = useState<{
    days: number;
    hours: number;
    minutes: number;
  } | null>(null);

  // Update countdown every minute
  useEffect(() => {
    if (!open || !treatmentPlanStartDate) {
      return;
    }

    const updateCountdown = () => {
      const remaining = calculateRemainingTime(treatmentPlanStartDate);
      setRemainingTime(remaining);

      // If waiting period has ended, close the modal
      if (!remaining) {
        onClose();
      }
    };

    // Initial update
    updateCountdown();

    // Set up interval to update every minute
    const interval = setInterval(updateCountdown, 60000);

    return () => clearInterval(interval);
  }, [open, treatmentPlanStartDate, onClose]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '20px',
          margin: '16px',
          maxWidth: '420px',
          border: '3px solid #007BFF',
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
        },
      }}
    >
      <DialogContent
        sx={{
          padding: '32px 24px 24px 24px',
          position: 'relative',
          textAlign: 'center',
        }}
      >
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: '12px',
            right: '12px',
            color: '#666',
            padding: '4px',
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>

        {/* Title */}
        <Typography
          sx={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '8px',
            paddingRight: '32px', // Account for close button
          }}
        >
          Waiting Period for
        </Typography>

        <Typography
          sx={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#217F00',
            marginBottom: '16px',
          }}
        >
          29% THC Increase Request
        </Typography>

        {/* Explanation Text */}
        <Typography
          sx={{
            fontSize: '16px',
            color: '#333',
            marginBottom: '8px',
            lineHeight: 1.5,
            textAlign: 'center',
          }}
        >
          You're currently in a <strong>one-month</strong> waiting period before you can request a higher strength.
        </Typography>

        {/* Countdown Timer */}
        <CountdownTimer remainingTime={remainingTime} />
      </DialogContent>
    </Dialog>
  );
};

export default WaitingPeriodModal;
