import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  Typography,
  Box,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { calculateExtendTPRemainingTime } from '../../../utils/waitingPeriod';

interface ExtendTPWaitingPeriodModalProps {
  open: boolean;
  onClose: () => void;
  treatmentPlanEndDate: string;
}

interface CountdownTimerProps {
  remainingTime: {
    days: number;
    hours: number;
    minutes: number;
  } | null;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ remainingTime }) => {
  if (!remainingTime) {
    return null;
  }

  const { days, hours, minutes } = remainingTime;

  return (
    <Box
      sx={{
        backgroundColor: '#217F00',
        borderRadius: '16px',
        padding: '24px',
        margin: '24px 0 16px 0',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: '16px',
      }}
    >
      {/* Countdown Numbers */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          gap: '32px',
          width: '100%',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'white' }}>
          <Typography
            sx={{
              fontSize: '48px',
              fontWeight: 'bold',
              lineHeight: 1,
              marginBottom: '4px',
            }}
          >
            {days}
          </Typography>
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.9,
            }}
          >
            {days === 1 ? 'Day' : 'Days'}
          </Typography>
        </Box>

        <Box sx={{ textAlign: 'center', color: 'white' }}>
          <Typography
            sx={{
              fontSize: '48px',
              fontWeight: 'bold',
              lineHeight: 1,
              marginBottom: '4px',
            }}
          >
            {hours.toString().padStart(2, '0')}
          </Typography>
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.9,
            }}
          >
            Hours
          </Typography>
        </Box>

        <Box sx={{ textAlign: 'center', color: 'white' }}>
          <Typography
            sx={{
              fontSize: '48px',
              fontWeight: 'bold',
              lineHeight: 1,
              marginBottom: '4px',
            }}
          >
            {minutes.toString().padStart(2, '0')}
          </Typography>
          <Typography
            sx={{
              fontSize: '14px',
              fontWeight: 'normal',
              opacity: 0.9,
            }}
          >
            Minutes
          </Typography>
        </Box>
      </Box>

      {/* Divider Line */}
      <Box
        sx={{
          width: '100%',
          height: '1px',
          backgroundColor: 'rgba(255, 255, 255, 0.3)',
          margin: '8px 0',
        }}
      />

      {/* Time Remaining Label */}
      <Typography
        sx={{
          fontSize: '16px',
          fontWeight: 'bold',
          color: 'white',
          textAlign: 'center',
        }}
      >
        Time Remaining
      </Typography>
    </Box>
  );
};

const ExtendTPWaitingPeriodModal: React.FC<ExtendTPWaitingPeriodModalProps> = ({
  open,
  onClose,
  treatmentPlanEndDate,
}) => {
  const [remainingTime, setRemainingTime] = useState<{
    days: number;
    hours: number;
    minutes: number;
  } | null>(null);

  // Update countdown every minute
  useEffect(() => {
    if (!open || !treatmentPlanEndDate) {
      return;
    }

    const updateCountdown = () => {
      const remaining = calculateExtendTPRemainingTime(treatmentPlanEndDate);
      setRemainingTime(remaining);

      // If waiting period has ended, close the modal
      if (!remaining) {
        onClose();
      }
    };

    // Initial update
    updateCountdown();

    // Set up interval to update every minute
    const interval = setInterval(updateCountdown, 60000);

    return () => clearInterval(interval);
  }, [open, treatmentPlanEndDate, onClose]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '20px',
          margin: '16px',
          maxWidth: '420px',
          border: '3px solid #217F00',
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
        },
      }}
    >
      <DialogContent
        sx={{
          padding: '32px 24px 24px 24px',
          position: 'relative',
          textAlign: 'center',
        }}
      >
        {/* Close Button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            top: '12px',
            right: '12px',
            color: '#666',
            padding: '4px',
          }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>

        {/* Title */}
        <Typography
          sx={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#333',
            marginBottom: '8px',
            paddingRight: '32px', // Account for close button
          }}
        >
          Waiting Period for
        </Typography>

        <Typography
          sx={{
            fontSize: '20px',
            fontWeight: 'bold',
            color: '#217F00',
            marginBottom: '16px',
          }}
        >
          Treatment Plan Extension
        </Typography>

        {/* Explanation Text */}
        <Typography
          sx={{
            fontSize: '16px',
            color: '#333',
            marginBottom: '8px',
            lineHeight: 1.5,
            textAlign: 'center',
          }}
        >
          Your current treatment plan is still active. You can request an extension starting <strong>14 days before</strong> your plan expires.
        </Typography>

        {/* Countdown Timer */}
        <CountdownTimer remainingTime={remainingTime} />
      </DialogContent>
    </Dialog>
  );
};

export default ExtendTPWaitingPeriodModal;
