import * as React from "react";
// import '../../../styles/zenith/main.css';
import Grid from "@mui/material/Grid2";
import { Divider, Typography } from "@mui/material";


function Footer() {
    return (
        <footer className="zenith-footer">
            <center>
                <Grid container sx={{ width: '100%', mt: 5, p: 2 }} direction={'column'}>
                    <Divider style={{ backgroundColor: 'black', width: '100%' }} />
                    <Typography sx={{ fontSize: '12px', mt: 2 }}>
                        Provided by  <span style={{ fontWeight: '600', color: 'green' }}>ZenithClinics</span> Pty Ltd
                    </Typography>
                </Grid>
            </center>
        </footer>
    )
}

export default Footer;