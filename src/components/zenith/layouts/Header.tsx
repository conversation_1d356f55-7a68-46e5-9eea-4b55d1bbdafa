// import '../../../styles/zenith/main.css';
import { AppBar, Toolbar, Typography, Button, IconButton, Menu, MenuItem, useMediaQuery, useTheme } from "@mui/material";
import { Menu as MenuIcon } from "@mui/icons-material";
import Grid from "@mui/material/Grid2";
import { useLocation, useNavigate } from "@tanstack/react-location";
import { useSnackbar } from "notistack";
import { useState, useEffect } from "react";
import { useFlow } from "../../../hooks/flow-controller";
import axiosInstance from "../../../services/axios";

function Header() {

    const location = useLocation();
    const navigate = useNavigate();
    const { enqueueSnackbar } = useSnackbar();
    const { user } = useFlow();
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [contactId, setContactId] = useState<string | null>(null);

    // Show navigation actions only on authenticated pages (profile and chat)
    const showNavActions = location.current.pathname === "/patient/profile" ||
                          location.current.pathname === "/patient/chat";

    // Fetch contactId for navigation actions
    useEffect(() => {
        const fetchContactId = async () => {
            if (!showNavActions || !user?.email || contactId) return;

            try {
                const response = await axiosInstance.get(
                    `/zoho/v1.0/treatment-plan/by-email?email=${encodeURIComponent(user.email)}`
                );
                if (response.data?.contactId) {
                    setContactId(response.data.contactId);
                }
            } catch (error) {
                console.error("Error fetching contact ID:", error);
            }
        };

        fetchContactId();
    }, [showNavActions, user?.email, contactId]);

    const handleLogout = () => {
        // Clear authentication data
        localStorage.removeItem('zenith_auth_user');
        localStorage.removeItem('zenith_authenticated');


        // Redirect to login page
        navigate({ to: "/patient/login" });

        // Close menu if open
        setAnchorEl(null);
    };

    const handleChatNavigation = () => {
        if (contactId) {
            navigate({ to: `/patient/chat?token=${encodeURIComponent(contactId)}` });
        } else {
            enqueueSnackbar("Chat not available. Please contact support.", { variant: "error" });
        }
        setAnchorEl(null);
    };

    const handleShopNavigation = () => {
        if (user?.email && contactId) {
            const safeContactId = contactId.startsWith('p') ? contactId : `p${contactId}`;
            const shopUrl = `https://letsroll.harvest.delivery/members-shop?email=${encodeURIComponent(user.email)}&contact=${encodeURIComponent(safeContactId)}`;
            window.location.href = shopUrl;
        } else {
            enqueueSnackbar("Shop access not available. Please contact support.", { variant: "error" });
        }
        setAnchorEl(null);
    };

    const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
    };

    return (
        <AppBar position="fixed" elevation={0} sx={{
            backgroundColor: 'green',
            zIndex: 1
        }}>
            <Toolbar>
                <Grid container sx={{ width: '100%' }} justifyContent={'center'} alignItems={'center'}>
                    <Typography sx={{ fontSize: isMobile ? '28px' : '38px', cursor: "pointer" }}
            onClick={(e) =>
              (window.location.href = import.meta.env.VITE_MAIN_ZENITH_URL)
            }>
                        <span style={{ fontWeight: '600' }}>Zenith</span>Clinics
                    </Typography>

                    {showNavActions && (
                        <>
                            {isMobile ? (
                                // Mobile: Hamburger menu with all actions
                                <>
                                    <IconButton
                                        onClick={handleMenuOpen}
                                        sx={{
                                            position: 'absolute',
                                            right: '20px',
                                            color: 'white',
                                        }}
                                    >
                                        <MenuIcon />
                                    </IconButton>
                                    <Menu
                                        anchorEl={anchorEl}
                                        open={Boolean(anchorEl)}
                                        onClose={handleMenuClose}
                                        anchorOrigin={{
                                            vertical: 'bottom',
                                            horizontal: 'right',
                                        }}
                                        transformOrigin={{
                                            vertical: 'top',
                                            horizontal: 'right',
                                        }}
                                    >
                                        {contactId && (
                                            <MenuItem onClick={handleChatNavigation}>
                                                Get Treatment Support
                                            </MenuItem>
                                        )}
                                        {contactId && (
                                            <MenuItem onClick={handleShopNavigation}>
                                                Order Your Products
                                            </MenuItem>
                                        )}
                                        <MenuItem onClick={handleLogout}>
                                            Logout
                                        </MenuItem>
                                    </Menu>
                                </>
                            ) : (
                                // Desktop: Action buttons + logout
                                <div style={{ position: 'absolute', right: '20px', display: 'flex', gap: '10px', alignItems: 'center' }}>
                                    {contactId && (
                                        <Button
                                            onClick={handleChatNavigation}
                                            sx={{
                                                color: 'white',
                                                fontSize: '14px',
                                                fontWeight: 'bold',
                                                textTransform: 'none',
                                                '&:hover': {
                                                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                },
                                            }}
                                        >
                                            Get Treatment Support
                                        </Button>
                                    )}
                                    {contactId && (
                                        <Button
                                            onClick={handleShopNavigation}
                                            sx={{
                                                color: 'white',
                                                fontSize: '14px',
                                                fontWeight: 'bold',
                                                textTransform: 'none',
                                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                                '&:hover': {
                                                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                                                },
                                            }}
                                        >
                                            Order Products
                                        </Button>
                                    )}
                                    <Button
                                        onClick={handleLogout}
                                        sx={{
                                            color: 'white',
                                            fontSize: '14px',
                                            fontWeight: 'bold',
                                            textTransform: 'none',
                                            '&:hover': {
                                                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                                            },
                                        }}
                                    >
                                        Logout
                                    </Button>
                                </div>
                            )}
                        </>
                    )}
                </Grid>
            </Toolbar>
        </AppBar>
    )
}

export default Header;
