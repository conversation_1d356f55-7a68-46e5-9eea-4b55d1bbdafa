import { Box, Typography } from '@mui/material';
import Slider from 'react-slick';
import type { Settings } from 'react-slick';
import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';

const messages = [
  "No Referral Required",
  "$0 Consultation",
  "Delivered Direct to Your Door",
  "Quick & Easy Pre-Screening",
  "Patient First Mindset",
  "Treatment Plans to Suit Your Needs",
  "Alternative Medicine Direct from Canada",
  "Ahpra-Registered Doctors",
  "Telehealth Consultations"
];

const Banner = () => {
  const settings: Settings = {
    dots: false,
    infinite: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    speed: 1000,
    fade: true,
    arrows: false,
    pauseOnHover: false,
    cssEase: 'linear'
  };

  return (
    <Box
      sx={{
        width: '100%',
        bgcolor: 'primary.main',
        color: 'white',
        py: { xs: 0.75, sm: 1 },
        textAlign: 'center',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1300,
        overflow: 'hidden',
        height: { xs: '40px', sm: '48px' },
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Box 
        sx={{
          width: '100%',
          '.slick-slider': {
            '.slick-slide': {
              opacity: 0,
              transition: 'opacity 0.5s ease-in-out',
            },
            '.slick-current': {
              opacity: 1,
            }
          }
        }}
      >
        {/* @ts-ignore */}
        <Slider {...settings}>
          {messages.map((message, index) => (
            <Typography
              key={index}
              variant="h6"
              sx={{
                fontWeight: 500,
                fontSize: { xs: '0.875rem', sm: '1rem', md: '1.25rem' },
                textAlign: 'center',
                width: '100%',
                mx: 'auto',
                px: 2,
                lineHeight: 1.2,
              }}
            >
              {message}
            </Typography>
          ))}
        </Slider>
      </Box>
    </Box>
  );
};

export default Banner; 