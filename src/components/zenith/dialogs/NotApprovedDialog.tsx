import {
  Button,
  Dialog,
  DialogContent,
  Divider,
  Fab,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import styles from "../../../css/confirmation.module.css";

export default function NotApprovedDialog({ openDialog, setOpenDialog }: any) {
  return (
    <Dialog
      open={openDialog}
      fullWidth={true}
      maxWidth={"xs"}
      disableEscapeKeyDown
      onClose={() => {}}
    >
      <DialogContent>
        <Grid
          container
          direction={"column"}
          sx={{ width: "100%" }}
          justifyContent={"center"}
          alignItems={"center"}
          textAlign={"center"}
        >
          <Typography
            sx={{
              fontSize: "24px",
              fontWeight: "bold",
              lineHeight: "1em",
            }}
          >
            You Have Not Been Approved
          </Typography>
          <img className={styles.confirmImg} src="/zenith/cancel.png" />
          <Grid
            sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
            textAlign={"start"}
            container
            direction={"column"}
            alignItems={"start"}
          >
            <Typography
              sx={{
                fontSize: "16px",
                lineHeight: "1em",
                paddingBottom: "10px",
              }}
            >
              Based on your responses, you do not currently meet the eligibility
              criteria for a free doctor consultation. <br /> <br />
              We appreciate your time and interest in our clinic, and we wish
              you the very best on your health journey.
              <br />
              <br />
            </Typography>
            <Typography
              sx={{
                fontSize: "14px",
                lineHeight: "1em",
              }}
            ></Typography>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              sx={{ mt: 2 }}
              onClick={() => {
                setOpenDialog(false);
                window.location.href = import.meta.env.VITE_MAIN_ZENITH_URL;
              }}
            >
              Back To Homepage
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
}
