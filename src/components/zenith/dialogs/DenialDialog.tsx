import {
  But<PERSON>,
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Typography,
  IconButton,
} from "@mui/material";
import Grid from "@mui/material/Grid2";

interface DenialDialogProps {
  open: boolean;
  onClose: () => void;
  type: 'thc' | 'extend' | 'add22thc';
}

export default function DenialDialog({ open, onClose, type }: DenialDialogProps) {

  const getTitleParts = () => {
    if (type === 'thc') {
      return {
        main: 'Not Approved For',
        highlight: '29% THC Increase Request'
      };
    } else if (type === 'extend') {
      return {
        main: 'Not Approved For',
        highlight: 'Treatment Plan Extension'
      };
    } else {
      return {
        main: 'Not Approved For',
        highlight: '22% THC Addition Request'
      };
    }
  };

  const getMessage = () => {
    if (type === 'thc') {
      return 'Your request to increase your prescribed THC level to 29% has not been approved at this time.';
    } else if (type === 'extend') {
      return 'Your request to extend your treatment plan was not approved at this time.';
    } else {
      return 'Your request to add 22% THC as an additional option has not been approved at this time.';
    }
  };

  const getExplanation = () => {
    if (type === 'thc') {
      return 'Our doctors follow clinical best practices, which means they may recommend more time for your body to adjust before approving higher potency levels.';
    } else if (type === 'extend') {
      return 'Our clinical team carefully reviews each request to ensure it aligns with best medical practice and your individual health needs.';
    } else {
      return 'Our clinical team evaluates each request to add additional THC options based on your current treatment response and individual health profile.';
    }
  };

  const getSupportMessage = () => {
    if (type === 'thc') {
      return (
        <>
          If you'd like more information or to better understand your treatment plan, <strong>please reach out to our care team</strong> at <EMAIL>.
        </>
      );
    } else if (type === 'extend') {
      return (
        <>
          If you have questions or would like to discuss your options, our care team is here to help, <strong>please reach out</strong> at <EMAIL>.
        </>
      );
    } else {
      return (
        <>
          If you have questions about adding additional THC options or would like to discuss alternative approaches, <strong>please reach out to our care team</strong> at <EMAIL>.
        </>
      );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth={true}
      maxWidth="sm"
      slotProps={{
        paper: {
          sx: {
            borderRadius: '12px',
            margin: '16px',
            maxHeight: 'calc(100vh - 32px)',
          }
        }
      }}
    >
      <DialogContent sx={{ padding: '32px 24px', position: 'relative' }}>
        {/* Close button */}
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 16,
            top: 16,
            color: '#333',
            fontSize: '24px',
            fontWeight: 'bold',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.04)',
            }
          }}
        >
          <Typography sx={{ fontSize: '20px', fontWeight: 'bold' }}>×</Typography>
        </IconButton>

        <Grid
          container
          direction="column"
          spacing={0}
          sx={{ width: "100%", maxWidth: "400px", margin: "0 auto" }}
          alignItems="center"
          textAlign="center"
        >
          {/* Title */}
          <Grid>
            <Typography
              variant="h5"
              sx={{
                fontSize: "28px",
                fontWeight: "700",
                color: "#333",
                marginBottom: "16px",
                textAlign: "center",
                lineHeight: "1.2",
              }}
            >
              {getTitleParts().main}
              <br />
              <Typography
                component="span"
                sx={{
                  fontSize: "28px",
                  fontWeight: "700",
                  color: "#e74c3c",
                }}
              >
                {getTitleParts().highlight}
              </Typography>
            </Typography>
          </Grid>

          {/* Main message */}
          <Grid>
            <Typography
              sx={{
                fontSize: "16px",
                color: "#333",
                lineHeight: "1.5",
                marginBottom: "24px",
                textAlign: "center",
                fontWeight: "400",
              }}
            >
              {getMessage()}
            </Typography>
          </Grid>

          {/* Explanation */}
          <Grid>
            <Typography
              sx={{
                fontSize: "16px",
                color: "#333",
                lineHeight: "1.6",
                marginBottom: "24px",
                textAlign: "center",
                fontWeight: "400",
              }}
            >
              {getExplanation()}
            </Typography>
          </Grid>

          {/* Support message */}
          <Grid>
            <Typography
              sx={{
                fontSize: "16px",
                color: "#333",
                lineHeight: "1.6",
                marginBottom: "32px",
                textAlign: "center",
                fontWeight: "400",
              }}
            >
              {getSupportMessage()}
            </Typography>
          </Grid>

          {/* Action button */}
        
        </Grid>
      </DialogContent>
    </Dialog>
  );
}
