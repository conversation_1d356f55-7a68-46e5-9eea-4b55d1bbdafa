import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  ThemeProvider,
  Typography,
  useMediaQuery,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import zenithTheme from "../../../styles/zenith/theme";
import styles from "../../../css/confirmation.module.css";

export default function DontOfferDialog({
  openDialog,
  setOpenDialog,
  onClickAction,
}: any) {
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("xs"));
  const liStyle = {
    padding: "5px 20px",
    margin: "10px 0px",
    fontSize: "16px",
    color: "white",
    backgroundColor: "#FF7171",
    borderRadius: "6px",
  };
  return (
    <ThemeProvider theme={zenithTheme}>
      <Dialog
        open={openDialog}
        fullWidth={true}
        maxWidth={"xs"}
        disableEscapeKeyDown
      >
        <DialogContent sx={{ paddingX: "30px", maxHeight:"550px" }}>
          <Grid
            container
            direction={"column"}
            sx={{ width: "100%" }}
            justifyContent={"center"}
            alignItems={"center"}
            textAlign={"center"}
          >
            <Typography
              sx={{
                fontSize: "35px",
                fontWeight: "700",
                lineHeight: "100%",
              }}
            >
              <span style={{ color: "green" }}>What We </span> <br />
              Don't Offer
            </Typography>
          </Grid>
          <Grid sx={{ marginTop: "20px" }}>
            <ul style={{ listStylePosition: "inside", padding: 0 }}>
              <li style={liStyle}>We do not offer oils</li>
              <li style={liStyle}>We do not offer edibles</li>
              <li style={liStyle}>We do not provide e-scripts</li>
            </ul>
          </Grid>
          <Typography
            sx={{
              fontSize: "16px",
              fontWeight: "400",
            }}
          >
            At Zenith Clinics, we focus on what we do best : Providing eligible
            patients with <b>natural alternatives</b>, prescribed and supplied to you by your Doctor.
          </Typography>
          <Grid
            sx={{ width: "100%" }}
            textAlign={"center"}
            container
            direction={"row"}
            alignItems={"center"}
            justifyContent={"space-evenly"}
          >
            <Grid
              textAlign={"center"}
              width={"80%"}
              container
              sx={{mt:2}}
              direction={"row"}
              alignItems={"center"}
              justifyContent={"space-between"}
            >
              <Button
                type="submit"
                variant="contained"
                sx={{ mt: 2, textTransform: "none" }}
                onClick={onClickAction}
              >
                I Understand
              </Button>
              <Button
                type="submit"
                variant="outlined"
                sx={{ mt: 2, textTransform: "none" }}
                onClick={() => setOpenDialog(false)}
              >
                Go Back
              </Button>
            </Grid>
          </Grid>
          <Grid
            sx={{ width: "100%", fontSize: "14px", paddingTop: "50px" }}
            textAlign={"start"}
            container
            direction={"column"}
            alignItems={"start"}
          >
            <span>
              <i>
                Any information you provide today is confidential and compliant
                with the Medical Board of Australia Good Medical Practice code,
                RACGP Standards of General Practice and our Medical
                Confidentially Duty of Conduct for doctors in Australia, which
                means we protect your privacy and right to confidentiality
              </i>
            </span>
          </Grid>
        </DialogContent>
      </Dialog>
    </ThemeProvider>
  );
}
