import {
  <PERSON>ton,
  <PERSON>alog,
  DialogContent,
  Divider,
  Fab,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  TextField,
  Typography,
  useMediaQuery,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import zenithTheme from "../../../styles/zenith/theme";
import styles from "../../../css/confirmation.module.css";
import LoadingScreen from "../../../utils/loading-screen";
import { useState } from "react";
import { enqueueSnackbar } from "notistack";
import axios from "axios";
import axiosInstance from "../../../services/axios";

export default function CancelConfirmationDialog({
  openDialog,
  setOpenDialog,
  leadId,
}: any) {
  const [isLoading, setIsLoading] = useState(false);
  const isDesktopOrMobile = useMediaQuery(zenithTheme.breakpoints.up("sm"));
  async function handleSubmitConfirm(e: any) {
    try {
      e.preventDefault();
      setIsLoading(true);
      await updateZohoLeadCancelBooking(leadId);
      enqueueSnackbar("You have cancelled your appointment with the doctor", {
        variant: "success",
      });
      window.location.href =
        import.meta.env.VITE_MAIN_ZENITH_URL;
      setIsLoading(false);
    } catch (e: any) {
      enqueueSnackbar("An error occured", {
        variant: "error",
      });
    } finally {
      setIsLoading(false);
    }
  }
  const updateZohoLeadCancelBooking = async (leadId : string) => {
    
    await axiosInstance.post(
      `${
        import.meta.env.VITE_API_URL
      }/funnel/v1.0/patient/updateZohoLeadCancelBooking`,
      {
        leadId: leadId,
      }
    );
  };
  return (
    <>
      {isLoading && <LoadingScreen />}
      <Dialog
        open={openDialog}
        fullWidth={true}
        maxWidth={"xs"}
        disableEscapeKeyDown
      >
        <DialogContent>
          <Grid
            container
            direction={"column"}
            sx={{ width: "100%" }}
            justifyContent={"center"}
            alignItems={"center"}
            textAlign={"center"}
          >
            <Typography
              sx={{
                fontSize: "16px",
                fontWeight: "bold",
                paddingBottom: "10px",
                lineHeight: "1.5em",
              }}
            >
              Confirm Cancellation <br />
              <span style={{ color: "green" }}>Are you sure ?</span>
            </Typography>
            <img
              className={styles.confirmImg}
              src="/zenith/confirm-cancel.png"
            />
            <Grid
              sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
              textAlign={"start"}
              container
              direction={"column"}
              alignItems={"start"}
            >
              <Typography
                sx={{
                  fontSize: "16px",
                  lineHeight: "1em",
                  margin: "20px 0px",
                }}
              >
                Are you sure you want to cancel your appointment with Dr. H ?
              </Typography>
              <Grid
                sx={{ width: "100%", paddingBottom: "20px" }}
                textAlign={"center"}
                container
                direction={"row"}
                alignItems={"center"}
                justifyContent={"space-evenly"}
              >
                <Grid
                  sx={{ width: isDesktopOrMobile ? "40%" : "80%" }}
                  textAlign={"center"}
                  container
                  direction={"row"}
                  alignItems={"center"}
                  justifyContent={"space-evenly"}
                >
                  <Button
                    type="submit"
                    variant="contained"
                    sx={{ mt: 2 }}
                    onClick={() => {setOpenDialog(false)}}
                  >
                    No
                  </Button>
                  <Button
                    type="submit"
                    variant="outlined"
                    sx={{ mt: 2 }}
                    onClick={(e) => handleSubmitConfirm(e)}
                  >
                    Yes
                  </Button>
                </Grid>
              </Grid>
              <Typography
                sx={{
                  fontSize: "14px",
                  lineHeight: "1em",
                }}
              >
                <i>
                  Any information you provide today is confidential and
                  compliant with the Medical Board of Australia Good Medical
                  Practice code, RACGP Standards of General Practice and our
                  Medical Confidentially Duty of Conduct for doctors in
                  Australia, which means we protect your privacy and right to
                  confidentiality
                </i>
              </Typography>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
    </>
  );
}
