import {
	<PERSON>ton,
	Dialog,
	DialogContent,
	Divider,
	Fab,
	FormControl,
	FormControlLabel,
	FormHelperText,
	Radio,
	RadioGroup,
	TextField,
	Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import styles from "../../../css/confirmation.module.css";

export default function PendingReviewDialog({ openDialog, setOpenDialog }: any) {
	return (
		<Dialog open={openDialog} fullWidth={true} maxWidth={"xs"} disableEscapeKeyDown onClose={() => {}}>
			<DialogContent>
				<Grid
					container
					direction={"column"}
					sx={{ width: "100%" }}
					justifyContent={"center"}
					alignItems={"center"}
					textAlign={"center"}
				>
					<Typography
						sx={{
							fontSize: "24px",
							fontWeight: "bold",
							lineHeight: "1em",
						}}
					>
						Pending Review
					</Typography>
					<img className={styles.pendingImg} src="/zenith/pending.png" />
					<Grid
						sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
						textAlign={"start"}
						container
						direction={"column"}
						alignItems={"start"}
					>
						<Typography
							sx={{
								fontSize: "16px",
								lineHeight: "1em",
								paddingBottom: "10px",
							}}
						>
							Your application is currently under review. We will notify you by email once a decision has
							been made.
						</Typography>
						<Button
							type="submit"
							fullWidth
							variant="contained"
							sx={{ mt: 2 }}
							onClick={() => {
								setOpenDialog(false);
							}}
						>
							OK
						</Button>
					</Grid>
				</Grid>
			</DialogContent>
		</Dialog>
	);
}
