import {
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alog<PERSON>ontent,
  Divider,
  Fab,
  FormControl,
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  TextField,
  Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import styles from "../../../css/confirmation.module.css"

export default function ConfirmationDialog({ openDialog, setOpenDialog, message }: any) {
  return (
    <Dialog
      open={openDialog}
      fullWidth={true}
      maxWidth={"xs"}
      disableEscapeKeyDown
      onClose={() => {}}
    >
      <DialogContent>
        <Grid
          container
          direction={"column"}
          sx={{ width: "100%" }}
          justifyContent={"center"}
          alignItems={"center"}
          textAlign={"center"}
        >
          <Typography
            sx={{
              fontSize: "16px",
              fontWeight: "bold",
              lineHeight: "2em",
            }}
          >
              Thank You for{" "}
              <span style={{ color: "green" }}>
                Confirming Your Consultation
              </span>
          </Typography>
          <img className={styles.confirmImg} src="/zenith/confirm.png" />
          <Grid
            sx={{ width: "100%", fontSize: "14px", margin: "20px 0px" }}
            textAlign={"start"}
            container
            direction={"column"}
            alignItems={"start"}
          >
            <Typography
              sx={{
                fontSize: "16px",
                fontWeight: "bold",
                lineHeight: "1em",
                paddingBottom:'10px'
              }}
            >
              {message}
            </Typography>
            <Typography
              sx={{
                fontSize: "14px",
                lineHeight: "1em",
              }}
            >
              <i>
                We appreciate the opportunity to assist you on your treatment
                journey. <br />
                At your scheduled time, you will receive an SMS with a link to
                join our consultation. Please ensure you are in a quiet, private
                space for our appointment. <br />
                We look forward to speaking with you soon. <br />
                Best regards, <br />
                Zenith Clinics Team
              </i>
            </Typography>
            <Button type="submit" fullWidth variant="contained" sx={{ mt: 2 }}
            onClick={()=>{
                setOpenDialog(false);
                // window.location.href = import.meta.env.VITE_MAIN_ZENITH_URL ;
                window.location.href = `/patient/review`;
            }}>
              Continue
            </Button>
          </Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
}
