// Quantity Increase Questionnaire Types

export interface QuantityIncreaseFormData {
  // Step 1: Reason for requesting quantity increase
  reasonNotLasting: boolean;
  reasonHigherDoses: boolean;
  reasonTolerance: boolean;
  reasonIncreasedSymptoms: boolean;
  reasonOther: boolean;
  reasonOtherText: string;

  // Step 2: Response to current treatment
  currentEffectiveness: string; // 1-10 scale
  sideEffectsNone: boolean;
  sideEffectsMild: boolean;
  sideEffectsModerate: boolean;
  sideEffectsStrong: boolean;
  sideEffectsDescription: string;
  usageConsistency: string; // 'full-amount' | 'leftover' | 'varies' | 'other'
  usageConsistencyOther: string;

  // Step 3: Health changes
  healthChanges: string; // 'no-changes' | 'yes'
  healthChangesDescription: string;

  // Step 4: Expectations and preferences
  expectations: string; // text field
  concerns: string; // text field
  intendedUsage: string; // radio buttons for how they'll use increased quantity
  intendedUsageOther: string;

  // Step 5: Consent
  consent: string; // 'yes' | 'no'

  // Dynamic fields based on patient's current treatment plan
  selectedStrengths: string[]; // ['22', '29'] - which strengths they want to increase

  // 22% THC specific fields
  thc22Selected: boolean;
  thc22CurrentQuantity: number;
  thc22RequestedQuantity: number;

  // 29% THC specific fields
  thc29Selected: boolean;
  thc29CurrentQuantity: number;
  thc29RequestedQuantity: number;
}

export interface QuantityIncreaseScoringState {
  totalScore: number;
  maxScore: number;
  isEligible: boolean;
  questionScores: Record<string, number>;
}

export interface QuantityIncreaseQuestionnaireStatus {
  completed: boolean;
  score: number;
  isEligible: boolean;
  status: string; // 'submitted' | 'under_review' | 'approved' | 'rejected'
  submittedAt?: string;
  selectedStrengths?: string[];
  strengthRequests?: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }>;
}

export interface QuantityIncreaseSubmissionData {
  questionsAndAnswers: Array<{
    questionKey: string;
    questionText: string;
    answerValue: string | boolean | number;
    answerText: string;
    score: number;
  }>;
  selectedStrengths: string[];
  strengthRequests: Array<{
    strength: string;
    currentQuantity: number;
    requestedQuantity: number;
    increaseAmount: number;
  }>;
  totalScore: number;
  maxScore: number;
  isEligible: boolean;
  submittedAt: string;
}

// Quantity progression constants
export const QUANTITY_LEVELS = [14, 28, 42, 56, 70, 84] as const;
export type QuantityLevel = typeof QUANTITY_LEVELS[number];

// Strength options
export const THC_STRENGTHS = ['22', '29'] as const;
export type ThcStrength = typeof THC_STRENGTHS[number];

// Patient's current quantities and available increases
export interface PatientQuantityStatus {
  thc22: {
    current: number;
    canIncrease: boolean;
    nextLevel: number | null;
    availableLevels: number[];
    maxLevel: number;
  };
  thc29: {
    current: number;
    canIncrease: boolean;
    nextLevel: number | null;
    availableLevels: number[];
    maxLevel: number;
  };
  hasAnyIncreaseOptions: boolean;
}

// Usage consistency options
export const USAGE_CONSISTENCY_OPTIONS = [
  { value: 'full-amount', label: 'Yes – I use my full monthly amount regularly' },
  { value: 'leftover', label: 'No – I sometimes have leftover product' },
  { value: 'varies', label: 'It varies month to month' },
  { value: 'other', label: 'Other (please explain)' }
] as const;

// Intended usage options
export const INTENDED_USAGE_OPTIONS = [
  { value: 'extend-daily', label: 'To extend daily use across more time periods' },
  { value: 'worsening-symptoms', label: 'To manage worsening or more frequent symptoms' },
  { value: 'breakthrough-symptoms', label: 'For occasional flare-ups or breakthrough symptoms' },
  { value: 'gradual-increase', label: 'To gradually increase my dose (within doctor\'s guidance)' },
  { value: 'unsure-advice', label: 'Unsure – I would like advice from my doctor' },
  { value: 'other', label: 'Other (please describe)' }
] as const;

// Health changes options
export const HEALTH_CHANGES_OPTIONS = [
  { value: 'no-changes', label: 'No changes' },
  { value: 'yes', label: 'Yes – please describe' }
] as const;

// Scoring thresholds
export const QUANTITY_INCREASE_SCORING = {
  MAX_SCORE: 50,
  ELIGIBILITY_THRESHOLD: 35, // 70% threshold
  QUESTION_WEIGHTS: {
    reasonForRequest: 8, // Max 8 points for reasons
    currentEffectiveness: 6, // 1-10 scale converted to 0-6 points
    sideEffects: 4, // Penalty for strong side effects
    usageConsistency: 6, // Important for determining need
    healthChanges: 3, // Health stability check
    expectations: 2, // Reasonable expectations
    concerns: 2, // Awareness of risks
    intendedUsage: 4, // Appropriate usage plan
    consent: 5 // Must consent
  }
} as const;
