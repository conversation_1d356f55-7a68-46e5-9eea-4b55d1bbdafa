import { createTheme } from '@mui/material/styles';
import { red } from '@mui/material/colors';

// A custom theme for this app
const harvestTheme = createTheme({
  cssVariables: true,
  palette: {
    primary: {
      main: '#FD6440',
      contrastText: "#FFFFFF"
    },
    secondary: {
      main: '#FFFFFF',
      contrastText: "#FD6440"
    },
    error: {
      main: red.A400,
    },
  },
  typography: {
    "fontFamily": `"Poppins", "Helvetica", "Arial", sans-serif`,
    "fontSize": 14,
    "fontWeightLight": 300,
    "fontWeightRegular": 400,
    "fontWeightMedium": 500,
    h4: {
      lineHeight: 0.8,
      letterSpacing: '0.02em',
      marginBottom: '0.6em !important',
      textTransform: 'uppercase'
    },
    h6: {
      lineHeight: 1,
      letterSpacing: '0.02em',
      textTransform: 'uppercase',
      fontWeight: '700'
    },
  }
});

export default harvestTheme;
