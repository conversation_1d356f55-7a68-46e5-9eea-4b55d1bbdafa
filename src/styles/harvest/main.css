body.harvest {

    background-color: #000000 !important;
    color: #ffffff !important;
    font-family: 'Poppins' Arial, Helvetica, sans-serif;

    a {
        color: #FD6440;
        text-decoration: none;
    }

    .header {
        color: #FD6440;
        background-color: #000000;
        padding: 20px 0;
        text-align: center;
    }

    .footer {
        background-color: #000000;
        padding: 20px 0;
        text-align: center;
        margin-top: 60px;
    }

    .footer a {
        color: #FD6440;
        font-weight: 300;
        font-size: 16px;
        text-decoration: none;
    }


    /* Stepper Styles */

    .MuiStack-root,
    .StepperNextButton {
        padding: 0 !important;
    }

    .MuiMobileStepper-dots {
        align-items: center !important;
    }

    .MuiMobileStepper-dot {
        background-color: #59352C !important;
        height: 5px !important;
        width: 5px !important;
    }

    .MuiMobileStepper-dotActive {
        background-color: #FD6440 !important;
        height: 8px !important;
        width: 8px !important;
    }

    /* Form styles */

    .MuiGrid2-root,
    .MuiBox-root {
        cursor: pointer;
    }

    .CheckBoxLabel {
        background-color: #FD6440;
        margin: 5px 0 !important;
        border-radius: 5px;
        width: 100%;
    }

    .CheckBoxLabel:has(.Mui-checked) {
        background-color: #ffffff;
        color: #FD6440;
    }

    .CheckBoxLabel .MuiFormControlLabel-label {
        width: 80%;
        display: flex;
        flex-direction: row;
        align-content: center;
        justify-content: space-between;
        align-items: center;
        text-align: left;
    }

    .CheckBoxLabel:has(.Mui-checked) img {
        filter: invert(52%) sepia(19%) saturate(5631%) hue-rotate(334deg) brightness(100%) contrast(99%);
    }

    /* Thank you pages styles */
    .thankyouBanner,
    .thankyouBannerMobile {
        height: 70vh;
        display: flex;
        flex-direction: column;
        align-content: center;
        justify-content: flex-end;
        align-items: center;
        background-position: center center;
        background-size: cover;
    }

    .thankyouBannerMobile {
        height: 30vh;
    }

    .thankyouBanner img,
    .thankyouBannerMobile img {
        width: 40%;
    }
}