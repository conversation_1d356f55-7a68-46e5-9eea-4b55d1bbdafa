import { useEffect } from "react";

function MetaPixel(props: any){
  useEffect(() => {
    // Load the Pixel script only if it's not already loaded
    // @ts-ignore
    if (!window.fbq) {
      // @ts-ignore
      !(function (f, b, e, v, n, t, s) {
        // @ts-ignore
        if (f.fbq) return;
        // @ts-ignore
        n = f.fbq = function () {
          // @ts-ignore
          n.callMethod
            ? // @ts-ignore
              n.callMethod.apply(n, arguments)
            : // @ts-ignore
              n.queue.push(arguments);
        };
        // @ts-ignore
        if (!f._fbq) f._fbq = n;
        // @ts-ignore
        n.push = n;
        // @ts-ignore
        n.loaded = !0;
        // @ts-ignore
        n.version = "2.0";
        // @ts-ignore
        n.queue = [];
        // @ts-ignore
        t = b.createElement(e);
        // @ts-ignore
        t.async = !0;
        // @ts-ignore
        t.src = v;
        // @ts-ignore
        s = b.getElementsByTagName(e)[0];
        // @ts-ignore
        s.parentNode.insertBefore(t, s);
      })(
        window,
        document,
        "script",
        "https://connect.facebook.net/en_US/fbevents.js"
      );
      // @ts-ignore
      fbq("init", "2897882420379934");
      // @ts-ignore
      fbq("init", "1107806040959291");
      // @ts-ignore
      fbq("track", "PageView");
    }
  },[]);
  return <></>;
};

export default MetaPixel;