import { useEffect } from "react";

const AnyTrackPixel = () => {
  useEffect(() => {
    // @ts-ignore
    window.AnyTrack = window.AnyTrack || function() {
      // @ts-ignore
      (window.AnyTrack.q = window.AnyTrack.q || []).push(arguments);
    };

    if (document.querySelector('script[src="https://assets.anytrack.io/XBWJdwVSqHnR.js"]')) {
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://assets.anytrack.io/XBWJdwVSqHnR.js';
    script.async = true;

    document.head.appendChild(script);

  }, []);

  return null;
};

export default AnyTrackPixel;