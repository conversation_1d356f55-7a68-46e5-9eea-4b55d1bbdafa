import {
  createContext,
  Dispatch,
  JS<PERSON>,
  SetStateAction,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import LoadingScreen from "../utils/loading-screen";
import axios from "axios";
import { useLocation, useNavigate } from "@tanstack/react-location";
import { enqueueSnackbar } from "notistack";
import { AuthUser } from "../types";
import axiosInstance from "../services/axios";
import UserSteps from "../types/enum";

type AuthContextValues = {
  authenticated: boolean;
  setUser: (user: AuthUser | null) => void;
  user: AuthUser | null;
  loginWithLeadId: (leadId: string) => Promise<void>;
  logout: () => void;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthContext = createContext<AuthContextValues | null>(null);

export const AuthProvider = ({ children }: AuthProviderProps): JSX.Element => {
  // Initialize state from localStorage if available
  const initialUser = localStorage.getItem('zenith_auth_user')
    ? JSON.parse(localStorage.getItem('zenith_auth_user') || '{}') as AuthUser
    : null;
  const initialAuth = localStorage.getItem('zenith_authenticated') === 'true';

  const [authenticated, setAuthenticated] = useState(initialAuth);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<AuthUser | null>(initialUser);
  const navigate = useNavigate();
  const location = useLocation();

  // Define public pages that require authentication validation
  const publicPages = [
    "/patient/questionnaire",
    "/patient/phone-verification",
    "/patient/discharge-letter",
    "/patient/consult-fee",
  ];

  // Only the consent page requires our new authentication mechanism
  const consentPage = "/patient/consent";

  // Original authentication logic will be used for all other pages

  // Custom setUser function that also updates localStorage - used only for consent page
  const setUserWithStorage = useCallback((newUser: AuthUser | null) => {
    // Update React state
    setUser(newUser);
    setAuthenticated(!!newUser);

    // Update localStorage for consent page
    if (newUser) {
      localStorage.setItem('zenith_auth_user', JSON.stringify(newUser));
      localStorage.setItem('zenith_authenticated', 'true');
    } else {
      localStorage.removeItem('zenith_auth_user');
      localStorage.removeItem('zenith_authenticated');
    }

  }, []);

  // Logout function - primarily for consent page
  const logout = useCallback(() => {


    // Clear localStorage for consent page
    localStorage.removeItem('zenith_auth_user');
    localStorage.removeItem('zenith_authenticated');

    // Update state
    setUser(null);
    setAuthenticated(false);

    // Redirect to login page
    navigate({ to: "/patient/login" });
  }, [navigate]);

  // Login with lead ID - combines original behavior with localStorage for consent page
  const loginWithLeadId = useCallback(async (leadID: string) => {

    try {
      const result = await axios.post(
        `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/login`,
        { leadID },
        { withCredentials: true }
      );

      if (result.data?.authenticated) {
        // Update state
        setAuthenticated(true);
        setUser(result.data?.user as AuthUser);

        // Also store in localStorage for consent page
        localStorage.setItem('zenith_auth_user', JSON.stringify(result.data?.user));
        localStorage.setItem('zenith_authenticated', 'true');

        // Navigate to phone verification
        navigate({ to: "/patient/phone-verification" });
      } else {

        setAuthenticated(false);
        setUser(null);

        // Clear localStorage
        localStorage.removeItem('zenith_auth_user');
        localStorage.removeItem('zenith_authenticated');
      }
    } catch (error) {

      setAuthenticated(false);
      setUser(null);

      // Clear localStorage on error
      localStorage.removeItem('zenith_auth_user');
      localStorage.removeItem('zenith_authenticated');
    }
  }, [navigate]);

  // Initialization effect with validation for public pages
  useEffect(() => {
    const init = async () => {
      try {
        // Consent page no longer requires authentication - skip validation
        if (location.current.pathname === consentPage) {
          setIsLoading(false);
          return;
        } else {
          // Special case for login page - don't redirect
          if (location.current.pathname === "/patient/login") {
            setIsLoading(false);
            return;
          }

          // For public pages, validate authentication
          if (publicPages.includes(location.current.pathname)) {
            const result = await axiosInstance.get(
              `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/validate`,
              {
                withCredentials: true,
              }
            );

            if (!result.data?.authenticated) {
              navigate({ to: "/patient/register" });
              setAuthenticated(false);
            } else {
              setUser(result.data?.user as AuthUser);
              setAuthenticated(true);

              // Also update localStorage for consent page compatibility
              localStorage.setItem(
                "zenith_auth_user",
                JSON.stringify(result.data?.user)
              );
              localStorage.setItem("zenith_authenticated", "true");

              // Navigate to phone verification if not already there
              if (location.current.pathname !== "/patient/phone-verification") {
                navigate({ to: "/patient/phone-verification" });
              }
            }
          }

          // For all other pages, just set loading to false
          setIsLoading(false);
        }
      } catch (e) {

        setIsLoading(false);

        // Consent page no longer requires authentication - skip error handling
        if (location.current.pathname === consentPage) {
          // No redirection needed
          return;
        }
        // Redirect to register on error for public pages
        else if (publicPages.includes(location.current.pathname)) {
          navigate({ to: "/patient/register" });
        }
      }
    };
    init();
  }, []);

  // Special authentication validation effect for consent page - DISABLED TO PREVENT LOOPS
  // This functionality is now handled directly in the FormConsent component

  return (
    <AuthContext.Provider
      value={{
        authenticated,
        loginWithLeadId,
        user,
        setUser: setUserWithStorage,
        logout
      }}
    >
      {isLoading ? <LoadingScreen /> : children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
