.consentContainer {
  width: 100%;
  margin: 0 auto;
  background-color: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
  .consentContainer {
    border-radius: 0;
    border: none;
  }
}

@media (min-width: 768px) {
  .consentContainer {
    max-width: 400px;
  }
}

.header {
  background-color: #007F00;
  color: white;
  padding: 15px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  border-radius: 0;
}

@media (max-width: 767px) {
  .header {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

@media (min-width: 768px) {
  .header {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}

.content {
  padding: 15px;
  flex-grow: 1;
  background-color: white;
}

@media (max-width: 767px) {
  .content {
    padding: 15px 10px;
  }
}

.title {
  color: #007F00;
  font-weight: bold;
  text-align: center;
  margin-bottom: 5px;
  font-size: 24px;
  margin-top: 0;
}

.subtitle {
  text-align: center;
  margin-bottom: 15px;
  font-size: 12px;
  color: #333;
}

.checkboxLabel {
  font-size: 12px;
  margin-bottom: 0;
  display: flex;
  align-items: flex-start;
  text-align: center;
}

.checkboxGroup {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
  gap: 30px;
}

.checkboxOption {
  margin: 0;
  font-size: 12px;
}

.signatureSection {
  margin-top: 0;
  padding: 20px 15px;
  background-color: white;
  color: #333;
  text-align: left;
}

@media (max-width: 767px) {
  .signatureSection {
    padding: 15px 10px;
  }
}

.submitButton {
  background-color: #007F00 !important;
  color: white !important;
  font-weight: bold;
  padding: 15px !important;
  margin: 0 !important;
  width: 100%;
  border: none;
  border-radius: 4px !important;
  cursor: pointer;
  text-transform: uppercase;
  font-size: 18px !important;
  letter-spacing: 1px;
  margin-bottom: 0 !important;
}

.submitButton:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.footer {
  text-align: center;
  font-size: 16px;
  color: white;
  padding: 15px 0;
  background-color: #007F00;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  margin-right: calc(-50vw + 50%);
  margin-top: 0;
  border-radius: 0;
}

@media (max-width: 767px) {
  .footer {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }
}

@media (min-width: 768px) {
  .footer {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }
}

.logo {
  width: 24px;
  height: 24px;
  margin-right: 8px;
}

.headerText {
  font-size: 18px;
  font-weight: bold;
}

.signatureTitle {
  color: white;
  font-weight: bold;
  font-size: 20px;
  text-align: center;
  margin-bottom: 15px;
}

.questionText {
  text-align: center;
  font-size: 12px;
  margin-bottom: 5px;
  color: #333;
}

.footerContainer {
  background-color: #007F00;
  padding: 5px;
  margin-top: 0;
  text-align: center;
}

.deviceInfo {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 10px;
  margin-bottom: 10px;
  text-align: center;
}
