import axios, { AxiosError, AxiosResponse } from "axios";
import { enqueueSnackbar } from "notistack";

const axiosInstance = axios.create({
  withCredentials: true,
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:5000/api",
  headers: {
    "Content-Type": "application/json",
    // Ensure cookies are accepted
    "Accept": "application/json"
  },
  responseType: "json",
  // Don't use CSRF protection for this application
  xsrfCookieName: undefined,
  xsrfHeaderName: undefined,
});

// Add request interceptor to log and ensure cookies are sent
axiosInstance.interceptors.request.use(
  (config) => {
    // Only log in development mode or if explicitly enabled
    if (import.meta.env.DEV || import.meta.env.VITE_ENABLE_API_LOGGING === 'true') {
      console.log(`Axios Request to ${config.url}:`, {
        method: config.method?.toUpperCase(),
        withCredentials: config.withCredentials,
        headers: config.headers,
        cookies: document.cookie
      });
    }

    // Always ensure withCredentials is true
    config.withCredentials = true;

    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    // Only log in development mode or if explicitly enabled
    if (import.meta.env.DEV || import.meta.env.VITE_ENABLE_API_LOGGING === 'true') {
      console.log(`Axios Response from ${response.config.url}:`, {
        method: response.config.method?.toUpperCase(),
        status: response.status,
        cookies: document.cookie,
        headers: response.headers
      });
    }
    return response;
  },
  (error: AxiosError) => {
    console.error('Axios Error:', error);

    // Log detailed error information
    if (error.response) {
      console.error('Error Response:', {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers
      });
    } else if (error.request) {
      console.error('Error Request:', error.request);
    } else {
      console.error('Error Message:', error.message);
    }

    const statusCode = error.response?.status;

    // Handle authentication errors
    if (statusCode === 401 || statusCode === 403) {
      // enqueueSnackbar("You need to login", {
      //   variant: "warning",
      // });
    }
    // Handle bad request errors
    else if (statusCode === 400) {
      // Try to extract a meaningful error message
      let errorMessage = "Bad request";

      if (error.response?.data) {
        if (typeof error.response.data === 'string') {
          errorMessage = error.response.data;
        } else if (typeof error.response.data === 'object') {
          // Try to extract message from object
          const data = error.response.data as any;
          errorMessage = data.message || data.error || JSON.stringify(data);
        }
      }

      enqueueSnackbar(errorMessage, {
        variant: "error",
      });
    }
    // Handle server errors
    else if (statusCode && statusCode >= 500) {
      enqueueSnackbar("Server error. Please try again later.", {
        variant: "error",
      });
    }
    // Handle network errors
    else if (!statusCode) {
      enqueueSnackbar("Network error. Please check your connection.", {
        variant: "error",
      });
    }

    return Promise.reject(error);
  }
);

// Helper function to create a request config with auth headers from localStorage
// This is primarily used for the consent page
export const getAuthConfig = () => {
  return {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    withCredentials: true
  };
};

export default axiosInstance;
