import axiosInstance from "./axios";
import { AuthUser } from "../types";

export interface EmailAuthResult {
  success: boolean;
  user?: AuthUser;
  error?: string;
}

/**
 * Authenticates a user using only their email address via the email-only login endpoint
 * This is used for direct authentication when we have user data but need proper tokens
 */
export const authenticateByEmail = async (email: string): Promise<EmailAuthResult> => {
  try {
    // Validate email format
    if (!email || typeof email !== 'string' || !email.includes('@')) {
      return {
        success: false,
        error: "Invalid email format."
      };
    }

    // Call the email-only login endpoint
    const result = await axiosInstance.post(
      `${import.meta.env.VITE_API_URL}/funnel/v1.0/patient/login/email-only`,
      { email },
      {
        withCredentials: true,
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      }
    );

    if (result.data && result.data.authenticated) {
      // Store authentication in localStorage
      localStorage.setItem(
        "zenith_auth_user",
        JSON.stringify(result.data.user)
      );
      localStorage.setItem("zenith_authenticated", "true");

      return {
        success: true,
        user: result.data.user as AuthUser
      };
    } else {
      return {
        success: false,
        error: "Authentication failed. Please check your email or contact support."
      };
    }
  } catch (error: any) {
    console.error("Email-only authentication error:", error);
    
    // Handle specific error cases
    if (error.response?.status === 404) {
      return {
        success: false,
        error: "User not found. Please check your email or contact support."
      };
    } else if (error.response?.status === 401) {
      return {
        success: false,
        error: "Authentication failed. Please contact support."
      };
    } else {
      return {
        success: false,
        error: "Network error. Please check your connection and try again."
      };
    }
  }
};

/**
 * Checks if the current user needs email-only authentication
 * This happens when we have user data in localStorage but API requests are failing due to missing tokens
 */
export const needsEmailAuthentication = (): boolean => {
  const storedUser = localStorage.getItem('zenith_auth_user');
  const storedAuth = localStorage.getItem('zenith_authenticated');
  
  // If we have stored user data but no proper authentication, we might need email auth
  return !!(storedUser && storedAuth === 'true');
};

/**
 * Gets the stored user email for authentication
 */
export const getStoredUserEmail = (): string | null => {
  try {
    const storedUser = localStorage.getItem('zenith_auth_user');
    if (storedUser) {
      const user = JSON.parse(storedUser) as AuthUser;
      return user.email || null;
    }
    return null;
  } catch (error) {
    console.error("Error parsing stored user data:", error);
    return null;
  }
};

/**
 * Attempts to authenticate using stored email if available
 * Returns true if authentication was attempted (regardless of success)
 */
export const tryEmailAuthentication = async (): Promise<EmailAuthResult> => {
  const email = getStoredUserEmail();
  
  if (!email) {
    return {
      success: false,
      error: "No stored email found for authentication."
    };
  }

  return await authenticateByEmail(email);
};
