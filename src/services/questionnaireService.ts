import axiosInstance from './axios';
import { QuestionnaireConfigResponse, QuestionnaireApiResponse, SectionConfig, QuestionConfig } from '../types';

export class QuestionnaireService {
  /**
   * Fetch questionnaire configuration from the backend
   */
  static async getQuestionnaireConfig(type: string, patientId?: string): Promise<QuestionnaireConfigResponse> {
    try {
      const params = patientId ? { patientId } : {};
      const response = await axiosInstance.get(`/funnel/v1.0/questionnaires/${type}/config`, { params });

      // Handle the actual API response structure
      const apiResponse: QuestionnaireApiResponse = response.data;
      if (!apiResponse.success) {
        throw new Error(apiResponse.message || 'Failed to fetch questionnaire config');
      }

      return apiResponse.data;
    } catch (error) {
      console.error(`Failed to fetch questionnaire config for ${type}:`, error);
      throw error;
    }
  }

  /**
   * Submit questionnaire response
   */
  static async submitQuestionnaire(type: string, submissionData: any): Promise<any> {
    try {
      const response = await axiosInstance.post(`/funnel/v1.0/questionnaires/${type}/submit`, submissionData);
      return response.data;
    } catch (error) {
      console.error(`Failed to submit questionnaire ${type}:`, error);
      throw error;
    }
  }

  /**
   * Get questionnaire status for a patient
   */
  static async getQuestionnaireStatus(type: string, patientId: string): Promise<any> {
    try {
      const response = await axiosInstance.get(`/funnel/v1.0/questionnaires/${type}/status`, {
        params: { patientId }
      });
      return response.data;
    } catch (error) {
      console.error(`Failed to get questionnaire status for ${type}:`, error);
      throw error;
    }
  }

  /**
   * Calculate score based on form data and questionnaire config
   */
  static calculateScore(formData: Record<string, any>, config: QuestionnaireConfigResponse): {
    totalScore: number;
    questionScores: Record<string, number>;
  } {
    const questionScores: Record<string, number> = {};
    let totalScore = 0;

    config.questions.forEach(question => {
      if (question.contributesToScore) {
        const value = formData[question.key];
        let score = 0;

        // Handle different question types
        switch (question.type) {
          case 'checkbox':
            score = this.calculateCheckboxScore(question, value);
            break;

          case 'radio':
            score = this.calculateRadioScore(question, value);
            break;

          case 'slider':
            score = this.calculateSliderScore(question, value);
            break;

          case 'text':
            // Text fields typically don't contribute to score unless specified
            score = this.calculateTextScore(question, value);
            break;

          default:
            console.warn(`Unknown question type for scoring: ${question.type}`);
            score = 0;
        }

        questionScores[question.key] = score;
        totalScore += score;
      }
    });

    return { totalScore, questionScores };
  }

  /**
   * Calculate score for checkbox questions
   */
  private static calculateCheckboxScore(question: QuestionConfig, value: any): number {
    if (!question.answerOptions || question.answerOptions.length === 0) {
      return 0;
    }

    // For checkboxes, value should be boolean
    const isChecked = Boolean(value);

    // Checkbox questions typically have one answer option with the score
    const answerOption = question.answerOptions[0];
    return isChecked ? (answerOption?.score || 0) : 0;
  }

  /**
   * Calculate score for radio questions
   */
  private static calculateRadioScore(question: QuestionConfig, value: any): number {
    if (!question.answerOptions || !value) {
      return 0;
    }

    // Find the selected option
    const selectedOption = question.answerOptions.find(option => option.value === value);
    return selectedOption?.score || 0;
  }

  /**
   * Calculate score for slider questions
   */
  private static calculateSliderScore(question: QuestionConfig, value: any): number {
    if (!question.sliderConfig?.scoreMapping) {
      return 0;
    }

    const stringValue = String(value || question.sliderConfig.min || 1);
    return question.sliderConfig.scoreMapping[stringValue] || 0;
  }

  /**
   * Calculate score for text questions
   */
  private static calculateTextScore(question: QuestionConfig, value: any): number {
    // Most text questions don't contribute to score
    // This can be extended if needed for length-based scoring
    return 0;
  }

  /**
   * Check if a question should be shown based on conditional logic
   * For now, all questions are shown since conditional logic isn't in the API yet
   */
  static shouldShowQuestion(question: QuestionConfig, formData: Record<string, any>): boolean {
    // TODO: Implement conditional logic when it's added to the API
    return true;
  }

  /**
   * Group questions by section and convert to step format
   */
  static groupQuestionsBySection(config: QuestionnaireConfigResponse): StepConfig[] {
    const steps: StepConfig[] = [];

    // Sort sections by order_index
    const sortedSections = [...config.sections].sort((a, b) => a.order_index - b.order_index);

    sortedSections.forEach(section => {
      // Get questions for this section, sorted by order
      const sectionQuestions = config.questions
        .filter(q => q.sectionId === section.id)
        .sort((a, b) => a.order - b.order);

      if (sectionQuestions.length > 0) {
        steps.push({
          stepNumber: section.order_index,
          title: section.title,
          questions: sectionQuestions,
          section: section // Include full section info
        });
      }
    });

    return steps;
  }

  /**
   * Validate form data against questionnaire configuration
   */
  static validateFormData(formData: Record<string, any>, config: QuestionnaireConfigResponse): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    const steps = this.groupQuestionsBySection(config);

    steps.forEach(step => {
      // Group questions by type for validation
      const checkboxQuestions = step.questions.filter((q: QuestionConfig) => q.type === 'checkbox');
      const otherQuestions = step.questions.filter((q: QuestionConfig) => q.type !== 'checkbox');

      // For checkbox questions, validate that at least one is selected (if any exist in this step)
      if (checkboxQuestions.length > 0) {
        const hasAnyCheckboxSelected = checkboxQuestions.some((question: QuestionConfig) => {
          const value = formData[question.key];
          return value === true || value === 'true';
        });

        if (!hasAnyCheckboxSelected) {
          errors.push('Please select at least one option');
        }
      }

      // For other question types, validate individually
      otherQuestions.forEach(question => {
        if (this.shouldShowQuestion(question, formData)) {
          const value = formData[question.key];

          // Check if this is a conditional text field that should be shown
          let shouldShow = true;

          if (question.type === 'text') {
            // Handle "Other reason" text field
            if (question.key === 'reasonOtherText' ||
                (question.key.toLowerCase().includes('other') &&
                 (question.text.toLowerCase().includes('describe') || question.text.toLowerCase().includes('other')))) {
              // Check if "Other reason" checkbox is selected (from any step, not just current step)
              const otherCheckboxSelected = formData['reasonOther'] === true || formData['reasonOther'] === 'true';
              shouldShow = otherCheckboxSelected;
            }

            // Handle "Side effects description" text field
            if (question.key === 'sideEffectsDescription' ||
                (question.text.toLowerCase().includes('description') &&
                 question.text.toLowerCase().includes('side effects'))) {
              const hasModerateOrStrong = formData['sideEffectsModerate'] || formData['sideEffectsStrong'];
              shouldShow = hasModerateOrStrong;
            }

            // Handle "Health changes description" text field
            if (question.key === 'healthChangesDescription' ||
                (question.key.toLowerCase().includes('healthchanges') && question.key.toLowerCase().includes('description')) ||
                (question.text.toLowerCase().includes('description') &&
                 question.text.toLowerCase().includes('health changes'))) {
              const healthChangesYes = formData['healthChanges'] === 'yes_please_describe' ||
                                     formData['healthChanges'] === 'yes' ||
                                     formData['healthChanges'] === 'Yes';
              shouldShow = healthChangesYes;
            }
          }

          // Only validate if the field should be shown AND is required according to backend
          let isRequired = true; // Default to required

          // For text fields, check textFieldConfig.required
          if (question.type === 'text' && question.textFieldConfig) {
            isRequired = question.textFieldConfig.required !== false;
          }

          if (shouldShow && isRequired && (value === undefined || value === null || value === '')) {
            errors.push(`${question.text} is required`);
          }
        }
      });
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Convert form data to submission format
   */
  static formatSubmissionData(
    formData: Record<string, any>,
    config: QuestionnaireConfigResponse,
    patientId: string
  ): any {
    const { totalScore, questionScores } = this.calculateScore(formData, config);
    const isEligible = totalScore >= config.threshold;

    const questionsAndAnswers = config.questions
      .filter(question => this.shouldShowQuestion(question, formData))
      .map(question => {
        const value = formData[question.key];
        let answerText = '';

        if (question.type === 'checkbox') {
          // Use the actual question text for better analytics
          answerText = value ? question.text : 'Not selected';
        } else if (question.type === 'radio') {
          const option = question.answerOptions?.find(opt => opt.value === value);
          answerText = option?.label || value;
        } else if (question.type === 'slider') {
          const maxValue = question.sliderConfig?.max || 10;
          answerText = `${value}/${maxValue}`;
        } else {
          answerText = value || '';
        }

        return {
          questionKey: question.key,
          questionText: question.text,
          answerValue: value,
          answerText,
          score: questionScores[question.key] || 0
        };
      });

    return {
      patientId,
      questionnaireId: config.id,
      responses: questionsAndAnswers,
      totalScore,
      isEligible,
      submittedAt: new Date().toISOString()
    };
  }
}

export default QuestionnaireService;
