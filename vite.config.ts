import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  server: {
    /*hmr: { overlay: false },
    // @ts-ignore
    allowedHosts: ["care.zenith.clinic", "funnel.zenith.ad", "localhost"],*/
    port: 5174,
  },
  preview: {
    port: 5174,
    // @ts-ignore
    allowedHosts: ["care.zenith.clinic", "funnel.zenith.ad", "localhost"],
  },
  build: {
    rollupOptions: {
      output: {
        assetFileNames: (assetInfo) => {
          // Keep original names for images to enable better caching
          if (assetInfo.name && /\.(png|jpe?g|svg|gif|webp|ico)$/i.test(assetInfo.name)) {
            return 'assets/[name]-[hash][extname]';
          }
          return 'assets/[name]-[hash][extname]';
        },
      },
    },
    // Enable asset inlining for small images (optional)
    assetsInlineLimit: 4096,
  },
});
